const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class WorkflowDefinition extends Model {
    static associate(models) {
      // 关联到工作流实例
      WorkflowDefinition.hasMany(models.WorkflowInstance, {
        foreignKey: 'workflow_definition_id',
        as: 'instances'
      });

      // 关联到工作流节点
      WorkflowDefinition.hasMany(models.WorkflowNode, {
        foreignKey: 'workflow_definition_id',
        as: 'nodes'
      });

      // 关联到创建者
      WorkflowDefinition.belongsTo(models.Employee, {
        foreignKey: 'created_by',
        as: 'creator'
      });
    }

    // 静态方法：获取工作流定义列表
    static async getDefinitionList(options = {}) {
      const {
        page = 1,
        pageSize = 20,
        category,
        status,
        keyword,
        createdBy
      } = options;

      const where = {};
      
      if (category) {
        where.category = category;
      }
      
      if (status) {
        where.status = status;
      }
      
      if (createdBy) {
        where.created_by = createdBy;
      }
      
      if (keyword) {
        where[sequelize.Sequelize.Op.or] = [
          { name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { description: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
        ];
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.Employee,
            as: 'creator',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: sequelize.models.WorkflowNode,
            as: 'nodes',
            attributes: ['id', 'name', 'type', 'order']
          }
        ],
        limit: pageSize,
        offset,
        order: [['created_at', 'DESC']]
      });

      return {
        definitions: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：获取活跃的工作流定义
    static async getActiveDefinitions(category = null) {
      const where = { status: 'active' };
      if (category) {
        where.category = category;
      }

      return this.findAll({
        where,
        include: [
          {
            model: sequelize.models.WorkflowNode,
            as: 'nodes',
            order: [['order', 'ASC']]
          }
        ],
        order: [['name', 'ASC']]
      });
    }

    // 实例方法：获取起始节点
    async getStartNode() {
      const nodes = await sequelize.models.WorkflowNode.findAll({
        where: {
          workflow_definition_id: this.id,
          type: 'start'
        },
        order: [['order', 'ASC']]
      });
      
      return nodes[0] || null;
    }

    // 实例方法：获取所有节点
    async getNodes() {
      return sequelize.models.WorkflowNode.findAll({
        where: {
          workflow_definition_id: this.id
        },
        order: [['order', 'ASC']]
      });
    }

    // 实例方法：验证工作流定义
    async validate() {
      const nodes = await this.getNodes();
      
      // 检查是否有起始节点
      const startNodes = nodes.filter(node => node.type === 'start');
      if (startNodes.length === 0) {
        throw new Error('工作流必须包含起始节点');
      }
      if (startNodes.length > 1) {
        throw new Error('工作流只能有一个起始节点');
      }

      // 检查是否有结束节点
      const endNodes = nodes.filter(node => node.type === 'end');
      if (endNodes.length === 0) {
        throw new Error('工作流必须包含结束节点');
      }

      // 检查审批节点是否有审批人配置
      const approvalNodes = nodes.filter(node => node.type === 'approval');
      for (const node of approvalNodes) {
        if (!node.config || !node.config.approvers || node.config.approvers.length === 0) {
          throw new Error(`审批节点 ${node.name} 必须配置审批人`);
        }
      }

      return true;
    }

    // 实例方法：激活工作流
    async activate() {
      await this.validate();
      this.status = 'active';
      this.activated_at = new Date();
      return this.save();
    }

    // 实例方法：停用工作流
    async deactivate() {
      this.status = 'inactive';
      this.deactivated_at = new Date();
      return this.save();
    }

    // 实例方法：复制工作流定义
    async clone(newName, createdBy) {
      const nodes = await this.getNodes();
      
      const newDefinition = await WorkflowDefinition.create({
        name: newName,
        description: `${this.description} (副本)`,
        category: this.category,
        version: '1.0',
        status: 'draft',
        created_by: createdBy
      });

      // 复制节点
      for (const node of nodes) {
        await sequelize.models.WorkflowNode.create({
          workflow_definition_id: newDefinition.id,
          name: node.name,
          type: node.type,
          order: node.order,
          config: node.config,
          conditions: node.conditions
        });
      }

      return newDefinition;
    }

    // 静态方法：获取工作流使用统计
    static async getUsageStats(definitionId, startDate, endDate) {
      const where = {
        workflow_definition_id: definitionId
      };

      if (startDate && endDate) {
        where.created_at = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }

      const instances = await sequelize.models.WorkflowInstance.findAll({
        where,
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const stats = {
        total: 0,
        running: 0,
        completed: 0,
        rejected: 0,
        cancelled: 0
      };

      instances.forEach(instance => {
        stats[instance.status] = parseInt(instance.count);
        stats.total += parseInt(instance.count);
      });

      return stats;
    }
  }

  WorkflowDefinition.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '工作流名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '工作流描述'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '工作流分类'
    },
    version: {
      type: DataTypes.STRING(20),
      defaultValue: '1.0',
      comment: '版本号'
    },
    status: {
      type: DataTypes.ENUM('draft', 'active', 'inactive', 'archived'),
      defaultValue: 'draft',
      comment: '状态'
    },
    form_config: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '表单配置'
    },
    settings: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '工作流设置'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '创建人ID'
    },
    activated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '激活时间'
    },
    deactivated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '停用时间'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '标签'
    }
  }, {
    sequelize,
    modelName: 'WorkflowDefinition',
    tableName: 'workflow_definitions',
    timestamps: true,
    paranoid: true,
    comment: '工作流定义表',
    indexes: [
      {
        fields: ['category', 'status']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_by']
      }
    ]
  });

  return WorkflowDefinition;
}; 