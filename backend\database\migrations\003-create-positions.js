'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('positions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true
      },
      department_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '职位级别，数字越大级别越高'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      responsibilities: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '职责描述'
      },
      requirements: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '任职要求'
      },
      salary_range: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '薪资范围'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('positions', ['code']);
    await queryInterface.addIndex('positions', ['department_id']);
    await queryInterface.addIndex('positions', ['level']);
    await queryInterface.addIndex('positions', ['is_active']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('positions');
  }
}; 