'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('workflow_instances', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_definition_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_definitions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false,
        comment: '流程标题'
      },
      request_number: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '申请单号'
      },
      initiator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
        comment: '发起人ID'
      },
      related_table: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '关联业务表名'
      },
      related_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '关联业务记录ID'
      },
      form_data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '表单数据JSON'
      },
      current_step: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '当前步骤序号'
      },
      status: {
        type: Sequelize.ENUM('pending', 'in_progress', 'approved', 'rejected', 'cancelled', 'withdrawn'),
        defaultValue: 'pending',
        allowNull: false
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        defaultValue: 'medium',
        allowNull: false
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '流程开始时间'
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '流程完成时间'
      },
      deadline: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '期限时间'
      },
      attachments: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '附件JSON数组'
      },
      process_variables: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '流程变量JSON，用于条件判断等'
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '备注信息'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_instances', ['workflow_definition_id']);
    await queryInterface.addIndex('workflow_instances', ['request_number']);
    await queryInterface.addIndex('workflow_instances', ['initiator_id']);
    await queryInterface.addIndex('workflow_instances', ['status']);
    await queryInterface.addIndex('workflow_instances', ['priority']);
    await queryInterface.addIndex('workflow_instances', ['started_at']);
    await queryInterface.addIndex('workflow_instances', ['completed_at']);
    await queryInterface.addIndex('workflow_instances', ['deadline']);
    await queryInterface.addIndex('workflow_instances', ['related_table', 'related_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('workflow_instances');
  }
}; 