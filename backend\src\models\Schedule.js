const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Schedule = sequelize.define('Schedule', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '日程标题'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '日程描述'
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '开始时间'
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '结束时间'
    },
    location: {
      type: DataTypes.STRING(255),
      comment: '地点'
    },
    type: {
      type: DataTypes.ENUM('personal', 'work', 'meeting', 'reminder', 'holiday'),
      defaultValue: 'personal',
      comment: '日程类型'
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium',
      comment: '优先级'
    },
    status: {
      type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'cancelled'),
      defaultValue: 'pending',
      comment: '状态'
    },
    is_all_day: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否全天'
    },
    is_recurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否重复'
    },
    recurrence_pattern: {
      type: DataTypes.TEXT,
      comment: '重复模式(JSON格式)'
    },
    reminder_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 15,
      comment: '提前提醒分钟数'
    },
    color: {
      type: DataTypes.STRING(7),
      defaultValue: '#409eff',
      comment: '日程颜色'
    },
    tags: {
      type: DataTypes.TEXT,
      comment: '标签(JSON数组)'
    },
    attendees: {
      type: DataTypes.TEXT,
      comment: '参与者(JSON数组)'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'schedules',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '日程安排表'
  });

  Schedule.associate = (models) => {
    // 关联用户
    Schedule.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });

    // 关联提醒
    Schedule.hasMany(models.Reminder, {
      foreignKey: 'schedule_id',
      as: 'reminders'
    });
  };

  return Schedule;
};
