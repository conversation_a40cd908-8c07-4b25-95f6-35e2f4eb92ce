const express = require('express');
const router = express.Router();
const attendanceController = require('../controllers/attendanceController');
const { authenticateToken, requireRoles } = require('../middleware/auth');
const { validateAttendance, validateLeaveRequest } = require('../validators/attendanceValidator');

// 考勤打卡相关路由
router.post('/clock-in', authenticateToken, validateAttendance.clockIn, attendanceController.clockIn);
router.post('/clock-out', authenticateToken, validateAttendance.clockOut, attendanceController.clockOut);

// 考勤记录查询
router.get('/records', authenticateToken, attendanceController.getAttendanceRecords);
router.get('/today-status', authenticateToken, attendanceController.getTodayStatus);
router.get('/stats', authenticateToken, attendanceController.getAttendanceStats);

// 部门考勤汇总（管理员功能）
router.get('/department-stats', authenticateToken, requireRoles(['ADMIN', 'SUPER_ADMIN']), attendanceController.getDepartmentStats);

// 请假申请相关路由
router.post('/leave/request', authenticateToken, validateLeaveRequest.submit, attendanceController.submitLeaveRequest);
router.get('/leave/requests', authenticateToken, attendanceController.getLeaveRequests);
router.get('/leave/pending', authenticateToken, attendanceController.getPendingLeaveRequests);
router.get('/leave/requests/:id', authenticateToken, attendanceController.getLeaveRequestDetail);
router.patch('/leave/requests/:id/approve', authenticateToken, validateLeaveRequest.approve, attendanceController.approveLeaveRequest);
router.patch('/leave/requests/:id/cancel', authenticateToken, attendanceController.cancelLeaveRequest);

// 请假统计
router.get('/leave/stats', authenticateToken, attendanceController.getLeaveStats);
router.get('/leave/balance', authenticateToken, attendanceController.getLeaveBalance);
router.get('/leave/department-stats', authenticateToken, requireRoles(['ADMIN', 'SUPER_ADMIN']), attendanceController.getDepartmentLeaveStats);

module.exports = router; 