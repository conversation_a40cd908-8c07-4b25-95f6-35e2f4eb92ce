{"version": 3, "names": ["_taggedTemplateLiteralLoose", "strings", "raw", "slice"], "sources": ["../../src/helpers/taggedTemplateLiteralLoose.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _taggedTemplateLiteralLoose(\n  strings: readonly string[],\n  raw?: readonly string[],\n): TemplateStringsArray {\n  if (!raw) {\n    raw = strings.slice(0);\n  }\n  // Loose: TemplateStringsArray['raw'] is readonly, so we have to cast it to any before assigning\n  (strings as any).raw = raw;\n  return strings as TemplateStringsArray;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,2BAA2BA,CACjDC,OAA0B,EAC1BC,GAAuB,EACD;EACtB,IAAI,CAACA,GAAG,EAAE;IACRA,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;EACxB;EAECF,OAAO,CAASC,GAAG,GAAGA,GAAG;EAC1B,OAAOD,OAAO;AAChB", "ignoreList": []}