'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('meetings', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false,
        comment: '会议主题'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '会议描述'
      },
      organizer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
        comment: '组织者ID'
      },
      meeting_room_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'meeting_rooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '会议室ID'
      },
      start_time: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '开始时间'
      },
      end_time: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '结束时间'
      },
      meeting_type: {
        type: Sequelize.ENUM('face_to_face', 'online', 'hybrid'),
        allowNull: false,
        defaultValue: 'face_to_face',
        comment: '会议类型'
      },
      meeting_url: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: '在线会议链接'
      },
      meeting_password: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '会议密码'
      },
      status: {
        type: Sequelize.ENUM('scheduled', 'in_progress', 'completed', 'cancelled'),
        defaultValue: 'scheduled',
        allowNull: false
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        defaultValue: 'medium',
        allowNull: false
      },
      agenda: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '会议议程'
      },
      minutes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '会议纪要'
      },
      attachments: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '会议附件JSON数组'
      },
      reminder_minutes: {
        type: Sequelize.INTEGER,
        defaultValue: 15,
        comment: '提前提醒分钟数'
      },
      is_recurring: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: '是否为重复会议'
      },
      recurring_rule: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '重复规则JSON配置'
      },
      parent_meeting_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'meetings',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '父会议ID，用于重复会议'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('meetings', ['organizer_id']);
    await queryInterface.addIndex('meetings', ['meeting_room_id']);
    await queryInterface.addIndex('meetings', ['start_time']);
    await queryInterface.addIndex('meetings', ['end_time']);
    await queryInterface.addIndex('meetings', ['status']);
    await queryInterface.addIndex('meetings', ['meeting_type']);
    await queryInterface.addIndex('meetings', ['priority']);
    await queryInterface.addIndex('meetings', ['parent_meeting_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('meetings');
  }
}; 