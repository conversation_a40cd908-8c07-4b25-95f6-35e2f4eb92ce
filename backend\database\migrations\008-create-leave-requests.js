'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('leave_requests', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      employee_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      leave_type: {
        type: Sequelize.ENUM('annual', 'sick', 'personal', 'maternity', 'paternity', 'bereavement', 'compensatory', 'other'),
        allowNull: false,
        comment: '请假类型'
      },
      start_date: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '开始时间'
      },
      end_date: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '结束时间'
      },
      total_days: {
        type: Sequelize.DECIMAL(4, 1),
        allowNull: false,
        comment: '请假天数'
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '请假原因'
      },
      status: {
        type: Sequelize.ENUM('pending', 'approved', 'rejected', 'cancelled'),
        defaultValue: 'pending',
        allowNull: false
      },
      submitted_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '提交时间'
      },
      approved_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '审批人ID'
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '审批时间'
      },
      approval_comments: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '审批意见'
      },
      attachments: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '附件信息JSON数组'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('leave_requests', ['employee_id']);
    await queryInterface.addIndex('leave_requests', ['leave_type']);
    await queryInterface.addIndex('leave_requests', ['status']);
    await queryInterface.addIndex('leave_requests', ['start_date']);
    await queryInterface.addIndex('leave_requests', ['end_date']);
    await queryInterface.addIndex('leave_requests', ['approved_by']);
    await queryInterface.addIndex('leave_requests', ['submitted_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('leave_requests');
  }
}; 