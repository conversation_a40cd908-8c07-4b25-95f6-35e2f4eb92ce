const express = require('express');
const authController = require('../controllers/authController');
const { 
  registerValidation, 
  loginValidation, 
  changePasswordValidation,
  requestPasswordResetValidation,
  resetPasswordValidation,
  updateProfileValidation,
  verifyEmailValidation
} = require('../validators/authValidator');
const { 
  authenticateToken, 
  optionalAuth 
} = require('../middleware/auth');

const router = express.Router();

/**
 * @route   POST /api/v1/auth/register
 * @desc    用户注册
 * @access  Public
 */
router.post('/register', registerValidation, authController.register);

/**
 * @route   POST /api/v1/auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post('/login', loginValidation, authController.login);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    刷新访问令牌
 * @access  Public
 */
router.post('/refresh', authController.refreshToken);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post('/logout', optionalAuth, authController.logout);

/**
 * @route   POST /api/v1/auth/change-password
 * @desc    更改密码
 * @access  Private
 */
router.post('/change-password', 
  authenticateToken, 
  changePasswordValidation, 
  authController.changePassword
);

/**
 * @route   POST /api/v1/auth/forgot-password
 * @desc    请求重置密码
 * @access  Public
 */
router.post('/forgot-password', 
  requestPasswordResetValidation, 
  authController.requestPasswordReset
);

/**
 * @route   POST /api/v1/auth/reset-password
 * @desc    重置密码
 * @access  Public
 */
router.post('/reset-password', 
  resetPasswordValidation, 
  authController.resetPassword
);

/**
 * @route   GET /api/v1/auth/profile
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get('/profile', authenticateToken, authController.getProfile);

/**
 * @route   PUT /api/v1/auth/profile
 * @desc    更新用户信息
 * @access  Private
 */
router.put('/profile', 
  authenticateToken, 
  updateProfileValidation, 
  authController.updateProfile
);

/**
 * @route   GET /api/v1/auth/verify-email/:token
 * @desc    验证邮箱
 * @access  Public
 */
router.get('/verify-email/:token', 
  verifyEmailValidation, 
  authController.verifyEmail
);

/**
 * @route   GET /api/v1/auth/check
 * @desc    检查认证状态
 * @access  Private
 */
router.get('/check', authenticateToken, authController.checkAuth);

module.exports = router; 