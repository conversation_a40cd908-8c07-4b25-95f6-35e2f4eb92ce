const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SupplyRequest = sequelize.define('SupplyRequest', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    request_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '申请单号'
    },
    requester_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '申请人ID'
    },
    supply_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'office_supplies',
        key: 'id'
      },
      comment: '用品ID'
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '申请数量'
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      comment: '单价'
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      comment: '总金额'
    },
    purpose: {
      type: DataTypes.TEXT,
      comment: '申请用途'
    },
    urgency: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium',
      comment: '紧急程度'
    },
    expected_date: {
      type: DataTypes.DATE,
      comment: '期望到货日期'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'purchased', 'received', 'cancelled'),
      defaultValue: 'pending',
      comment: '申请状态'
    },
    approver_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '审批人ID'
    },
    approved_at: {
      type: DataTypes.DATE,
      comment: '审批时间'
    },
    approval_notes: {
      type: DataTypes.TEXT,
      comment: '审批备注'
    },
    purchaser_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '采购人ID'
    },
    purchased_at: {
      type: DataTypes.DATE,
      comment: '采购时间'
    },
    purchase_notes: {
      type: DataTypes.TEXT,
      comment: '采购备注'
    },
    received_at: {
      type: DataTypes.DATE,
      comment: '收货时间'
    },
    received_quantity: {
      type: DataTypes.INTEGER,
      comment: '实际收货数量'
    },
    receiver_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '收货人ID'
    },
    receive_notes: {
      type: DataTypes.TEXT,
      comment: '收货备注'
    },
    attachments: {
      type: DataTypes.TEXT,
      comment: '附件(JSON数组)'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'supply_requests',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '办公用品申请表'
  });

  SupplyRequest.associate = (models) => {
    // 关联申请人
    SupplyRequest.belongsTo(models.User, {
      foreignKey: 'requester_id',
      as: 'requester'
    });

    // 关联办公用品
    SupplyRequest.belongsTo(models.OfficeSupply, {
      foreignKey: 'supply_id',
      as: 'supply'
    });

    // 关联审批人
    SupplyRequest.belongsTo(models.User, {
      foreignKey: 'approver_id',
      as: 'approver'
    });

    // 关联采购人
    SupplyRequest.belongsTo(models.User, {
      foreignKey: 'purchaser_id',
      as: 'purchaser'
    });

    // 关联收货人
    SupplyRequest.belongsTo(models.User, {
      foreignKey: 'receiver_id',
      as: 'receiver'
    });
  };

  return SupplyRequest;
};
