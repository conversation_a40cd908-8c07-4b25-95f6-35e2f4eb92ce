/**
 * API 工具模块
 * 统一的API请求封装
 */

import axios from './axios'

// 基础API类
class BaseAPI {
  constructor(baseURL = '') {
    this.baseURL = baseURL
  }

  // GET请求
  get(url, params = {}, config = {}) {
    return axios.get(this.baseURL + url, { params, ...config })
  }

  // POST请求
  post(url, data = {}, config = {}) {
    return axios.post(this.baseURL + url, data, config)
  }

  // PUT请求
  put(url, data = {}, config = {}) {
    return axios.put(this.baseURL + url, data, config)
  }

  // PATCH请求
  patch(url, data = {}, config = {}) {
    return axios.patch(this.baseURL + url, data, config)
  }

  // DELETE请求
  delete(url, config = {}) {
    return axios.delete(this.baseURL + url, config)
  }
}

// 认证相关API
export const authAPI = new BaseAPI('/v1/auth')

// 员工相关API
export const employeeAPI = new BaseAPI('/v1/employees')

// 考勤相关API
export const attendanceAPI = new BaseAPI('/v1/attendance')

// 会议相关API
export const meetingAPI = new BaseAPI('/v1/meetings')

// 文件相关API
export const fileAPI = new BaseAPI('/v1/files')

// 工作流相关API
export const workflowAPI = new BaseAPI('/v1/workflow')

// 部门相关API
export const departmentAPI = new BaseAPI('/v1/departments')

// 统一API对象
const api = {
  // 认证
  auth: {
    login: (data) => authAPI.post('/login', data),
    logout: () => authAPI.post('/logout'),
    refresh: () => authAPI.post('/refresh'),
    profile: () => authAPI.get('/profile'),
    updateProfile: (data) => authAPI.put('/profile', data),
    changePassword: (data) => authAPI.post('/change-password', data)
  },

  // 员工管理
  employee: {
    list: (params) => employeeAPI.get('', params),
    detail: (id) => employeeAPI.get(`/${id}`),
    create: (data) => employeeAPI.post('', data),
    update: (id, data) => employeeAPI.put(`/${id}`, data),
    delete: (id) => employeeAPI.delete(`/${id}`),
    transfer: (id, data) => employeeAPI.post(`/${id}/transfer`, data),
    export: (params) => employeeAPI.get('/export', params)
  },

  // 考勤管理
  attendance: {
    list: (params) => attendanceAPI.get('', params),
    clock: (data) => attendanceAPI.post('/clock', data),
    records: (params) => attendanceAPI.get('/records', params),
    statistics: (params) => attendanceAPI.get('/statistics', params),
    leave: (data) => attendanceAPI.post('/leave', data),
    leaveList: (params) => attendanceAPI.get('/leave', params),
    approveLeave: (id, data) => attendanceAPI.post(`/leave/${id}/approve`, data)
  },

  // 会议管理
  meeting: {
    list: (params) => meetingAPI.get('', params),
    detail: (id) => meetingAPI.get(`/${id}`),
    create: (data) => meetingAPI.post('', data),
    update: (id, data) => meetingAPI.put(`/${id}`, data),
    delete: (id) => meetingAPI.delete(`/${id}`),
    join: (id) => meetingAPI.post(`/${id}/join`),
    leave: (id) => meetingAPI.post(`/${id}/leave`),
    rooms: (params) => meetingAPI.get('/rooms', params)
  },

  // 文件管理
  file: {
    list: (params) => fileAPI.get('', params),
    upload: (formData) => fileAPI.post('/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
    download: (id) => fileAPI.get(`/${id}/download`),
    delete: (id) => fileAPI.delete(`/${id}`),
    share: (id, data) => fileAPI.post(`/${id}/share`, data),
    sharedList: (params) => fileAPI.get('/shared', params)
  },

  // 工作流管理
  workflow: {
    definitions: (params) => workflowAPI.get('/definitions', params),
    createDefinition: (data) => workflowAPI.post('/definitions', data),
    updateDefinition: (id, data) => workflowAPI.put(`/definitions/${id}`, data),
    deleteDefinition: (id) => workflowAPI.delete(`/definitions/${id}`),
    instances: (params) => workflowAPI.get('/instances', params),
    createInstance: (data) => workflowAPI.post('/instances', data),
    approveTask: (taskId, data) => workflowAPI.post(`/tasks/${taskId}/approve`, data),
    rejectTask: (taskId, data) => workflowAPI.post(`/tasks/${taskId}/reject`, data)
  },

  // 部门管理
  department: {
    list: (params) => departmentAPI.get('', params),
    tree: () => departmentAPI.get('/tree'),
    create: (data) => departmentAPI.post('', data),
    update: (id, data) => departmentAPI.put(`/${id}`, data),
    delete: (id) => departmentAPI.delete(`/${id}`)
  },

  // 系统管理
  system: {
    settings: () => axios.get('/v1/system/settings'),
    updateSettings: (data) => axios.put('/v1/system/settings', data),
    logs: (params) => axios.get('/v1/system/logs', { params }),
    backup: () => axios.post('/v1/system/backup'),
    restore: (data) => axios.post('/v1/system/restore', data)
  }
}

// 默认导出
export default api

// 导出基础API类供扩展使用
export { BaseAPI }

// 导出axios实例供直接使用
export { default as axios } from './axios'
