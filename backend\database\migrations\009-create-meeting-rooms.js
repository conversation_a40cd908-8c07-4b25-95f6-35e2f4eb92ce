'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('meeting_rooms', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '会议室名称'
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true,
        comment: '会议室编号'
      },
      location: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '会议室位置'
      },
      capacity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 10,
        comment: '容纳人数'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '会议室描述'
      },
      facilities: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '设施设备JSON数组，如投影仪、白板等'
      },
      booking_rules: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '预订规则JSON配置'
      },
      hourly_rate: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: true,
        comment: '小时收费标准'
      },
      contact_person: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '联系人'
      },
      contact_phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '联系电话'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('meeting_rooms', ['code']);
    await queryInterface.addIndex('meeting_rooms', ['location']);
    await queryInterface.addIndex('meeting_rooms', ['capacity']);
    await queryInterface.addIndex('meeting_rooms', ['is_active']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('meeting_rooms');
  }
}; 