import request from '@/utils/request';

export const workflowAPI = {
  // 工作流定义相关
  getDefinitions(params) {
    return request({
      url: '/v1/workflow/definitions',
      method: 'get',
      params
    });
  },

  getDefinitionById(definitionId) {
    return request({
      url: `/v1/workflow/definitions/${definitionId}`,
      method: 'get'
    });
  },

  createDefinition(data) {
    return request({
      url: '/v1/workflow/definitions',
      method: 'post',
      data
    });
  },

  updateDefinition(definitionId, data) {
    return request({
      url: `/v1/workflow/definitions/${definitionId}`,
      method: 'put',
      data
    });
  },

  activateDefinition(definitionId) {
    return request({
      url: `/v1/workflow/definitions/${definitionId}/activate`,
      method: 'post'
    });
  },

  deactivateDefinition(definitionId) {
    return request({
      url: `/v1/workflow/definitions/${definitionId}/deactivate`,
      method: 'post'
    });
  },

  cloneDefinition(definitionId, data) {
    return request({
      url: `/workflow/definitions/${definitionId}/clone`,
      method: 'post',
      data
    });
  },

  validateDefinition(definitionId) {
    return request({
      url: `/workflow/definitions/${definitionId}/validate`,
      method: 'post'
    });
  },

  getActiveDefinitions(category) {
    return request({
      url: '/workflow/definitions/active',
      method: 'get',
      params: { category }
    });
  },

  getUsageStats(definitionId, startDate, endDate) {
    return request({
      url: `/workflow/definitions/${definitionId}/usage-stats`,
      method: 'get',
      params: { startDate, endDate }
    });
  },

  // 工作流实例相关
  startInstance(data) {
    return request({
      url: '/workflow/instances',
      method: 'post',
      data
    });
  },

  getInstances(params) {
    return request({
      url: '/workflow/instances',
      method: 'get',
      params
    });
  },

  getMyInstances(params) {
    return request({
      url: '/workflow/instances/my',
      method: 'get',
      params
    });
  },

  getInstanceById(instanceId) {
    return request({
      url: `/workflow/instances/${instanceId}`,
      method: 'get'
    });
  },

  cancelInstance(instanceId, data) {
    return request({
      url: `/workflow/instances/${instanceId}/cancel`,
      method: 'put',
      data
    });
  },

  // 审批任务相关
  getPendingTasks(params) {
    return request({
      url: '/workflow/approval-tasks',
      method: 'get',
      params
    });
  },

  getTaskById(taskId) {
    return request({
      url: `/workflow/approval-tasks/${taskId}`,
      method: 'get'
    });
  },

  processTask(taskId, data) {
    return request({
      url: `/workflow/approval-tasks/${taskId}/process`,
      method: 'post',
      data
    });
  },

  // 统计数据
  getStatistics(params) {
    return request({
      url: '/workflow/statistics',
      method: 'get',
      params
    });
  },

  // 集成相关
  integrateWithLeaveRequest(data) {
    return request({
      url: '/workflow/integrations/leave-request',
      method: 'post',
      data
    });
  }
}; 