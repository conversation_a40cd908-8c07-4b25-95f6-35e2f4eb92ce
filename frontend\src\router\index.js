import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  trickleSpeed: 200
})

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false
    }
  },
  {
    path: '/server-error',
    name: 'ServerError',
    component: () => import('@/views/ServerError.vue'),
    meta: {
      title: '服务器错误',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { 
          title: '仪表盘',
          requiresAuth: true 
        }
      },
      {
        path: 'employees',
        name: 'Employees',
        component: () => import('@/views/employee/EmployeeList.vue'),
        meta: { 
          title: '员工管理',
          requiresAuth: true 
        }
      },
      {
        path: 'attendance',
        name: 'Attendance',
        component: () => import('@/views/attendance/AttendanceList.vue'),
        meta: { 
          title: '考勤管理',
          requiresAuth: true 
        }
      },
      {
        path: 'meetings',
        name: 'Meetings',
        component: () => import('@/views/meeting/MeetingList.vue'),
        meta: { 
          title: '会议管理',
          requiresAuth: true 
        }
      },
      {
        path: 'files',
        name: 'Files',
        component: () => import('@/views/file/FileList.vue'),
        meta: { 
          title: '文件管理',
          requiresAuth: true 
        }
      },
      {
        path: 'workflows',
        name: 'Workflows',
        component: () => import('@/views/workflow/WorkflowList.vue'),
        meta: {
          title: '工作流管理',
          requiresAuth: true
        }
      },
      {
        path: 'schedules',
        name: 'Schedules',
        component: () => import('@/views/schedule/ScheduleList.vue'),
        meta: {
          title: '日程安排',
          requiresAuth: true
        }
      },
      {
        path: 'supplies',
        name: 'Supplies',
        component: () => import('@/views/supply/SupplyList.vue'),
        meta: {
          title: '办公用品管理',
          requiresAuth: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          title: '个人资料',
          requiresAuth: true
        }
      },
      {
        path: 'connection-test',
        name: 'ConnectionTest',
        component: () => import('@/views/ConnectionTest.vue'),
        meta: {
          title: '连接测试',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: { 
      title: '页面不存在' 
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智能办公系统`
  }
  
  const authStore = useAuthStore()
  
  // 需要认证的页面
  if (to.meta.requiresAuth) {
    if (!authStore.accessToken) {
      next('/login')
      return
    }
    
    // 验证token有效性
    if (!authStore.user) {
      try {
        // 添加超时处理，避免一直等待
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 5000)
        })

        await Promise.race([
          authStore.fetchUserProfile(),
          timeoutPromise
        ])
      } catch (error) {
        console.warn('获取用户信息失败:', error.message)
        authStore.clearAuth()

        // 如果是网络错误或服务器不可用，跳转到错误页面
        if (!error.response || error.message.includes('超时') || error.message.includes('Network Error')) {
          next('/server-error')
        } else {
          next('/login')
        }
        return
      }
    }
  }
  
  // 已登录用户访问登录页直接跳转到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router 