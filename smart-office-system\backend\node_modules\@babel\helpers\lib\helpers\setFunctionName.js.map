{"version": 3, "names": ["setFunctionName", "fn", "name", "prefix", "description", "Object", "defineProperty", "configurable", "value", "_"], "sources": ["../../src/helpers/setFunctionName.ts"], "sourcesContent": ["/* @minVersion 7.23.6 */\n\n// https://tc39.es/ecma262/#sec-setfunctionname\nexport default function setFunctionName<T extends Function>(\n  fn: T,\n  name: symbol | string,\n  prefix?: string,\n): T {\n  if (typeof name === \"symbol\") {\n    // Here `undefined` is possible, we check for it in the next line.\n    name = name.description!;\n    name = name ? \"[\" + name + \"]\" : \"\";\n  }\n  // In some older browsers .name was non-configurable, here we catch any\n  // errors thrown by defineProperty.\n  try {\n    Object.defineProperty(fn, \"name\", {\n      configurable: true,\n      value: prefix ? prefix + \" \" + name : name,\n    });\n  } catch (_) {}\n  return fn;\n}\n"], "mappings": ";;;;;;AAGe,SAASA,eAAeA,CACrCC,EAAK,EACLC,IAAqB,EACrBC,MAAe,EACZ;EACH,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAE5BA,IAAI,GAAGA,IAAI,CAACE,WAAY;IACxBF,IAAI,GAAGA,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG,GAAG,EAAE;EACrC;EAGA,IAAI;IACFG,MAAM,CAACC,cAAc,CAACL,EAAE,EAAE,MAAM,EAAE;MAChCM,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAEL,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGD,IAAI,GAAGA;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOO,CAAC,EAAE,CAAC;EACb,OAAOR,EAAE;AACX", "ignoreList": []}