const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class FileShare extends Model {
    static associate(models) {
      // 关联到文件
      FileShare.belongsTo(models.File, {
        foreignKey: 'file_id',
        as: 'file'
      });

      // 关联到分享者
      FileShare.belongsTo(models.Employee, {
        foreignKey: 'shared_by',
        as: 'sharer'
      });

      // 关联到被分享者
      FileShare.belongsTo(models.Employee, {
        foreignKey: 'shared_with',
        as: 'recipient'
      });
    }

    // 静态方法：创建文件分享
    static async createShare(fileId, sharedBy, shareData) {
      const {
        shared_with,
        permissions = ['read'],
        expires_at,
        share_message,
        is_public_link = false,
        access_code
      } = shareData;

      // 检查文件是否存在
      const file = await sequelize.models.File.findByPk(fileId);
      if (!file) {
        throw new Error('文件不存在');
      }

      // 检查分享者是否有权限
      const allowedPermissions = file.canAccessBy(sharedBy);
      if (!allowedPermissions.includes('share')) {
        throw new Error('无权限分享此文件');
      }

      // 创建分享记录
      const share = await this.create({
        file_id: fileId,
        shared_by: sharedBy,
        shared_with: is_public_link ? null : shared_with,
        permissions,
        expires_at,
        share_message,
        is_public_link,
        access_code: is_public_link ? access_code : null,
        share_token: this.generateShareToken()
      });

      return share;
    }

    // 静态方法：生成分享令牌
    static generateShareToken() {
      const crypto = require('crypto');
      return crypto.randomBytes(32).toString('hex');
    }

    // 静态方法：通过分享令牌获取分享信息
    static async getByShareToken(shareToken, accessCode = null) {
      const where = {
        share_token: shareToken,
        is_active: true
      };

      // 检查过期时间
      where[sequelize.Sequelize.Op.or] = [
        { expires_at: null },
        { expires_at: { [sequelize.Sequelize.Op.gt]: new Date() } }
      ];

      const share = await this.findOne({
        where,
        include: [
          {
            model: sequelize.models.File,
            as: 'file',
            include: [{
              model: sequelize.models.Employee,
              as: 'uploader',
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name']
              }]
            }]
          },
          {
            model: sequelize.models.Employee,
            as: 'sharer',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ]
      });

      if (!share) {
        return null;
      }

      // 如果是公开链接且需要访问码
      if (share.is_public_link && share.access_code) {
        if (!accessCode || accessCode !== share.access_code) {
          throw new Error('访问码错误');
        }
      }

      return share;
    }

    // 静态方法：获取用户的分享记录
    static async getUserShares(userId, options = {}) {
      const {
        page = 1,
        pageSize = 20,
        type = 'shared_by', // shared_by 或 shared_with
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const where = {
        [type]: userId,
        is_active: true
      };

      if (keyword) {
        where['$file.original_name$'] = {
          [sequelize.Sequelize.Op.like]: `%${keyword}%`
        };
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.File,
            as: 'file',
            include: [{
              model: sequelize.models.Employee,
              as: 'uploader',
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name']
              }]
            }]
          },
          {
            model: sequelize.models.Employee,
            as: 'sharer',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: sequelize.models.Employee,
            as: 'recipient',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [[sortBy, sortOrder]],
        distinct: true
      });

      return {
        shares: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 实例方法：检查是否已过期
    isExpired() {
      if (!this.expires_at) {
        return false;
      }
      return new Date() > this.expires_at;
    }

    // 实例方法：检查用户是否有访问权限
    canAccessBy(userId) {
      if (!this.is_active || this.isExpired()) {
        return false;
      }

      // 公开链接任何人都可以访问
      if (this.is_public_link) {
        return true;
      }

      // 检查是否是分享的接收者
      return this.shared_with === userId;
    }

    // 实例方法：检查用户权限
    hasPermission(permission) {
      return this.permissions.includes(permission);
    }

    // 实例方法：记录访问日志
    async logAccess(userId, action = 'view') {
      this.access_count = (this.access_count || 0) + 1;
      this.last_accessed_at = new Date();
      this.last_accessed_by = userId;

      // 记录访问详情到日志
      const accessLog = {
        user_id: userId,
        action,
        accessed_at: new Date(),
        ip_address: null // 可从请求中获取
      };

      if (!this.access_logs) {
        this.access_logs = [];
      }
      this.access_logs.push(accessLog);

      return this.save();
    }

    // 实例方法：生成分享链接
    generateShareUrl(baseUrl) {
      return `${baseUrl}/share/${this.share_token}`;
    }

    // 实例方法：停用分享
    async deactivate() {
      this.is_active = false;
      this.deactivated_at = new Date();
      return this.save();
    }

    // 静态方法：清理过期分享
    static async cleanupExpiredShares() {
      const expiredShares = await this.update(
        { is_active: false },
        {
          where: {
            expires_at: { [sequelize.Sequelize.Op.lt]: new Date() },
            is_active: true
          }
        }
      );

      return expiredShares[0]; // 返回更新的记录数
    }

    // 静态方法：获取分享统计
    static async getShareStats(userId) {
      const stats = await this.findOne({
        where: {
          shared_by: userId
        },
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_shares'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN is_active = true THEN 1 END')), 'active_shares'],
          [sequelize.fn('COUNT', sequelize.literal('CASE WHEN is_public_link = true THEN 1 END')), 'public_shares'],
          [sequelize.fn('SUM', sequelize.col('access_count')), 'total_access_count']
        ],
        raw: true
      });

      return {
        totalShares: parseInt(stats.total_shares) || 0,
        activeShares: parseInt(stats.active_shares) || 0,
        publicShares: parseInt(stats.public_shares) || 0,
        totalAccessCount: parseInt(stats.total_access_count) || 0
      };
    }
  }

  FileShare.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    file_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'files',
        key: 'id'
      },
      comment: '文件ID'
    },
    shared_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '分享者ID'
    },
    shared_with: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '被分享者ID（公开链接时为null）'
    },
    share_token: {
      type: DataTypes.STRING(64),
      allowNull: false,
      unique: true,
      comment: '分享令牌'
    },
    permissions: {
      type: DataTypes.JSON,
      defaultValue: ['read'],
      comment: '权限列表'
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '过期时间'
    },
    share_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '分享消息'
    },
    is_public_link: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否为公开链接'
    },
    access_code: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '访问码（公开链接）'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否有效'
    },
    access_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '访问次数'
    },
    last_accessed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后访问时间'
    },
    last_accessed_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '最后访问者ID'
    },
    deactivated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '停用时间'
    },
    access_logs: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '访问日志'
    }
  }, {
    sequelize,
    modelName: 'FileShare',
    tableName: 'file_shares',
    timestamps: true,
    comment: '文件分享表',
    indexes: [
      {
        fields: ['file_id']
      },
      {
        fields: ['shared_by']
      },
      {
        fields: ['shared_with']
      },
      {
        fields: ['share_token'],
        unique: true
      },
      {
        fields: ['expires_at']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  return FileShare;
}; 