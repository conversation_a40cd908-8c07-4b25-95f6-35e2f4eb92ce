const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class WorkflowInstance extends Model {
    static associate(models) {
      // 关联到工作流定义
      WorkflowInstance.belongsTo(models.WorkflowDefinition, {
        foreignKey: 'workflow_definition_id',
        as: 'definition'
      });

      // 关联到发起人
      WorkflowInstance.belongsTo(models.Employee, {
        foreignKey: 'initiator_id',
        as: 'initiator'
      });

      // 关联到审批任务
      WorkflowInstance.hasMany(models.ApprovalTask, {
        foreignKey: 'workflow_instance_id',
        as: 'approvalTasks'
      });

      // 关联到当前节点
      WorkflowInstance.belongsTo(models.WorkflowNode, {
        foreignKey: 'current_node_id',
        as: 'currentNode'
      });
    }

    // 静态方法：获取工作流实例列表
    static async getInstanceList(options = {}) {
      const {
        page = 1,
        pageSize = 20,
        status,
        initiatorId,
        assigneeId,
        workflowDefinitionId,
        startDate,
        endDate,
        keyword
      } = options;

      const where = {};
      
      if (status) {
        where.status = status;
      }
      
      if (initiatorId) {
        where.initiator_id = initiatorId;
      }
      
      if (workflowDefinitionId) {
        where.workflow_definition_id = workflowDefinitionId;
      }

      if (startDate && endDate) {
        where.created_at = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }
      
      if (keyword) {
        where[sequelize.Sequelize.Op.or] = [
          { title: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { business_key: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
        ];
      }

      // 如果指定了审批人，需要通过审批任务表关联查询
      let include = [
        {
          model: sequelize.models.WorkflowDefinition,
          as: 'definition',
          attributes: ['id', 'name', 'category']
        },
        {
          model: sequelize.models.Employee,
          as: 'initiator',
          include: [{
            model: sequelize.models.User,
            as: 'user',
            attributes: ['real_name']
          }]
        },
        {
          model: sequelize.models.WorkflowNode,
          as: 'currentNode',
          attributes: ['id', 'name', 'type']
        }
      ];

      if (assigneeId) {
        include.push({
          model: sequelize.models.ApprovalTask,
          as: 'approvalTasks',
          where: {
            assignee_id: assigneeId
          },
          required: true,
          attributes: []
        });
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include,
        limit: pageSize,
        offset,
        order: [['created_at', 'DESC']],
        distinct: true
      });

      return {
        instances: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：启动工作流实例
    static async startInstance(workflowDefinitionId, initiatorId, data = {}) {
      const definition = await sequelize.models.WorkflowDefinition.findByPk(workflowDefinitionId);
      if (!definition) {
        throw new Error('工作流定义不存在');
      }

      if (definition.status !== 'active') {
        throw new Error('工作流定义未激活');
      }

      // 获取起始节点
      const startNode = await definition.getStartNode();
      if (!startNode) {
        throw new Error('工作流定义缺少起始节点');
      }

      // 创建工作流实例
      const instance = await this.create({
        workflow_definition_id: workflowDefinitionId,
        initiator_id: initiatorId,
        current_node_id: startNode.id,
        title: data.title || `${definition.name} - ${new Date().toLocaleDateString()}`,
        business_key: data.business_key,
        form_data: data.form_data || {},
        status: 'running',
        started_at: new Date()
      });

      // 移动到下一个节点
      await instance.moveToNext();

      return instance;
    }

    // 实例方法：移动到下一个节点
    async moveToNext(approvalResult = {}) {
      const currentNode = await sequelize.models.WorkflowNode.findByPk(this.current_node_id);
      if (!currentNode) {
        throw new Error('当前节点不存在');
      }

      // 获取下一个节点
      const nextNodes = await sequelize.models.WorkflowNode.getNextNodes(
        this.current_node_id, 
        { ...this.form_data, ...approvalResult }
      );

      if (nextNodes.length === 0) {
        // 没有下一个节点，流程结束
        await this.complete();
        return;
      }

      const nextNode = nextNodes[0]; // 简化处理，只取第一个节点

      // 更新当前节点
      this.current_node_id = nextNode.id;
      
      if (nextNode.isEndNode()) {
        // 到达结束节点
        await this.complete();
      } else if (nextNode.isApprovalNode()) {
        // 创建审批任务
        await this.createApprovalTasks(nextNode);
      }

      await this.save();
    }

    // 实例方法：创建审批任务
    async createApprovalTasks(node) {
      const approvers = await node.getApprovers();
      
      if (approvers.length === 0) {
        throw new Error(`审批节点 ${node.name} 没有配置审批人`);
      }

      const tasks = [];
      for (const approver of approvers) {
        const task = await sequelize.models.ApprovalTask.create({
          workflow_instance_id: this.id,
          workflow_node_id: node.id,
          assignee_id: approver.id,
          status: 'pending',
          deadline: node.timeout ? new Date(Date.now() + node.timeout) : null,
          task_data: {
            approver_type: approver.type,
            department: approver.department,
            role: approver.role
          }
        });
        tasks.push(task);
      }

      return tasks;
    }

    // 实例方法：完成工作流
    async complete() {
      this.status = 'completed';
      this.completed_at = new Date();
      await this.save();

      // 触发完成事件
      await this.onComplete();
    }

    // 实例方法：拒绝工作流
    async reject(reason, rejectedBy) {
      this.status = 'rejected';
      this.rejected_at = new Date();
      this.rejected_by = rejectedBy;
      this.rejection_reason = reason;
      await this.save();

      // 更新所有未完成的审批任务状态
      await sequelize.models.ApprovalTask.update(
        { status: 'cancelled' },
        {
          where: {
            workflow_instance_id: this.id,
            status: 'pending'
          }
        }
      );

      // 触发拒绝事件
      await this.onReject();
    }

    // 实例方法：取消工作流
    async cancel(reason, cancelledBy) {
      this.status = 'cancelled';
      this.cancelled_at = new Date();
      this.cancelled_by = cancelledBy;
      this.cancellation_reason = reason;
      await this.save();

      // 更新所有未完成的审批任务状态
      await sequelize.models.ApprovalTask.update(
        { status: 'cancelled' },
        {
          where: {
            workflow_instance_id: this.id,
            status: 'pending'
          }
        }
      );

      // 触发取消事件
      await this.onCancel();
    }

    // 实例方法：获取审批历史
    async getApprovalHistory() {
      return sequelize.models.ApprovalTask.findAll({
        where: {
          workflow_instance_id: this.id
        },
        include: [
          {
            model: sequelize.models.Employee,
            as: 'assignee',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: sequelize.models.WorkflowNode,
            as: 'node',
            attributes: ['name', 'type']
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // 实例方法：获取当前待审批任务
    async getPendingTasks() {
      return sequelize.models.ApprovalTask.findAll({
        where: {
          workflow_instance_id: this.id,
          status: 'pending'
        },
        include: [
          {
            model: sequelize.models.Employee,
            as: 'assignee',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name', 'email']
            }]
          },
          {
            model: sequelize.models.WorkflowNode,
            as: 'node',
            attributes: ['name', 'type', 'timeout']
          }
        ]
      });
    }

    // 实例方法：检查是否可以被用户操作
    canBeOperatedBy(userId) {
      // 发起人可以取消
      if (this.initiator_id === userId) {
        return ['cancel'];
      }

      // 其他操作权限可以根据业务需求扩展
      return [];
    }

    // 实例方法：获取流程进度
    async getProgress() {
      const allNodes = await sequelize.models.WorkflowNode.findAll({
        where: {
          workflow_definition_id: this.workflow_definition_id
        },
        order: [['order', 'ASC']]
      });

      const currentNode = await sequelize.models.WorkflowNode.findByPk(this.current_node_id);
      const currentOrder = currentNode ? currentNode.order : 0;

      const progress = {
        current: currentOrder,
        total: allNodes.length,
        percentage: Math.round((currentOrder / allNodes.length) * 100),
        nodes: allNodes.map(node => ({
          id: node.id,
          name: node.name,
          type: node.type,
          order: node.order,
          completed: node.order < currentOrder,
          current: node.order === currentOrder
        }))
      };

      return progress;
    }

    // 事件处理方法
    async onComplete() {
      // 可以在这里添加完成后的业务逻辑，如发送通知等
      console.log(`工作流实例 ${this.id} 已完成`);
    }

    async onReject() {
      // 可以在这里添加拒绝后的业务逻辑
      console.log(`工作流实例 ${this.id} 已拒绝`);
    }

    async onCancel() {
      // 可以在这里添加取消后的业务逻辑
      console.log(`工作流实例 ${this.id} 已取消`);
    }

    // 静态方法：获取统计数据
    static async getStatistics(options = {}) {
      const { userId, startDate, endDate } = options;
      
      const where = {};
      if (userId) {
        where.initiator_id = userId;
      }
      if (startDate && endDate) {
        where.created_at = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }

      const statusStats = await this.findAll({
        where,
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const stats = {
        total: 0,
        running: 0,
        completed: 0,
        rejected: 0,
        cancelled: 0
      };

      statusStats.forEach(stat => {
        stats[stat.status] = parseInt(stat.count);
        stats.total += parseInt(stat.count);
      });

      return stats;
    }
  }

  WorkflowInstance.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflow_definition_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_definitions',
        key: 'id'
      },
      comment: '工作流定义ID'
    },
    initiator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '发起人ID'
    },
    current_node_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      },
      comment: '当前节点ID'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '实例标题'
    },
    business_key: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '业务关键字'
    },
    form_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '表单数据'
    },
    status: {
      type: DataTypes.ENUM('running', 'completed', 'rejected', 'cancelled', 'suspended'),
      defaultValue: 'running',
      comment: '实例状态'
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal',
      comment: '优先级'
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '开始时间'
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '完成时间'
    },
    rejected_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '拒绝时间'
    },
    rejected_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '拒绝人ID'
    },
    rejection_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '拒绝原因'
    },
    cancelled_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '取消时间'
    },
    cancelled_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '取消人ID'
    },
    cancellation_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '取消原因'
    },
    variables: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '流程变量'
    }
  }, {
    sequelize,
    modelName: 'WorkflowInstance',
    tableName: 'workflow_instances',
    timestamps: true,
    paranoid: true,
    comment: '工作流实例表',
    indexes: [
      {
        fields: ['workflow_definition_id']
      },
      {
        fields: ['initiator_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['business_key']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return WorkflowInstance;
}; 