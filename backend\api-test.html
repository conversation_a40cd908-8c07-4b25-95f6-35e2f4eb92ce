<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能办公系统 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        h3 {
            color: #34495e;
            margin-bottom: 15px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 智能办公系统 API 测试面板</h1>
        <p style="text-align: center; color: #666;">
            测试所有后端API接口，确保系统正常运行
        </p>
    </div>
    
    <div class="grid">
        <div class="test-section">
            <h3>🔐 认证相关</h3>
            <button onclick="testLogin()">登录测试</button>
            <button onclick="testProfile()">用户信息</button>
            <div id="auth-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🏢 部门管理</h3>
            <button onclick="testDepartments()">获取部门列表</button>
            <button onclick="createDepartment()">创建测试部门</button>
            <div id="dept-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>👔 职位管理</h3>
            <button onclick="testPositions()">获取职位列表</button>
            <button onclick="createPosition()">创建测试职位</button>
            <div id="pos-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>👥 员工管理</h3>
            <button onclick="testEmployees()">获取员工列表</button>
            <div id="emp-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>⏰ 考勤管理</h3>
            <button onclick="testAttendanceRecords()">考勤记录</button>
            <button onclick="testTodayStatus()">今日状态</button>
            <button onclick="testClockIn()">模拟打卡</button>
            <div id="att-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🏢 会议管理</h3>
            <button onclick="testMeetings()">获取会议列表</button>
            <button onclick="testMeetingRooms()">获取会议室</button>
            <button onclick="createMeeting()">创建测试会议</button>
            <div id="meeting-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 仪表盘</h3>
            <button onclick="testDashboardStats()">统计数据</button>
            <button onclick="testAttendanceChart()">考勤图表</button>
            <div id="dash-result" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:3001/api';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function displayResult(elementId, result, title = '') {
            const element = document.getElementById(elementId);
            const statusClass = result.success ? 'success' : 'error';
            const statusText = result.success ? '✅ 成功' : '❌ 失败';
            
            element.innerHTML = `
                <strong>${title}</strong>
                <span class="status ${statusClass}">${statusText}</span>
                <br><br>
                ${JSON.stringify(result, null, 2)}
            `;
            element.className = `result ${statusClass}`;
        }
        
        // 认证相关
        async function testLogin() {
            const result = await makeRequest(`${API_BASE}/v1/auth/login`, {
                method: 'POST',
                body: JSON.stringify({ username: 'admin', password: '123456' })
            });
            
            if (result.success && result.data.success) {
                authToken = result.data.data.token;
                console.log('登录成功，token:', authToken);
            }
            
            displayResult('auth-result', result, '登录测试');
        }
        
        async function testProfile() {
            if (!authToken) {
                displayResult('auth-result', { success: false, error: '请先登录' }, '用户信息');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/auth/profile`);
            displayResult('auth-result', result, '用户信息');
        }
        
        // 部门管理
        async function testDepartments() {
            if (!authToken) {
                displayResult('dept-result', { success: false, error: '请先登录' }, '部门列表');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/departments`);
            displayResult('dept-result', result, '部门列表');
        }
        
        async function createDepartment() {
            if (!authToken) {
                displayResult('dept-result', { success: false, error: '请先登录' }, '创建部门');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/departments`, {
                method: 'POST',
                body: JSON.stringify({
                    name: '测试部门',
                    code: 'TEST_' + Date.now(),
                    description: '这是一个测试部门'
                })
            });
            displayResult('dept-result', result, '创建部门');
        }
        
        // 职位管理
        async function testPositions() {
            if (!authToken) {
                displayResult('pos-result', { success: false, error: '请先登录' }, '职位列表');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/positions`);
            displayResult('pos-result', result, '职位列表');
        }
        
        async function createPosition() {
            if (!authToken) {
                displayResult('pos-result', { success: false, error: '请先登录' }, '创建职位');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/positions`, {
                method: 'POST',
                body: JSON.stringify({
                    name: '测试职位_' + Date.now(),
                    level: 2,
                    description: '这是一个测试职位'
                })
            });
            displayResult('pos-result', result, '创建职位');
        }
        
        // 员工管理
        async function testEmployees() {
            if (!authToken) {
                displayResult('emp-result', { success: false, error: '请先登录' }, '员工列表');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/employees`);
            displayResult('emp-result', result, '员工列表');
        }
        
        // 考勤管理
        async function testAttendanceRecords() {
            if (!authToken) {
                displayResult('att-result', { success: false, error: '请先登录' }, '考勤记录');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/attendance/records`);
            displayResult('att-result', result, '考勤记录');
        }
        
        async function testTodayStatus() {
            if (!authToken) {
                displayResult('att-result', { success: false, error: '请先登录' }, '今日状态');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/attendance/today-status`);
            displayResult('att-result', result, '今日考勤状态');
        }
        
        async function testClockIn() {
            if (!authToken) {
                displayResult('att-result', { success: false, error: '请先登录' }, '模拟打卡');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/attendance/clock-in`, {
                method: 'POST',
                body: JSON.stringify({
                    location: '公司总部',
                    latitude: 39.9042,
                    longitude: 116.4074,
                    remarks: '正常上班打卡'
                })
            });
            displayResult('att-result', result, '模拟打卡');
        }
        
        // 会议管理
        async function testMeetings() {
            if (!authToken) {
                displayResult('meeting-result', { success: false, error: '请先登录' }, '会议列表');
                return;
            }

            const result = await makeRequest(`${API_BASE}/v1/meetings`);
            displayResult('meeting-result', result, '会议列表');
        }

        async function testMeetingRooms() {
            if (!authToken) {
                displayResult('meeting-result', { success: false, error: '请先登录' }, '会议室列表');
                return;
            }

            const result = await makeRequest(`${API_BASE}/v1/meeting-rooms`);
            displayResult('meeting-result', result, '会议室列表');
        }

        async function createMeeting() {
            if (!authToken) {
                displayResult('meeting-result', { success: false, error: '请先登录' }, '创建会议');
                return;
            }

            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(14, 0, 0, 0);

            const endTime = new Date(tomorrow);
            endTime.setHours(15, 0, 0, 0);

            const result = await makeRequest(`${API_BASE}/v1/meetings`, {
                method: 'POST',
                body: JSON.stringify({
                    title: '测试会议_' + Date.now(),
                    description: '这是一个API测试会议',
                    startTime: tomorrow.toISOString(),
                    endTime: endTime.toISOString(),
                    meetingType: 'internal',
                    agenda: '1. 测试议题\n2. 讨论事项'
                })
            });
            displayResult('meeting-result', result, '创建会议');
        }

        // 仪表盘
        async function testDashboardStats() {
            if (!authToken) {
                displayResult('dash-result', { success: false, error: '请先登录' }, '仪表盘统计');
                return;
            }

            const result = await makeRequest(`${API_BASE}/v1/dashboard/stats`);
            displayResult('dash-result', result, '仪表盘统计');
        }
        
        async function testAttendanceChart() {
            if (!authToken) {
                displayResult('dash-result', { success: false, error: '请先登录' }, '考勤图表');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/v1/dashboard/attendance-chart`);
            displayResult('dash-result', result, '考勤图表');
        }
        
        // 页面加载时自动测试登录
        window.onload = function() {
            console.log('API测试页面已加载');
            console.log('请点击"登录测试"按钮开始测试');
        };
    </script>
</body>
</html>
