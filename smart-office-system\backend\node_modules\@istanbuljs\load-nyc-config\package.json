{"name": "@istanbuljs/load-nyc-config", "version": "1.1.0", "description": "Utility function to load nyc configuration", "main": "index.js", "scripts": {"pretest": "xo", "test": "tap", "snap": "npm test -- --snapshot", "release": "standard-version"}, "engines": {"node": ">=8"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/load-nyc-config.git"}, "bugs": {"url": "https://github.com/istanbuljs/load-nyc-config/issues"}, "homepage": "https://github.com/istanbuljs/load-nyc-config#readme", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "devDependencies": {"semver": "^6.3.0", "standard-version": "^7.0.0", "tap": "^14.10.5", "xo": "^0.25.3"}, "xo": {"ignores": ["test/fixtures/extends/invalid.*"], "rules": {"require-atomic-updates": 0, "capitalized-comments": 0, "unicorn/import-index": 0, "import/extensions": 0, "import/no-useless-path-segments": 0}}}