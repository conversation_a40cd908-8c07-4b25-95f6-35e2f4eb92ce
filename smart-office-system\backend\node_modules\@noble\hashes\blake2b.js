"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.blake2b = exports.BLAKE2b = void 0;
/**
 * Blake2b hash function. Focuses on 64-bit platforms, but in JS speed different from Blake2s is negligible.
 * @module
 * @deprecated
 */
const blake2_ts_1 = require("./blake2.js");
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.BLAKE2b = blake2_ts_1.BLAKE2b;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.blake2b = blake2_ts_1.blake2b;
//# sourceMappingURL=blake2b.js.map