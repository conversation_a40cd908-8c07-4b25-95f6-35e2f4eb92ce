const express = require('express');
const router = express.Router();
const workflowController = require('../controllers/workflowController');
const { authenticateToken } = require('../middleware/auth');
const { validateWorkflowDefinition, validateWorkflowInstance, validateApprovalTask } = require('../middleware/validation');

// 应用认证中间件到所有路由
router.use(authenticateToken);

// 工作流定义相关路由
router.get('/definitions', workflowController.getWorkflowDefinitions);
router.post('/definitions', validateWorkflowDefinition, workflowController.createWorkflowDefinition);
router.get('/definitions/active', workflowController.getActiveWorkflowDefinitions);
router.get('/definitions/:definitionId', workflowController.getWorkflowDefinitionById);
router.put('/definitions/:definitionId', validateWorkflowDefinition, workflowController.updateWorkflowDefinition);
router.post('/definitions/:definitionId/activate', workflowController.activateWorkflowDefinition);
router.post('/definitions/:definitionId/deactivate', workflowController.deactivateWorkflowDefinition);
router.post('/definitions/:definitionId/clone', workflowController.cloneWorkflowDefinition);
router.post('/definitions/:definitionId/validate', workflowController.validateWorkflowDefinition);
router.get('/definitions/:definitionId/usage-stats', workflowController.getWorkflowUsageStats);

// 工作流实例相关路由
router.post('/instances', validateWorkflowInstance, workflowController.startWorkflowInstance);
router.get('/instances', workflowController.getWorkflowInstances);
router.get('/instances/my', workflowController.getMyWorkflowInstances);
router.get('/instances/:instanceId', workflowController.getWorkflowInstanceById);
router.put('/instances/:instanceId/cancel', workflowController.cancelWorkflowInstance);

// 审批任务相关路由
router.get('/approval-tasks', workflowController.getPendingApprovalTasks);
router.get('/approval-tasks/:taskId', workflowController.getApprovalTaskById);
router.post('/approval-tasks/:taskId/process', validateApprovalTask, workflowController.processApprovalTask);

// 统计和报表路由
router.get('/statistics', workflowController.getWorkflowStatistics);

// 集成相关路由
router.post('/integrations/leave-request', workflowController.integrateWithLeaveRequest);

module.exports = router; 