<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="编辑个人资料"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="real_name">
            <el-input v-model="formData.real_name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="formData.gender">
              <el-radio label="male">男</el-radio>
              <el-radio label="female">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生日" prop="birth_date">
            <el-date-picker
              v-model="formData.birth_date"
              type="date"
              placeholder="请选择生日"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model="formData.email" placeholder="请输入邮箱" readonly />
        <div class="form-tip">邮箱不可修改</div>
      </el-form-item>

      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <el-avatar
            v-if="formData.avatar"
            :src="formData.avatar"
            :size="80"
            shape="square"
          />
          <el-icon v-else class="avatar-uploader-icon" :size="80">
            <Plus />
          </el-icon>
        </el-upload>
        <div class="form-tip">点击上传头像，支持 JPG、PNG 格式，大小不超过 2MB</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Store
const authStore = useAuthStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)

const formData = reactive({
  real_name: '',
  phone: '',
  email: '',
  gender: 'male',
  birth_date: '',
  avatar: ''
})

// 上传配置
const uploadUrl = computed(() => `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/v1/files/upload`)
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.accessToken}`
}))

// 表单验证规则
const formRules = {
  real_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadUserData()
  } else {
    resetForm()
  }
})

// 加载用户数据
const loadUserData = () => {
  if (authStore.user) {
    Object.assign(formData, {
      real_name: authStore.user.real_name || '',
      phone: authStore.user.phone || '',
      email: authStore.user.email || '',
      gender: authStore.user.gender || 'male',
      birth_date: authStore.user.birth_date || '',
      avatar: authStore.user.avatar || ''
    })
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    real_name: '',
    phone: '',
    email: '',
    gender: 'male',
    birth_date: '',
    avatar: ''
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 头像上传成功回调
const handleAvatarSuccess = (response) => {
  if (response.success) {
    formData.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 头像上传前检查
const beforeAvatarUpload = (file) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPGOrPNG) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 准备更新数据
    const updateData = {
      real_name: formData.real_name,
      phone: formData.phone,
      gender: formData.gender,
      birth_date: formData.birth_date,
      avatar: formData.avatar
    }

    // 调用更新接口
    await authStore.updateProfile(updateData)
    
    ElMessage.success('个人资料更新成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error(error || '更新失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.avatar-uploader {
  .avatar-uploader-icon {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c939d;
    font-size: 28px;
  }

  .avatar-uploader-icon:hover {
    border-color: var(--el-color-primary);
  }
}

:deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.el-upload:hover) {
  border-color: var(--el-color-primary);
}
</style> 