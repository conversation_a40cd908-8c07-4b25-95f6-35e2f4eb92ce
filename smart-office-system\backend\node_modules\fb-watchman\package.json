{"name": "fb-watchman", "version": "2.0.2", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "**************:facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": "<PERSON><PERSON> Furlong <<EMAIL>> (http://wezfurlong.org)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "2.1.1"}}