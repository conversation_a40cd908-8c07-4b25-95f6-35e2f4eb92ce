{"version": 3, "file": "XUserDefinedEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/x-user-defined/XUserDefinedEncoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,0DAA6E;AAC7E,sDAAmD;AAEnD;;;;GAIG;AACH;IAIE,6BAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,qCAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,qDAAqD;QACrD,IAAI,UAAU,KAAK,2BAAa;YAC9B,OAAO,mBAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,8BAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,gEAAgE;QAChE,2DAA2D;QAC3D,IAAI,mBAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;YACrC,OAAO,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC;QAEpC,mCAAmC;QACnC,OAAO,wBAAY,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,0BAAC;AAAD,CAAC,AA/BD,IA+BC;AA/BY,kDAAmB"}