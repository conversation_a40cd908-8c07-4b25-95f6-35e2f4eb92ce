// MySQL配置脚本
// 设置环境变量以连接MySQL数据库

console.log('🔧 配置MySQL环境变量...');

// 设置MySQL环境变量
process.env.DB_DIALECT = 'mysql';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_NAME = 'smart_office_system';
process.env.DB_USER = 'root';
process.env.DB_PASSWORD = '123456';
process.env.DB_LOGGING = 'true';
process.env.NODE_ENV = 'development';
process.env.PORT = '3001';
process.env.JWT_SECRET = 'smart_office_jwt_secret_key_at_least_32_characters_long_for_security';
process.env.JWT_EXPIRES_IN = '7d';

console.log('✅ MySQL环境变量已设置:');
console.log('- 数据库类型:', process.env.DB_DIALECT);
console.log('- 数据库主机:', process.env.DB_HOST);
console.log('- 数据库端口:', process.env.DB_PORT);
console.log('- 数据库名称:', process.env.DB_NAME);
console.log('- 数据库用户:', process.env.DB_USER);

// 启动应用
console.log('\n🚀 启动应用...');
require('./src/app.js'); 