const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class UserRole extends Model {
    static associate(models) {
      // 关联到用户表
      UserRole.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });

      // 关联到角色表
      UserRole.belongsTo(models.Role, {
        foreignKey: 'role_id',
        as: 'role'
      });

      // 关联到授权人（员工表）
      UserRole.belongsTo(models.Employee, {
        foreignKey: 'granted_by',
        as: 'granter'
      });
    }

    // 静态方法：为用户分配角色
    static async assignRole(userId, roleId, grantedBy = null) {
      return this.create({
        user_id: userId,
        role_id: roleId,
        granted_by: grantedBy,
        granted_at: new Date(),
        is_active: true
      });
    }

    // 静态方法：撤销用户角色
    static async revokeRole(userId, roleId) {
      return this.update(
        { is_active: false },
        {
          where: {
            user_id: userId,
            role_id: roleId,
            is_active: true
          }
        }
      );
    }

    // 静态方法：获取用户的活跃角色
    static async getUserActiveRoles(userId) {
      return this.findAll({
        where: {
          user_id: userId,
          is_active: true,
          [sequelize.Sequelize.Op.or]: [
            { expires_at: null },
            { expires_at: { [sequelize.Sequelize.Op.gt]: new Date() } }
          ]
        },
        include: [{
          model: sequelize.models.Role,
          as: 'role'
        }]
      });
    }

    // 实例方法：检查角色是否已过期
    isExpired() {
      if (!this.expires_at) return false;
      return new Date() > this.expires_at;
    }

    // 实例方法：延长角色有效期
    async extendExpiry(days) {
      const newExpiry = new Date();
      newExpiry.setDate(newExpiry.getDate() + days);
      return this.update({ expires_at: newExpiry });
    }
  }

  UserRole.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id'
      }
    },
    granted_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    granted_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'UserRole',
    tableName: 'user_roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['role_id']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['expires_at']
      },
      {
        unique: true,
        fields: ['user_id', 'role_id']
      }
    ]
  });

  return UserRole;
};
