const workflowService = require('../services/workflowService');
const { ResponseHelper } = require('../shared/response');
const logger = require('../shared/logger');

class WorkflowController {
  // 获取工作流定义列表
  async getWorkflowDefinitions(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        category,
        status,
        keyword,
        createdBy
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        category,
        status,
        keyword,
        createdBy: createdBy ? parseInt(createdBy) : undefined
      };

      const result = await workflowService.getWorkflowDefinitions(options);
      
      ResponseHelper.success(res, result, '获取工作流定义列表成功');
    } catch (error) {
      logger.error('获取工作流定义列表失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取工作流定义详情
  async getWorkflowDefinitionById(req, res) {
    try {
      const { definitionId } = req.params;
      
      const definition = await workflowService.getWorkflowDefinitionById(parseInt(definitionId));
      
      ResponseHelper.success(res, definition, '获取工作流定义详情成功');
    } catch (error) {
      logger.error('获取工作流定义详情失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 创建工作流定义
  async createWorkflowDefinition(req, res) {
    try {
      const { name, description, category, form_config, nodes } = req.body;
      const createdBy = req.user.employeeId;

      const definition = await workflowService.createWorkflowDefinition({
        name,
        description,
        category,
        form_config,
        nodes
      }, createdBy);
      
      ResponseHelper.success(res, definition, '创建工作流定义成功', 201);
    } catch (error) {
      logger.error('创建工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 更新工作流定义
  async updateWorkflowDefinition(req, res) {
    try {
      const { definitionId } = req.params;
      const { name, description, category, form_config, nodes } = req.body;
      const updatedBy = req.user.employeeId;

      const definition = await workflowService.updateWorkflowDefinition(
        parseInt(definitionId),
        { name, description, category, form_config, nodes },
        updatedBy
      );
      
      ResponseHelper.success(res, definition, '更新工作流定义成功');
    } catch (error) {
      logger.error('更新工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 激活工作流定义
  async activateWorkflowDefinition(req, res) {
    try {
      const { definitionId } = req.params;
      const activatedBy = req.user.employeeId;

      const definition = await workflowService.activateWorkflowDefinition(
        parseInt(definitionId),
        activatedBy
      );
      
      ResponseHelper.success(res, definition, '激活工作流定义成功');
    } catch (error) {
      logger.error('激活工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 停用工作流定义
  async deactivateWorkflowDefinition(req, res) {
    try {
      const { definitionId } = req.params;
      const deactivatedBy = req.user.employeeId;

      const definition = await workflowService.deactivateWorkflowDefinition(
        parseInt(definitionId),
        deactivatedBy
      );
      
      ResponseHelper.success(res, definition, '停用工作流定义成功');
    } catch (error) {
      logger.error('停用工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 复制工作流定义
  async cloneWorkflowDefinition(req, res) {
    try {
      const { definitionId } = req.params;
      const { newName } = req.body;
      const createdBy = req.user.employeeId;

      const definition = await workflowService.cloneWorkflowDefinition(
        parseInt(definitionId),
        newName,
        createdBy
      );
      
      ResponseHelper.success(res, definition, '复制工作流定义成功', 201);
    } catch (error) {
      logger.error('复制工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 验证工作流定义
  async validateWorkflowDefinition(req, res) {
    try {
      const { definitionId } = req.params;

      const result = await workflowService.validateWorkflowDefinition(parseInt(definitionId));
      
      ResponseHelper.success(res, result, '验证工作流定义完成');
    } catch (error) {
      logger.error('验证工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取活跃的工作流定义
  async getActiveWorkflowDefinitions(req, res) {
    try {
      const { category } = req.query;

      const definitions = await workflowService.getActiveWorkflowDefinitions(category);
      
      ResponseHelper.success(res, definitions, '获取活跃工作流定义成功');
    } catch (error) {
      logger.error('获取活跃工作流定义失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 启动工作流实例
  async startWorkflowInstance(req, res) {
    try {
      const { workflowDefinitionId, title, business_key, form_data, priority } = req.body;
      const initiatorId = req.user.employeeId;

      const instance = await workflowService.startWorkflowInstance(
        parseInt(workflowDefinitionId),
        initiatorId,
        { title, business_key, form_data, priority }
      );
      
      ResponseHelper.success(res, instance, '启动工作流实例成功', 201);
    } catch (error) {
      logger.error('启动工作流实例失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取工作流实例列表
  async getWorkflowInstances(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        status,
        initiatorId,
        assigneeId,
        workflowDefinitionId,
        startDate,
        endDate,
        keyword
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        status,
        initiatorId: initiatorId ? parseInt(initiatorId) : undefined,
        assigneeId: assigneeId ? parseInt(assigneeId) : undefined,
        workflowDefinitionId: workflowDefinitionId ? parseInt(workflowDefinitionId) : undefined,
        startDate,
        endDate,
        keyword
      };

      const result = await workflowService.getWorkflowInstances(options);
      
      ResponseHelper.success(res, result, '获取工作流实例列表成功');
    } catch (error) {
      logger.error('获取工作流实例列表失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取工作流实例详情
  async getWorkflowInstanceById(req, res) {
    try {
      const { instanceId } = req.params;
      
      const instance = await workflowService.getWorkflowInstanceById(parseInt(instanceId));
      
      ResponseHelper.success(res, instance, '获取工作流实例详情成功');
    } catch (error) {
      logger.error('获取工作流实例详情失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 取消工作流实例
  async cancelWorkflowInstance(req, res) {
    try {
      const { instanceId } = req.params;
      const { reason } = req.body;
      const cancelledBy = req.user.employeeId;

      const instance = await workflowService.cancelWorkflowInstance(
        parseInt(instanceId),
        reason,
        cancelledBy
      );
      
      ResponseHelper.success(res, instance, '取消工作流实例成功');
    } catch (error) {
      logger.error('取消工作流实例失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取用户发起的工作流实例
  async getMyWorkflowInstances(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        status,
        workflowDefinitionId,
        startDate,
        endDate
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        status,
        initiatorId: req.user.employeeId,
        workflowDefinitionId: workflowDefinitionId ? parseInt(workflowDefinitionId) : undefined,
        startDate,
        endDate
      };

      const result = await workflowService.getWorkflowInstances(options);
      
      ResponseHelper.success(res, result, '获取我发起的工作流实例成功');
    } catch (error) {
      logger.error('获取我发起的工作流实例失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取待审批任务
  async getPendingApprovalTasks(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        category,
        priority,
        keyword,
        overdue
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        category,
        priority,
        keyword,
        overdue: overdue === 'true'
      };

      const result = await workflowService.getPendingApprovalTasks(req.user.employeeId, options);
      
      ResponseHelper.success(res, result, '获取待审批任务成功');
    } catch (error) {
      logger.error('获取待审批任务失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取审批任务详情
  async getApprovalTaskById(req, res) {
    try {
      const { taskId } = req.params;
      
      const task = await workflowService.getApprovalTaskById(parseInt(taskId));
      
      ResponseHelper.success(res, task, '获取审批任务详情成功');
    } catch (error) {
      logger.error('获取审批任务详情失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 处理审批任务
  async processApprovalTask(req, res) {
    try {
      const { taskId } = req.params;
      const { action, comment, attachments } = req.body;
      const processedBy = req.user.employeeId;

      if (!['approve', 'reject'].includes(action)) {
        return ResponseHelper.error(res, '无效的审批操作');
      }

      const result = await workflowService.approveTask(
        parseInt(taskId),
        processedBy,
        action,
        comment,
        attachments
      );
      
      ResponseHelper.success(res, result, `${action === 'approve' ? '审批通过' : '审批拒绝'}成功`);
    } catch (error) {
      logger.error('处理审批任务失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取工作流统计数据
  async getWorkflowStatistics(req, res) {
    try {
      const { startDate, endDate, scope } = req.query;
      
      const options = {
        startDate,
        endDate
      };

      // 如果scope是'my'，只查看当前用户的统计
      if (scope === 'my') {
        options.userId = req.user.employeeId;
      }

      const stats = await workflowService.getWorkflowStatistics(options);
      
      ResponseHelper.success(res, stats, '获取工作流统计数据成功');
    } catch (error) {
      logger.error('获取工作流统计数据失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 获取工作流使用统计
  async getWorkflowUsageStats(req, res) {
    try {
      const { definitionId } = req.params;
      const { startDate, endDate } = req.query;

      const stats = await workflowService.getWorkflowUsageStats(
        parseInt(definitionId),
        startDate,
        endDate
      );
      
      ResponseHelper.success(res, stats, '获取工作流使用统计成功');
    } catch (error) {
      logger.error('获取工作流使用统计失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }

  // 与请假申请集成
  async integrateWithLeaveRequest(req, res) {
    try {
      const { leaveRequestId } = req.body;
      const employeeId = req.user.employeeId;

      const instance = await workflowService.integrateWithLeaveRequest(
        parseInt(leaveRequestId),
        employeeId
      );
      
      ResponseHelper.success(res, instance, '请假审批流程启动成功', 201);
    } catch (error) {
      logger.error('请假审批流程启动失败:', error);
      ResponseHelper.error(res, error.message);
    }
  }
}

module.exports = new WorkflowController(); 