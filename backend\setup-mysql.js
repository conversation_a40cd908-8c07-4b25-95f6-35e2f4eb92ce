const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function setupMySQL() {
  try {
    console.log('🔧 开始设置MySQL数据库...');

    // 1. 连接MySQL服务器（不指定数据库）
    console.log('📡 连接MySQL服务器...');
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456'
    });

    console.log('✅ MySQL连接成功');

    // 2. 创建数据库
    console.log('🗄️ 创建数据库 smart_office_system...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS smart_office_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ 数据库创建成功');

    // 3. 选择数据库
    await connection.execute('USE smart_office_system');

    // 4. 读取并执行SQL初始化脚本
    const sqlPath = path.join(__dirname, 'create-mysql-database.sql');
    if (fs.existsSync(sqlPath)) {
      console.log('📜 执行数据库初始化脚本...');
      const sqlContent = fs.readFileSync(sqlPath, 'utf8');
      
      // 分割SQL语句并执行
      const statements = sqlContent.split(';').filter(stmt => stmt.trim());
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await connection.execute(statement);
          } catch (error) {
            if (!error.message.includes('already exists')) {
              console.warn('⚠️ SQL执行警告:', error.message);
            }
          }
        }
      }
      console.log('✅ 数据库初始化脚本执行完成');
    }

    // 5. 插入初始管理员用户
    console.log('👤 创建管理员用户...');
    try {
      // 插入角色
      await connection.execute(`
        INSERT IGNORE INTO roles (id, name, description, permissions, created_at, updated_at) 
        VALUES (1, 'admin', '系统管理员', '["*"]', NOW(), NOW())
      `);

      // 插入部门
      await connection.execute(`
        INSERT IGNORE INTO departments (id, name, description, created_at, updated_at) 
        VALUES (1, '管理部', '系统管理部门', NOW(), NOW())
      `);

      // 插入职位
      await connection.execute(`
        INSERT IGNORE INTO positions (id, name, department_id, level, description, created_at, updated_at) 
        VALUES (1, '系统管理员', 1, 1, '系统管理员职位', NOW(), NOW())
      `);

      // 插入管理员用户
      await connection.execute(`
        INSERT IGNORE INTO users (id, username, password, real_name, email, phone, status, created_at, updated_at) 
        VALUES (1, 'admin', '$2b$10$rqD0qYzV0XGM4KT5oAhkA.Q8qY5nN2xqH1VnI5qGQM4KA5nN2xqH1V', '系统管理员', '<EMAIL>', '13800138000', 'active', NOW(), NOW())
      `);

      // 插入员工记录
      await connection.execute(`
        INSERT IGNORE INTO employees (id, employee_no, user_id, department_id, position_id, hire_date, status, created_at, updated_at) 
        VALUES (1, 'EMP20240001', 1, 1, 1, '2024-01-01', 'active', NOW(), NOW())
      `);

      // 插入用户角色关联
      await connection.execute(`
        INSERT IGNORE INTO user_roles (user_id, role_id, created_at, updated_at) 
        VALUES (1, 1, NOW(), NOW())
      `);

      console.log('✅ 管理员用户创建成功 (用户名: admin, 密码: 123456)');
    } catch (error) {
      console.warn('⚠️ 管理员用户可能已存在:', error.message);
    }

    await connection.end();
    console.log('🎉 MySQL数据库设置完成！');
    console.log('📝 数据库信息:');
    console.log('   - 数据库名: smart_office_system');
    console.log('   - 管理员账号: admin');
    console.log('   - 管理员密码: 123456');

  } catch (error) {
    console.error('❌ MySQL设置失败:', error.message);
    console.log('\n🔧 请确保:');
    console.log('1. MySQL服务已启动');
    console.log('2. root用户密码是 123456');
    console.log('3. MySQL监听在localhost:3306');
    process.exit(1);
  }
}

setupMySQL(); 