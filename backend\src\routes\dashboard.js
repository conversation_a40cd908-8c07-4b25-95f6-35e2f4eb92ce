const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authenticateToken } = require('../middleware/auth');

// 应用认证中间件到所有dashboard路由
router.use(authenticateToken);

/**
 * @route   GET /api/v1/dashboard/stats
 * @desc    获取仪表盘统计数据
 * @access  Private
 */
router.get('/stats', dashboardController.getStats);

/**
 * @route   GET /api/v1/dashboard/attendance-chart
 * @desc    获取考勤图表数据
 * @access  Private
 */
router.get('/attendance-chart', dashboardController.getAttendanceChart);

/**
 * @route   GET /api/v1/dashboard/system-status
 * @desc    获取系统状态
 * @access  Private
 */
router.get('/system-status', dashboardController.getSystemStatus);

module.exports = router; 