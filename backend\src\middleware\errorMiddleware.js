const logger = require('../shared/logger');

// 详细错误分析工具
const analyzeError = (error, req) => {
  const errorInfo = {
    type: 'UNKNOWN_ERROR',
    category: 'general',
    severity: 'medium',
    isDatabase: false,
    isValidation: false,
    isAuth: false,
    details: {}
  };

  // Sequelize数据库错误分析
  if (error.name && error.name.startsWith('Sequelize')) {
    errorInfo.isDatabase = true;
    errorInfo.category = 'database';
    
    switch (error.name) {
      case 'SequelizeConnectionError':
        errorInfo.type = 'DATABASE_CONNECTION_ERROR';
        errorInfo.severity = 'critical';
        errorInfo.details = {
          host: error.parent?.hostname || 'unknown',
          port: error.parent?.port || 'unknown',
          errno: error.parent?.errno,
          syscall: error.parent?.syscall
        };
        break;
        
      case 'SequelizeDatabaseError':
        errorInfo.type = 'DATABASE_QUERY_ERROR';
        errorInfo.severity = 'high';
        if (error.message.includes('Unknown column')) {
          errorInfo.details.missingColumn = error.message.match(/'([^']+)'/)?.[1];
          errorInfo.details.table = error.message.match(/in '([^']+)'/)?.[1];
        }
        if (error.message.includes("Table") && error.message.includes("doesn't exist")) {
          errorInfo.details.missingTable = error.message.match(/Table '([^']+)'/)?.[1];
        }
        break;
        
      case 'SequelizeValidationError':
        errorInfo.type = 'DATABASE_VALIDATION_ERROR';
        errorInfo.isValidation = true;
        errorInfo.severity = 'medium';
        errorInfo.details.validationErrors = error.errors?.map(err => ({
          field: err.path,
          message: err.message,
          value: err.value
        }));
        break;
        
      case 'SequelizeUniqueConstraintError':
        errorInfo.type = 'DATABASE_UNIQUE_CONSTRAINT_ERROR';
        errorInfo.severity = 'medium';
        errorInfo.details.constraintErrors = error.errors?.map(err => ({
          field: err.path,
          value: err.value
        }));
        break;
        
      case 'SequelizeForeignKeyConstraintError':
        errorInfo.type = 'DATABASE_FOREIGN_KEY_ERROR';
        errorInfo.severity = 'medium';
        errorInfo.details.constraint = error.index;
        break;
    }
  }

  // 认证错误分析
  if (error.name === 'UnauthorizedError' || error.code === 'UNAUTHORIZED') {
    errorInfo.isAuth = true;
    errorInfo.type = 'AUTHENTICATION_ERROR';
    errorInfo.category = 'auth';
    errorInfo.severity = 'medium';
  }

  // 验证错误分析
  if (error.name === 'ValidationError' || error.code === 'VALIDATION_ERROR') {
    errorInfo.isValidation = true;
    errorInfo.type = 'INPUT_VALIDATION_ERROR';
    errorInfo.category = 'validation';
    errorInfo.severity = 'low';
  }

  return errorInfo;
};

// 生成详细错误日志
const generateDetailedErrorLog = (error, req, errorInfo) => {
  const logData = {
    // 错误基本信息
    error: {
      type: errorInfo.type,
      category: errorInfo.category,
      severity: errorInfo.severity,
      message: error.message,
      name: error.name,
      code: error.code,
      stack: error.stack
    },
    
    // 请求详情
    request: {
      method: req.method,
      url: req.originalUrl,
      path: req.path,
      params: req.params,
      query: req.query,
      headers: {
        'user-agent': req.get('User-Agent'),
        'content-type': req.get('Content-Type'),
        'authorization': req.get('Authorization') ? 'Bearer [HIDDEN]' : undefined
      },
      body: req.method !== 'GET' ? sanitizeRequestBody(req.body) : undefined,
      ip: req.ip,
      timestamp: new Date().toISOString()
    },
    
    // 用户信息（如果已认证）
    user: req.user ? {
      id: req.user.id,
      username: req.user.username,
      real_name: req.user.real_name
    } : null,
    
    // 错误分析详情
    analysis: errorInfo.details,
    
    // 建议的修复措施
    suggestions: generateErrorSuggestions(errorInfo, error)
  };

  return logData;
};

// 清理请求体敏感信息
const sanitizeRequestBody = (body) => {
  if (!body || typeof body !== 'object') return body;
  
  const sanitized = { ...body };
  
  // 隐藏敏感字段
  const sensitiveFields = ['password', 'token', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[HIDDEN]';
    }
  });
  
  return sanitized;
};

// 生成错误修复建议
const generateErrorSuggestions = (errorInfo, error) => {
  const suggestions = [];
  
  switch (errorInfo.type) {
    case 'DATABASE_CONNECTION_ERROR':
      suggestions.push('检查MySQL服务是否正在运行');
      suggestions.push('验证数据库连接配置（主机、端口、用户名、密码）');
      suggestions.push('检查网络连接和防火墙设置');
      break;
      
    case 'DATABASE_QUERY_ERROR':
      if (errorInfo.details.missingColumn) {
        suggestions.push(`添加缺失的数据库字段: ${errorInfo.details.missingColumn}`);
        suggestions.push(`运行数据库迁移脚本更新表结构`);
      }
      if (errorInfo.details.missingTable) {
        suggestions.push(`创建缺失的数据库表: ${errorInfo.details.missingTable}`);
        suggestions.push('运行数据库初始化脚本');
      }
      break;
      
    case 'DATABASE_VALIDATION_ERROR':
      suggestions.push('检查输入数据的格式和类型');
      suggestions.push('验证必填字段是否完整');
      suggestions.push('确认数据长度不超过数据库字段限制');
      break;
      
    case 'DATABASE_UNIQUE_CONSTRAINT_ERROR':
      suggestions.push('检查重复数据的唯一性约束');
      suggestions.push('使用不同的唯一标识符（如编码、邮箱等）');
      break;
      
    case 'AUTHENTICATION_ERROR':
      suggestions.push('检查用户认证token是否有效');
      suggestions.push('验证token格式是否正确');
      suggestions.push('确认用户是否已正确登录');
      break;
      
    default:
      suggestions.push('查看详细错误堆栈信息');
      suggestions.push('检查相关业务逻辑代码');
      suggestions.push('验证输入参数的有效性');
  }
  
  return suggestions;
};

// 404错误处理
const notFound = (req, res, next) => {
  const error = new Error(`接口不存在 - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// 增强的全局错误处理中间件
const errorHandler = (err, req, res, next) => {
  // 如果响应状态码为200，则设置为500
  let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  
  // 分析错误详情
  const errorInfo = analyzeError(err, req);
  
  // 生成详细错误日志
  const detailedLog = generateDetailedErrorLog(err, req, errorInfo);
  
  // 记录详细错误日志
  if (errorInfo.severity === 'critical') {
    logger.error('🚨 CRITICAL ERROR 🚨', detailedLog);
  } else if (errorInfo.severity === 'high') {
    logger.error('⚠️ HIGH SEVERITY ERROR', detailedLog);
  } else {
    logger.error('❌ API ERROR', detailedLog);
  }

  // 根据错误类型设置合适的状态码
  switch (errorInfo.type) {
    case 'DATABASE_VALIDATION_ERROR':
    case 'INPUT_VALIDATION_ERROR':
    case 'DATABASE_UNIQUE_CONSTRAINT_ERROR':
      statusCode = 400;
      break;
    case 'AUTHENTICATION_ERROR':
      statusCode = 401;
      break;
    case 'DATABASE_FOREIGN_KEY_ERROR':
      statusCode = 409;
      break;
    case 'DATABASE_CONNECTION_ERROR':
    case 'DATABASE_QUERY_ERROR':
    default:
      statusCode = 500;
  }

  // 构建错误响应
  const errorResponse = {
    success: false,
    error: {
      code: errorInfo.type,
      message: err.message || 'Internal server error',
      category: errorInfo.category,
      timestamp: new Date().toISOString()
    }
  };

  // 开发环境返回更多调试信息
  if (process.env.NODE_ENV !== 'production') {
    errorResponse.error.stack = err.stack;
    errorResponse.error.details = errorInfo.details;
    errorResponse.error.suggestions = generateErrorSuggestions(errorInfo, err);
  }

  // 特殊错误类型的额外处理
  if (err.name === 'SequelizeValidationError') {
    errorResponse.error.validationErrors = err.errors.map(error => ({
      field: error.path,
      message: error.message,
      value: error.value
    }));
  }

  res.status(statusCode).json(errorResponse);
};

module.exports = {
  notFound,
  errorHandler,
  analyzeError,
  generateDetailedErrorLog
}; 