const { Department, Employee, User } = require('../models');
const { ValidationError, NotFoundError, ConflictError } = require('../shared/errors');

class DepartmentService {
  // 获取部门树形结构
  async getDepartmentTree() {
    return await Department.getDepartmentTree();
  }

  // 获取所有部门的扁平列表
  async getAllDepartments() {
    return await Department.getAllDepartments();
  }

  // 根据ID获取部门详情
  async getDepartmentById(id) {
    const department = await Department.findByPk(id, {
      include: [
        {
          model: Department,
          as: 'parent',
          attributes: ['id', 'name', 'code']
        },
        {
          model: Department,
          as: 'children',
          attributes: ['id', 'name', 'code', 'status']
        },
        {
          model: Employee,
          as: 'manager',
          include: [{
            model: User,
            as: 'user',
            attributes: ['real_name', 'phone', 'email']
          }]
        }
      ]
    });

    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    // 获取部门员工数量
    const employeeCount = await Employee.count({
      where: { department_id: id }
    });

    return {
      ...department.toJSON(),
      employeeCount
    };
  }

  // 创建部门
  async createDepartment(departmentData, userId) {
    const {
      name,
      parent_id,
      description,
      manager_id,
      location,
      contact_phone,
      contact_email,
      budget,
      sort_order = 0
    } = departmentData;

    // 验证必填字段
    if (!name) {
      throw new ValidationError('部门名称不能为空');
    }

    // 验证上级部门（如果指定）
    if (parent_id) {
      const parentDepartment = await Department.findByPk(parent_id);
      if (!parentDepartment) {
        throw new ValidationError('指定的上级部门不存在');
      }
    }

    // 验证部门负责人（如果指定）
    if (manager_id) {
      const manager = await Employee.findByPk(manager_id);
      if (!manager) {
        throw new ValidationError('指定的部门负责人不存在');
      }
    }

    // 生成部门编码
    const code = await Department.generateDepartmentCode(parent_id);

    try {
      const department = await Department.create({
        name,
        code,
        parent_id,
        description,
        manager_id,
        location,
        contact_phone,
        contact_email,
        budget,
        sort_order
      });

      return await this.getDepartmentById(department.id);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new ConflictError('部门编码已存在');
      }
      throw error;
    }
  }

  // 更新部门信息
  async updateDepartment(id, updateData, userId) {
    const department = await Department.findByPk(id);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    const {
      name,
      parent_id,
      description,
      manager_id,
      location,
      contact_phone,
      contact_email,
      budget,
      sort_order,
      status
    } = updateData;

    // 验证上级部门（如果更新）
    if (parent_id !== undefined) {
      if (parent_id === id) {
        throw new ValidationError('不能将自己设为上级部门');
      }

      if (parent_id) {
        const parentDepartment = await Department.findByPk(parent_id);
        if (!parentDepartment) {
          throw new ValidationError('指定的上级部门不存在');
        }

        // 检查是否会形成循环引用
        const isDescendant = await this.isDescendantOf(parent_id, id);
        if (isDescendant) {
          throw new ValidationError('不能将下级部门设为上级部门');
        }
      }
    }

    // 验证部门负责人（如果更新）
    if (manager_id !== undefined) {
      if (manager_id) {
        const manager = await Employee.findByPk(manager_id);
        if (!manager) {
          throw new ValidationError('指定的部门负责人不存在');
        }
      }
    }

    await department.update({
      name,
      parent_id,
      description,
      manager_id,
      location,
      contact_phone,
      contact_email,
      budget,
      sort_order,
      status
    });

    return await this.getDepartmentById(id);
  }

  // 删除部门
  async deleteDepartment(id, userId) {
    const department = await Department.findByPk(id);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    // 检查是否可以删除
    const canDelete = await department.canDelete();
    if (!canDelete.canDelete) {
      throw new ConflictError(canDelete.reason);
    }

    await department.destroy();
    return { message: '部门删除成功' };
  }

  // 移动部门到新的上级部门
  async moveDepartment(id, newParentId, userId) {
    const department = await Department.findByPk(id);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    if (newParentId === id) {
      throw new ValidationError('不能将自己设为上级部门');
    }

    // 验证新上级部门
    if (newParentId) {
      const newParent = await Department.findByPk(newParentId);
      if (!newParent) {
        throw new ValidationError('目标上级部门不存在');
      }

      // 检查是否会形成循环引用
      const isDescendant = await this.isDescendantOf(newParentId, id);
      if (isDescendant) {
        throw new ValidationError('不能将下级部门设为上级部门');
      }
    }

    // 生成新的部门编码
    const newCode = await Department.generateDepartmentCode(newParentId);

    await department.update({
      parent_id: newParentId,
      code: newCode
    });

    return await this.getDepartmentById(id);
  }

  // 获取部门及其所有子部门的ID
  async getDepartmentAndChildrenIds(departmentId) {
    return await Department.getDepartmentAndChildrenIds(departmentId);
  }

  // 检查部门A是否是部门B的下级
  async isDescendantOf(departmentAId, departmentBId) {
    const childrenIds = await this.getDepartmentAndChildrenIds(departmentBId);
    return childrenIds.includes(departmentAId);
  }

  // 获取部门员工
  async getDepartmentEmployees(departmentId, includeSubDepartments = false) {
    const department = await Department.findByPk(departmentId);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    let departmentIds = [departmentId];
    
    if (includeSubDepartments) {
      departmentIds = await this.getDepartmentAndChildrenIds(departmentId);
    }

    const employees = await Employee.findAll({
      where: {
        department_id: departmentIds
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name', 'email', 'phone', 'avatar']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: require('../models').Position,
          as: 'position',
          attributes: ['id', 'name', 'level']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return employees;
  }

  // 部门统计信息
  async getDepartmentStats() {
    // 总部门数
    const totalDepartments = await Department.count();

    // 活跃部门数
    const activeDepartments = await Department.count({
      where: { status: 'active' }
    });

    // 各级部门统计
    const levelStats = await Department.findAll({
      attributes: [
        [Department.sequelize.fn('COUNT', Department.sequelize.col('id')), 'count'],
        [Department.sequelize.literal('CASE WHEN parent_id IS NULL THEN 1 ELSE 2 END'), 'level']
      ],
      group: [Department.sequelize.literal('CASE WHEN parent_id IS NULL THEN 1 ELSE 2 END')],
      raw: true
    });

    // 按员工数量排序的部门
    const departmentsByEmployeeCount = await Department.findAll({
      attributes: [
        'id',
        'name',
        [Department.sequelize.fn('COUNT', Department.sequelize.col('employees.id')), 'employee_count']
      ],
      include: [{
        model: Employee,
        as: 'employees',
        attributes: []
      }],
      group: ['Department.id'],
      order: [[Department.sequelize.literal('employee_count'), 'DESC']],
      limit: 10,
      raw: true
    });

    return {
      totalDepartments,
      activeDepartments,
      inactiveDepartments: totalDepartments - activeDepartments,
      levelStats,
      topDepartmentsByEmployeeCount: departmentsByEmployeeCount
    };
  }

  // 批量更新部门状态
  async batchUpdateStatus(departmentIds, status, userId) {
    if (!Array.isArray(departmentIds) || departmentIds.length === 0) {
      throw new ValidationError('部门ID列表不能为空');
    }

    if (!['active', 'inactive'].includes(status)) {
      throw new ValidationError('无效的部门状态');
    }

    const [affectedCount] = await Department.update(
      { status },
      { 
        where: { 
          id: departmentIds 
        } 
      }
    );

    return { 
      message: `成功更新${affectedCount}个部门的状态`,
      affectedCount 
    };
  }

  // 获取部门的层级路径
  async getDepartmentPath(departmentId) {
    const department = await Department.findByPk(departmentId);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    return await department.getDepartmentPath();
  }

  // 搜索部门
  async searchDepartments(keyword, options = {}) {
    const { page = 1, pageSize = 20 } = options;
    const offset = (page - 1) * pageSize;

    const where = {
      [require('sequelize').Op.or]: [
        { name: { [require('sequelize').Op.like]: `%${keyword}%` } },
        { code: { [require('sequelize').Op.like]: `%${keyword}%` } },
        { description: { [require('sequelize').Op.like]: `%${keyword}%` } }
      ]
    };

    const result = await Department.findAndCountAll({
      where,
      include: [
        {
          model: Department,
          as: 'parent',
          attributes: ['name']
        },
        {
          model: Employee,
          as: 'manager',
          include: [{
            model: User,
            as: 'user',
            attributes: ['real_name']
          }]
        }
      ],
      limit: pageSize,
      offset,
      order: [['name', 'ASC']]
    });

    return {
      departments: result.rows,
      total: result.count,
      page,
      pageSize,
      totalPages: Math.ceil(result.count / pageSize)
    };
  }
}

module.exports = new DepartmentService(); 