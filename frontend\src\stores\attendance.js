import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'

export const useAttendanceStore = defineStore('attendance', () => {
  // 状态
  const todayStatus = ref({
    date: null,
    hasClockIn: false,
    hasClockOut: false,
    clockInTime: null,
    clockOutTime: null,
    workDuration: null
  })

  const attendanceRecords = ref([])
  const attendanceStats = ref([])
  const leaveRequests = ref([])
  const pendingLeaveRequests = ref([])
  const leaveBalance = ref({})
  const loading = ref(false)

  // 计算属性
  const canClockIn = computed(() => !todayStatus.value.hasClockIn)
  const canClockOut = computed(() => todayStatus.value.hasClockIn && !todayStatus.value.hasClockOut)
  
  const todayWorkStatus = computed(() => {
    if (!todayStatus.value.hasClockIn) {
      return { text: '未打卡', type: 'warning' }
    } else if (!todayStatus.value.hasClockOut) {
      return { text: '工作中', type: 'success' }
    } else {
      return { text: '已下班', type: 'info' }
    }
  })

  // 打卡相关方法
  const clockIn = async (data = {}) => {
    try {
      loading.value = true
      const response = await axios.post('/v1/attendance/clock-in', data)

      if (response.data.success) {
        ElMessage.success('上班打卡成功')
        await getTodayStatus()
        return response.data.data
      }
    } catch (error) {
      const message = error.response?.data?.error?.message || error.response?.data?.message || '打卡失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  const clockOut = async (data = {}) => {
    try {
      loading.value = true
      const response = await axios.post('/v1/attendance/clock-out', data)

      if (response.data.success) {
        ElMessage.success('下班打卡成功')
        await getTodayStatus()
        return response.data.data
      }
    } catch (error) {
      const message = error.response?.data?.error?.message || error.response?.data?.message || '打卡失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取今日考勤状态
  const getTodayStatus = async () => {
    try {
      const response = await axios.get('/v1/attendance/today-status')

      if (response.data.success) {
        todayStatus.value = response.data.data
      }
    } catch (error) {
      console.error('获取今日考勤状态失败:', error)
    }
  }

  // 获取考勤记录
  const getAttendanceRecords = async (params = {}) => {
    try {
      loading.value = true
      const response = await axios.get('/v1/attendance/records', { params })

      if (response.data.success) {
        attendanceRecords.value = response.data.data
        return {
          records: response.data.data,
          total: response.data.total,
          page: response.data.page,
          pageSize: response.data.pageSize
        }
      }
    } catch (error) {
      const message = error.response?.data?.error?.message || error.response?.data?.message || '获取考勤记录失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取考勤统计
  const getAttendanceStats = async (params) => {
    try {
      loading.value = true
      const response = await api.get('/attendance/stats', { params })
      
      if (response.data.success) {
        attendanceStats.value = response.data.data
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取考勤统计失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 请假申请相关方法
  const submitLeaveRequest = async (data) => {
    try {
      loading.value = true
      const response = await api.post('/attendance/leave/request', data)
      
      if (response.data.success) {
        ElMessage.success('请假申请提交成功')
        await getLeaveRequests()
        return response.data.data
      }
    } catch (error) {
      const message = error.response?.data?.message || '提交请假申请失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取请假申请列表
  const getLeaveRequests = async (params = {}) => {
    try {
      loading.value = true
      const response = await api.get('/attendance/leave/requests', { params })
      
      if (response.data.success) {
        leaveRequests.value = response.data.data.requests
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取请假申请列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取待审批的请假申请
  const getPendingLeaveRequests = async () => {
    try {
      const response = await api.get('/attendance/leave/pending')
      
      if (response.data.success) {
        pendingLeaveRequests.value = response.data.data
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取待审批请假申请失败')
      throw error
    }
  }

  // 审批请假申请
  const approveLeaveRequest = async (id, data) => {
    try {
      loading.value = true
      const response = await api.patch(`/attendance/leave/requests/${id}/approve`, data)
      
      if (response.data.success) {
        ElMessage.success('审批操作成功')
        await getPendingLeaveRequests()
        return response.data.data
      }
    } catch (error) {
      const message = error.response?.data?.message || '审批操作失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 取消请假申请
  const cancelLeaveRequest = async (id) => {
    try {
      loading.value = true
      const response = await api.patch(`/attendance/leave/requests/${id}/cancel`)
      
      if (response.data.success) {
        ElMessage.success('请假申请已取消')
        await getLeaveRequests()
        return response.data.data
      }
    } catch (error) {
      const message = error.response?.data?.message || '取消请假申请失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取请假申请详情
  const getLeaveRequestDetail = async (id) => {
    try {
      const response = await api.get(`/attendance/leave/requests/${id}`)
      
      if (response.data.success) {
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取请假申请详情失败')
      throw error
    }
  }

  // 获取假期余额
  const getLeaveBalance = async (params = {}) => {
    try {
      const response = await api.get('/attendance/leave/balance', { params })
      
      if (response.data.success) {
        leaveBalance.value = response.data.data
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取假期余额失败')
      throw error
    }
  }

  // 获取请假统计
  const getLeaveStats = async (params = {}) => {
    try {
      const response = await api.get('/attendance/leave/stats', { params })
      
      if (response.data.success) {
        return response.data.data
      }
    } catch (error) {
      ElMessage.error('获取请假统计失败')
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    todayStatus.value = {
      date: null,
      hasClockIn: false,
      hasClockOut: false,
      clockInTime: null,
      clockOutTime: null,
      workDuration: null
    }
    attendanceRecords.value = []
    attendanceStats.value = []
    leaveRequests.value = []
    pendingLeaveRequests.value = []
    leaveBalance.value = {}
  }

  return {
    // 状态
    todayStatus,
    attendanceRecords,
    attendanceStats,
    leaveRequests,
    pendingLeaveRequests,
    leaveBalance,
    loading,
    
    // 计算属性
    canClockIn,
    canClockOut,
    todayWorkStatus,
    
    // 方法
    clockIn,
    clockOut,
    getTodayStatus,
    getAttendanceRecords,
    getAttendanceStats,
    submitLeaveRequest,
    getLeaveRequests,
    getPendingLeaveRequests,
    approveLeaveRequest,
    cancelLeaveRequest,
    getLeaveRequestDetail,
    getLeaveBalance,
    getLeaveStats,
    resetState
  }
}) 