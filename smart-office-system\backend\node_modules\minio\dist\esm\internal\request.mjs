import { pipeline } from "stream";
export async function request(transport, opt, body = null) {
  return new Promise((resolve, reject) => {
    const requestObj = transport.request(opt, resp => {
      resolve(resp);
    });
    if (!body || Buffer.isBuffer(body) || typeof body === 'string') {
      requestObj.on('error', e => {
        reject(e);
      }).end(body);
      return;
    }

    // pump readable stream
    pipeline(body, requestObj, err => {
      if (err) {
        reject(err);
      }
    });
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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