// 使用MySQL配置启动服务器
const path = require('path');
const fs = require('fs');

console.log('🔧 配置MySQL环境变量...');

// 读取mysql.env配置文件
const envPath = path.join(__dirname, 'mysql.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      if (key && value) {
        process.env[key] = value;
      }
    }
  });
  
  console.log('✅ MySQL环境变量已加载');
} else {
  console.error('❌ mysql.env 文件不存在');
  process.exit(1);
}

console.log('📋 当前数据库配置:');
console.log('- 数据库类型:', process.env.DB_DIALECT);
console.log('- 数据库主机:', process.env.DB_HOST);
console.log('- 数据库端口:', process.env.DB_PORT);
console.log('- 数据库名称:', process.env.DB_NAME);
console.log('- 数据库用户:', process.env.DB_USER);

console.log('\n🚀 启动智能办公系统服务器...');
require('./src/app.js'); 