'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const departments = [
      {
        id: 1,
        name: '总经理办公室',
        code: 'CEO_OFFICE',
        parent_id: null,
        level: 1,
        sort_order: 1,
        description: '公司最高管理层',
        contact_phone: '021-12345678',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 2,
        name: '人力资源部',
        code: 'HR',
        parent_id: 1,

        level: 2,
        sort_order: 1,
        description: '负责公司人力资源管理',
        contact_phone: '021-12345679',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 3,
        name: '技术部',
        code: 'TECH',
        parent_id: 1,

        level: 2,
        sort_order: 2,
        description: '负责公司技术研发',
        contact_phone: '021-12345680',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 4,
        name: '前端开发组',
        code: 'FRONTEND',
        parent_id: 3,

        level: 3,
        sort_order: 1,
        description: '前端开发团队',
        contact_phone: '021-12345681',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 5,
        name: '后端开发组',
        code: 'BACKEND',
        parent_id: 3,

        level: 3,
        sort_order: 2,
        description: '后端开发团队',
        contact_phone: '021-12345682',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 6,
        name: '测试组',
        code: 'QA',
        parent_id: 3,

        level: 3,
        sort_order: 3,
        description: '质量保证团队',
        contact_phone: '021-12345683',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 7,
        name: '市场部',
        code: 'MARKETING',
        parent_id: 1,

        level: 2,
        sort_order: 3,
        description: '负责公司市场营销',
        contact_phone: '021-12345684',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 8,
        name: '财务部',
        code: 'FINANCE',
        parent_id: 1,

        level: 2,
        sort_order: 4,
        description: '负责公司财务管理',
        contact_phone: '021-12345685',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 9,
        name: '行政部',
        code: 'ADMIN',
        parent_id: 1,

        level: 2,
        sort_order: 5,
        description: '负责公司行政管理',
        contact_phone: '021-12345686',
        contact_email: '<EMAIL>',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('departments', departments);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('departments', null, {});
  }
}; 