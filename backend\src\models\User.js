const { Model, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
// const tokenService = require('../services/tokenService');

module.exports = (sequelize) => {
  class User extends Model {
    static associate(models) {
      // 多对多关联 - 用户可以有多个角色
      User.belongsToMany(models.Role, {
        through: models.UserRole,
        foreignKey: 'user_id',
        otherKey: 'role_id',
        as: 'roles'
      });

      // 一对一关联 - 用户对应一个员工记录
      User.hasOne(models.Employee, {
        foreignKey: 'user_id',
        as: 'employee'
      });
    }

    // 静态方法：根据用户名或邮箱查找用户
    static async findByCredentials(credential) {
      try {
        return await this.findOne({
          where: {
            [sequelize.Sequelize.Op.or]: [
              { username: credential },
              { email: credential }
            ],
            status: 'active'
          }
        });
      } catch (error) {
        console.error('findByCredentials error:', error);
        // 如果关联查询失败，尝试简单查询
        return await this.findOne({
          where: {
            [sequelize.Sequelize.Op.or]: [
              { username: credential },
              { email: credential }
            ],
            status: 'active'
          }
        });
      }
    }

    // 实例方法：验证密码
    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }

    // 实例方法：更新密码
    async updatePassword(newPassword) {
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      return this.update({ password: hashedPassword });
    }

    // 实例方法：更新登录信息
    async updateLoginInfo(ip = null) {
      const updateData = {
        last_login_at: new Date(),
        login_count: this.login_count + 1
      };

      if (ip) {
        updateData.last_login_ip = ip;
      }

      return this.update(updateData);
    }

    // 实例方法：生成重置令牌
    async generateResetToken() {
      // TODO: 实现tokenService后启用
      // const resetToken = tokenService.generateResetPasswordToken({
      //   id: this.id,
      //   email: this.email
      // });

      const resetToken = 'temp_token_' + Date.now();

      await this.update({
        reset_password_token: resetToken,
        reset_password_expires: new Date(Date.now() + 60 * 60 * 1000) // 1小时后过期
      });

      return resetToken;
    }

    // 实例方法：检查用户权限
    async hasPermission(permission) {
      // 特殊处理：admin用户拥有所有权限
      if (this.username === 'admin') {
        return true;
      }
      
      if (!this.roles || this.roles.length === 0) {
        // 如果没有预加载角色，先加载
        await this.reload({
          include: [{
            model: sequelize.models.Role,
            as: 'roles',
            through: { attributes: [] }
          }]
        });
      }

      return this.roles.some(role => {
        if (!role.permissions) return false;
        
        const permissions = Array.isArray(role.permissions) 
          ? role.permissions 
          : JSON.parse(role.permissions || '[]');

        return permissions.some(p => {
          if (p === permission) return true;
          if (p.endsWith(':*')) {
            const prefix = p.slice(0, -1);
            return permission.startsWith(prefix);
          }
          return false;
        });
      });
    }

    // 实例方法：检查用户角色
    async hasRole(roleCode) {
      // 特殊处理：admin用户拥有所有角色
      if (this.username === 'admin') {
        return true;
      }
      
      if (!this.roles || this.roles.length === 0) {
        // 如果没有预加载角色，先加载
        await this.reload({
          include: [{
            model: sequelize.models.Role,
            as: 'roles',
            through: { attributes: [] }
          }]
        });
      }

      const roleCodes = Array.isArray(roleCode) ? roleCode : [roleCode];
      return this.roles.some(role => roleCodes.includes(role.code));
    }

    // 重写toJSON方法，排除敏感信息
    toJSON() {
      const values = Object.assign({}, this.get());
      delete values.password;
      delete values.reset_password_token;
      delete values.reset_password_expires;
      return values;
    }
  }

  User.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: false,  // 暂时取消唯一约束
      validate: {
        len: [3, 50]
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: false,  // 暂时取消唯一约束
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [6, 255]
      }
    },
    real_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: false  // 暂时取消唯一约束
    },
    avatar: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    gender: {
      type: DataTypes.ENUM('male', 'female', 'other'),
      allowNull: true
    },
    birth_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      allowNull: false,
      defaultValue: 'active'
    },
    email_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    reset_password_token: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    reset_password_expires: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login_ip: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    login_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    underscored: true,
    indexes: [
      { fields: ['username'] },
      { fields: ['email'] },
      { fields: ['phone'] },
      { fields: ['status'] },
      { fields: ['last_login_at'] }
    ],
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      }
    }
  });

  return User;
}; 