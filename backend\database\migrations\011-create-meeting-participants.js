'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('meeting_participants', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      meeting_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'meetings',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      employee_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      participant_type: {
        type: Sequelize.ENUM('required', 'optional', 'organizer'),
        allowNull: false,
        defaultValue: 'required',
        comment: '参与类型'
      },
      invitation_status: {
        type: Sequelize.ENUM('pending', 'accepted', 'declined', 'tentative'),
        defaultValue: 'pending',
        allowNull: false,
        comment: '邀请状态'
      },
      attendance_status: {
        type: Sequelize.ENUM('not_started', 'present', 'absent', 'late'),
        defaultValue: 'not_started',
        allowNull: false,
        comment: '出席状态'
      },
      joined_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '加入时间'
      },
      left_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '离开时间'
      },
      response_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '回复信息'
      },
      reminded_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '提醒时间'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加复合唯一索引，确保会议-员工组合唯一
    await queryInterface.addIndex('meeting_participants', ['meeting_id', 'employee_id'], {
      unique: true,
      name: 'unique_meeting_participant'
    });
    
    // 添加其他索引
    await queryInterface.addIndex('meeting_participants', ['meeting_id']);
    await queryInterface.addIndex('meeting_participants', ['employee_id']);
    await queryInterface.addIndex('meeting_participants', ['participant_type']);
    await queryInterface.addIndex('meeting_participants', ['invitation_status']);
    await queryInterface.addIndex('meeting_participants', ['attendance_status']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('meeting_participants');
  }
}; 