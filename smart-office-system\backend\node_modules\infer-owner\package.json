{"name": "infer-owner", "version": "1.0.4", "description": "Infer the owner of a path based on the owner of its nearest existing parent", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap -J test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap -J test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"mutate-fs": "^2.1.1", "tap": "^12.4.2"}, "main": "index.js", "repository": "https://github.com/npm/infer-owner", "publishConfig": {"access": "public"}, "files": ["index.js"]}