const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const { authenticateToken, requireRoles } = require('../middleware/auth');
const { OfficeSupply, SupplyRequest, User } = require('../models');
const { success, error } = require('../shared/response');

// 获取办公用品列表
router.get('/office-supplies', authenticateToken, async (req, res) => {
  try {
    const { page = 1, size = 20, search, category, status } = req.query;
    const offset = (page - 1) * size;

    const where = {};
    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { code: { [Op.like]: `%${search}%` } },
        { brand: { [Op.like]: `%${search}%` } }
      ];
    }
    if (category) where.category = category;
    if (status) where.status = status;

    const supplies = await OfficeSupply.findAndCountAll({
      where,
      limit: parseInt(size),
      offset: offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'real_name']
        }
      ]
    });

    return success(res, '获取办公用品列表成功', {
      supplies: supplies.rows,
      pagination: {
        page: parseInt(page),
        size: parseInt(size),
        total: supplies.count,
        totalPages: Math.ceil(supplies.count / size)
      }
    });
  } catch (error) {
    console.error('获取办公用品列表失败:', error);
    return error(res, '获取办公用品列表失败', 500);
  }
});

// 创建办公用品申请
router.post('/supply-requests', authenticateToken, async (req, res) => {
  try {
    const { supply_id, quantity, purpose, urgency, expected_date } = req.body;
    const userId = req.user.id;

    if (!supply_id || !quantity || quantity <= 0) {
      return error(res, '用品ID和数量不能为空且数量必须大于0', 400);
    }

    // 检查用品是否存在
    const supply = await OfficeSupply.findByPk(supply_id);
    if (!supply) {
      return error(res, '办公用品不存在', 404);
    }

    // 生成申请单号
    const now = new Date();
    const requestNo = `REQ${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

    const request = await SupplyRequest.create({
      request_no: requestNo,
      requester_id: userId,
      supply_id,
      quantity,
      unit_price: supply.unit_price,
      total_amount: supply.unit_price ? supply.unit_price * quantity : null,
      purpose,
      urgency: urgency || 'medium',
      expected_date: expected_date ? new Date(expected_date) : null
    });

    // 获取完整信息
    const fullRequest = await SupplyRequest.findByPk(request.id, {
      include: [
        { model: User, as: 'requester', attributes: ['id', 'real_name', 'email'] },
        { model: OfficeSupply, as: 'supply', attributes: ['id', 'name', 'code', 'unit'] }
      ]
    });

    return success(res, fullRequest, '申请提交成功', 201);
  } catch (error) {
    console.error('提交申请失败:', error);
    return error(res, '提交申请失败: ' + error.message, 500);
  }
});

// 获取供应请求列表
router.get('/supply-requests', authenticateToken, async (req, res) => {
  try {
    const { page = 1, size = 20, status, urgency, requester_id } = req.query;
    const userId = req.user.id;

    const whereClause = {};

    // 如果不是管理员，只能看到自己的申请
    if (req.user.role !== 'admin') {
      whereClause.requester_id = userId;
    } else if (requester_id) {
      whereClause.requester_id = requester_id;
    }

    if (status) {
      whereClause.status = status;
    }

    if (urgency) {
      whereClause.urgency = urgency;
    }

    const { count, rows } = await SupplyRequest.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'real_name', 'email']
        },
        {
          model: OfficeSupply,
          as: 'supply',
          attributes: ['id', 'name', 'code', 'unit', 'current_stock']
        },
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'real_name'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(size),
      offset: (parseInt(page) - 1) * parseInt(size)
    });

    return success(res, '获取申请列表成功', {
      requests: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        size: parseInt(size),
        pages: Math.ceil(count / parseInt(size))
      }
    });
  } catch (error) {
    console.error('获取申请列表失败:', error);
    return error(res, '获取申请列表失败', 500);
  }
});

module.exports = router; 