<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>智能办公系统</h1>
      <p>欢迎回来，{{ userStore.userInfo?.name || '用户' }}！</p>
    </div>
    
    <div class="stats-grid">
      <el-card v-for="stat in stats" :key="stat.title" class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon :size="32" :color="stat.color">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stat.value }}</h3>
            <p>{{ stat.title }}</p>
          </div>
        </div>
      </el-card>
    </div>

    <div class="content-grid">
      <el-card class="chart-card">
        <template #header>
          <h3>考勤统计</h3>
        </template>
        <div class="chart-container">
          <v-chart
            class="attendance-chart"
            :option="attendanceChartOption"
            :loading="chartLoading"
            autoresize
          />
        </div>
      </el-card>

      <el-card class="quick-actions">
        <template #header>
          <h3>快速操作</h3>
        </template>
        <div class="action-buttons">
          <el-button
            v-for="action in quickActions"
            :key="action.name"
            :type="action.type"
            @click="action.handler"
            class="action-btn"
            size="large"
          >
            <el-icon>
              <component :is="action.icon" />
            </el-icon>
            {{ action.name }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- API连接测试 -->
    <div class="api-test-section">
      <el-card>
        <template #header>
          <h3>API连接测试</h3>
        </template>
        <div class="test-buttons">
          <el-button type="primary" @click="testHealthAPI" :loading="testingHealth">
            测试健康检查API
          </el-button>
          <el-button type="success" @click="testEmployeeAPI" :loading="testingEmployee">
            测试员工列表API
          </el-button>
          <el-button type="warning" @click="testProfileAPI" :loading="testingProfile">
            测试用户信息API
          </el-button>
        </div>
        <div v-if="apiTestResults.length > 0" class="test-results">
          <h4>测试结果：</h4>
          <div v-for="result in apiTestResults" :key="result.id" class="test-result">
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.api }}
            </el-tag>
            <span class="result-message">{{ result.message }}</span>
            <span class="result-time">{{ result.time }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <div class="recent-activities">
      <el-card>
        <template #header>
          <h3>最近活动</h3>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import {
  User,
  Clock,
  Calendar,
  Document,
  Plus,
  Timer,
  VideoCamera
} from '@element-plus/icons-vue'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()
const userStore = useUserStore()

// API测试相关
const testingHealth = ref(false)
const testingEmployee = ref(false)
const testingProfile = ref(false)
const apiTestResults = ref([])

// 图表相关
const chartLoading = ref(true)
const attendanceChartOption = ref({
  title: {
    text: '最近7天考勤统计',
    left: 'center',
    textStyle: {
      fontSize: 16,
      color: '#303133'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['出勤人数', '出勤率'],
    bottom: 10
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: [
    {
      type: 'value',
      name: '人数',
      position: 'left'
    },
    {
      type: 'value',
      name: '出勤率(%)',
      position: 'right',
      max: 100
    }
  ],
  series: [
    {
      name: '出勤人数',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#409eff'
      }
    },
    {
      name: '出勤率',
      type: 'line',
      yAxisIndex: 1,
      data: [],
      itemStyle: {
        color: '#67c23a'
      },
      lineStyle: {
        width: 3
      }
    }
  ]
})

// 获取仪表盘统计数据
const fetchDashboardStats = async () => {
  try {
    const response = await axios.get('/v1/dashboard/stats')

    if (response.data.success) {
      const { stats: statsData, recentActivities: activities } = response.data.data

      // 更新统计数据
      stats.value[0].value = `${statsData.attendanceRate || 0}%`
      stats.value[1].value = statsData.totalEmployees || 0
      stats.value[2].value = statsData.todayMeetings || 0
      stats.value[3].value = statsData.pendingDocuments || 0

      // 更新最近活动
      recentActivities.value = activities.map(activity => ({
        ...activity,
        time: new Date(activity.time).toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }))
    }
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error)
    // 设置默认数据
    stats.value[0].value = '85%'
    stats.value[1].value = '30'
    stats.value[2].value = '3'
    stats.value[3].value = '12'
  }
}

// 获取考勤图表数据
const fetchAttendanceChart = async () => {
  try {
    chartLoading.value = true
    const response = await axios.get('/v1/dashboard/attendance-chart')

    if (response.data.success) {
      const { dates, attendanceCount, attendanceRate } = response.data.data

      // 更新图表配置
      attendanceChartOption.value.xAxis.data = dates
      attendanceChartOption.value.series[0].data = attendanceCount
      attendanceChartOption.value.series[1].data = attendanceRate
    }
  } catch (error) {
    console.error('获取考勤图表数据失败:', error)
    // 使用默认数据
    const defaultDates = []
    const defaultData = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      defaultDates.push(`${date.getMonth() + 1}/${date.getDate()}`)
      defaultData.push(Math.floor(Math.random() * 50) + 50)
    }
    attendanceChartOption.value.xAxis.data = defaultDates
    attendanceChartOption.value.series[0].data = defaultData
    attendanceChartOption.value.series[1].data = defaultData.map(d => Math.floor(d * 1.8))
  } finally {
    chartLoading.value = false
  }
}

// 统计数据
const stats = ref([
  {
    title: '今日出勤',
    value: '0%',
    icon: Clock,
    color: '#409eff'
  },
  {
    title: '员工总数',
    value: '0',
    icon: User,
    color: '#67c23a'
  },
  {
    title: '今日会议',
    value: '0',
    icon: Calendar,
    color: '#e6a23c'
  },
  {
    title: '待处理文档',
    value: '0',
    icon: Document,
    color: '#f56c6c'
  }
])

// 最近活动
const recentActivities = ref([])

// 快速操作
const quickActions = ref([
  {
    name: '打卡',
    icon: Timer,
    type: 'primary',
    handler: () => router.push('/attendance')
  },
  {
    name: '新建会议',
    icon: VideoCamera,
    type: 'success',
    handler: () => router.push('/meetings')
  },
  {
    name: '上传文档',
    icon: Plus,
    type: 'warning',
    handler: () => router.push('/files')
  }
])



// API测试方法
const addTestResult = (api, success, message) => {
  apiTestResults.value.unshift({
    id: Date.now(),
    api,
    success,
    message,
    time: new Date().toLocaleTimeString()
  })
  // 只保留最近10条结果
  if (apiTestResults.value.length > 10) {
    apiTestResults.value = apiTestResults.value.slice(0, 10)
  }
}

const testHealthAPI = async () => {
  testingHealth.value = true
  try {
    const response = await fetch('http://localhost:3001/health')
    const data = await response.json()

    if (response.ok) {
      addTestResult('健康检查', true, `状态: ${data.status}`)
      ElMessage.success('健康检查API连接成功')
    } else {
      addTestResult('健康检查', false, '请求失败')
      ElMessage.error('健康检查API连接失败')
    }
  } catch (error) {
    addTestResult('健康检查', false, `错误: ${error.message}`)
    ElMessage.error('健康检查API连接失败: ' + error.message)
  } finally {
    testingHealth.value = false
  }
}

const testEmployeeAPI = async () => {
  testingEmployee.value = true
  try {
    const response = await axios.get('/v1/employees')

    if (response.data.success) {
      addTestResult('员工列表', true, `获取到 ${response.data.employees?.length || 0} 条员工数据`)
      ElMessage.success('员工列表API连接成功')
    } else {
      addTestResult('员工列表', false, '请求失败')
      ElMessage.error('员工列表API连接失败')
    }
  } catch (error) {
    addTestResult('员工列表', false, `错误: ${error.message}`)
    ElMessage.error('员工列表API连接失败: ' + error.message)
  } finally {
    testingEmployee.value = false
  }
}

const testProfileAPI = async () => {
  testingProfile.value = true
  try {
    const response = await axios.get('/v1/auth/profile')

    if (response.data.success) {
      addTestResult('用户信息', true, `用户: ${response.data.data?.real_name || response.data.data?.username}`)
      ElMessage.success('用户信息API连接成功')
    } else {
      addTestResult('用户信息', false, '请求失败')
      ElMessage.error('用户信息API连接失败')
    }
  } catch (error) {
    addTestResult('用户信息', false, `错误: ${error.message}`)
    ElMessage.error('用户信息API连接失败: ' + error.message)
  } finally {
    testingProfile.value = false
  }
}

onMounted(async () => {
  // 页面加载时获取最新数据
  console.log('Dashboard mounted')

  try {
    // 并行获取仪表盘统计数据和图表数据
    await Promise.all([
      fetchDashboardStats(),
      fetchAttendanceChart()
    ])
  } catch (error) {
    console.warn('获取仪表盘数据失败:', error)
    // 不显示错误消息，因为这只是Dashboard的统计信息
  }
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  margin-bottom: 24px;
  text-align: center;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #606266;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-info p {
  margin: 4px 0 0 0;
  color: #606266;
  font-size: 14px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  margin-bottom: 24px;
  align-items: start;
}

.chart-card {
  min-height: 300px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.attendance-chart {
  height: 100%;
  width: 100%;
}

.quick-actions {
  min-height: 300px;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 16px;
  padding: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 140px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.api-test-section {
  margin-bottom: 24px;
}

.test-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.test-results {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.test-results h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.test-result {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.test-result:last-child {
  border-bottom: none;
}

.result-message {
  flex: 1;
  color: #606266;
  font-size: 14px;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

.recent-activities {
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .dashboard {
    padding: 12px;
  }

  .test-buttons {
    flex-direction: column;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}
</style> 