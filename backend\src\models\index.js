const { Sequelize } = require('sequelize');
const config = require('../config/database');
const logger = require('../shared/logger');

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 创建Sequelize实例
const sequelize = dbConfig.dialect === 'sqlite' 
  ? new Sequelize({
      dialect: 'sqlite',
      storage: dbConfig.storage,
      logging: dbConfig.logging,
      define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true
      }
    })
  : new Sequelize(
      dbConfig.database,
      dbConfig.username,
      dbConfig.password,
      {
        host: dbConfig.host,
        port: dbConfig.port,
        dialect: dbConfig.dialect,
        timezone: dbConfig.timezone,
        dialectOptions: dbConfig.dialectOptions,
        pool: dbConfig.pool,
        logging: dbConfig.logging,
        define: {
          timestamps: true,
          underscored: true,
          freezeTableName: true,
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci'
        }
      }
    );

// 数据库连接测试
sequelize.authenticate()
  .then(() => {
    logger.info(`数据库连接成功 (${env}环境)`);
  })
  .catch(err => {
    logger.error('数据库连接失败:', err);
  });

// 模型定义
const db = {
  Sequelize,
  sequelize,
  // 用户管理相关模型
  User: require('./User')(sequelize),
  Role: require('./Role')(sequelize),
  UserRole: require('./UserRole')(sequelize),
  Employee: require('./Employee')(sequelize),
  Department: require('./Department')(sequelize),
  Position: require('./Position')(sequelize),

  // 考勤管理相关模型
  AttendanceRecord: require('./Attendance')(sequelize),
  LeaveRequest: require('./LeaveRequest')(sequelize),

  // 会议管理相关模型
  MeetingRoom: require('./MeetingRoom')(sequelize),
  Meeting: require('./Meeting')(sequelize),
  MeetingParticipant: require('./MeetingParticipant')(sequelize),

  // 工作流管理相关模型
  WorkflowDefinition: require('./WorkflowDefinition')(sequelize),
  WorkflowNode: require('./WorkflowNode')(sequelize),
  WorkflowInstance: require('./WorkflowInstance')(sequelize),
  ApprovalTask: require('./ApprovalTask')(sequelize),

  // 文件管理相关模型
  File: require('./File')(sequelize),
  FileShare: require('./FileShare')(sequelize),

  // 日程安排相关模型
  Schedule: require('./Schedule')(sequelize),
  Reminder: require('./Reminder')(sequelize),

  // 办公用品管理相关模型
  OfficeSupply: require('./OfficeSupply')(sequelize),
  SupplyRequest: require('./SupplyRequest')(sequelize),
};

// 设置模型关联
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

module.exports = db; 