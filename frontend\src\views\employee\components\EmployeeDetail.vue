<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="员工详情"
    :size="600"
    direction="rtl"
    @close="handleClose"
  >
    <div v-if="employeeData" class="employee-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card">
        <template #header>
          <h3>基本信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">
            {{ employeeData.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工号">
            {{ employeeData.employeeId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ employeeData.department || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="职位">
            {{ employeeData.position || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ employeeData.email || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ employeeData.phone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ employeeData.gender === 'male' ? '男' : '女' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(employeeData.status)">
              {{ getStatusText(employeeData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="入职日期">
            {{ formatDate(employeeData.hireDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(employeeData.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 考勤统计 -->
      <el-card class="detail-card">
        <template #header>
          <h3>考勤统计</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ attendanceStats.totalDays }}</div>
              <div class="stat-label">总出勤天数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ attendanceStats.lateDays }}</div>
              <div class="stat-label">迟到次数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ attendanceStats.leaveDays }}</div>
              <div class="stat-label">请假天数</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 最近活动 -->
      <el-card class="detail-card">
        <template #header>
          <h3>最近活动</h3>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无员工数据" />
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑员工</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  employeeData: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// 模拟考勤统计数据
const attendanceStats = ref({
  totalDays: 245,
  lateDays: 3,
  leaveDays: 8
})

// 模拟最近活动数据
const recentActivities = ref([
  {
    id: 1,
    content: '完成了月度工作总结',
    time: '2024-01-15 17:30',
    color: '#409eff'
  },
  {
    id: 2,
    content: '参加了团队会议',
    time: '2024-01-15 14:00',
    color: '#67c23a'
  },
  {
    id: 3,
    content: '提交了项目进度报告',
    time: '2024-01-15 10:30',
    color: '#e6a23c'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'danger',
    leave: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    leave: '休假'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 编辑员工
const handleEdit = () => {
  emit('edit', props.employeeData)
}
</script>

<style scoped>
.employee-detail {
  padding: 0 16px;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #ebeef5;
}
</style>
