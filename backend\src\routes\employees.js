const express = require('express');
const router = express.Router();
const employeeController = require('../controllers/employeeController');
const { authenticateToken, requirePermissions } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validateRequest');
const { employeeValidators } = require('../validators/employeeValidator');

// 应用认证中间件到所有路由
router.use(authenticateToken);

// 员工列表相关路由
router.get('/', 
  requirePermissions(['employee:read']),
  employeeController.getEmployees
);

// 获取员工统计信息
router.get('/stats',
  requirePermissions(['employee:read']),
  employeeController.getEmployeeStats
);

// 导出员工数据
router.get('/export',
  requirePermissions(['employee:read']),
  employeeController.exportEmployees
);

// 获取当前用户的员工信息
router.get('/me',
  employeeController.getCurrentEmployeeInfo
);

// 获取部门树形结构（用于选择）
router.get('/departments/tree',
  requirePermissions(['employee:read']),
  employeeController.getDepartmentTree
);

// 获取职位列表（用于选择）
router.get('/positions/select',
  requirePermissions(['employee:read']),
  employeeController.getPositionsForSelect
);

// 获取可选择的上级员工列表
router.get('/managers/options',
  requirePermissions(['employee:read']),
  employeeController.getManagerOptions
);

// 批量更新员工状态
router.patch('/batch/status',
  requirePermissions(['employee:update']),
  validateRequest(employeeValidators.batchUpdateStatus),
  employeeController.batchUpdateStatus
);

// 获取员工详情
router.get('/:id',
  requirePermissions(['employee:read']),
  validateRequest(employeeValidators.getById),
  employeeController.getEmployee
);

// 创建员工
router.post('/',
  requirePermissions(['employee:create']),
  validateRequest(employeeValidators.create),
  employeeController.createEmployee
);

// 更新员工信息
router.patch('/:id',
  requirePermissions(['employee:update']),
  validateRequest(employeeValidators.update),
  employeeController.updateEmployee
);

// 删除员工
router.delete('/:id',
  requirePermissions(['employee:delete']),
  validateRequest(employeeValidators.delete),
  employeeController.deleteEmployee
);

// 获取员工下属
router.get('/:id/subordinates',
  requirePermissions(['employee:read']),
  validateRequest(employeeValidators.getById),
  employeeController.getEmployeeSubordinates
);

// 转移员工部门
router.post('/:id/transfer',
  requirePermissions(['employee:update']),
  validateRequest(employeeValidators.transfer),
  employeeController.transferEmployee
);

// 获取部门员工
router.get('/departments/:departmentId/employees',
  requirePermissions(['employee:read']),
  validateRequest(employeeValidators.getDepartmentEmployees),
  employeeController.getDepartmentEmployees
);

module.exports = router; 