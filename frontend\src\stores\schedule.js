import { defineStore } from 'pinia'
import axios from '@/utils/axios'

export const useScheduleStore = defineStore('schedule', {
  state: () => ({
    schedules: [],
    currentSchedule: null,
    todaySchedules: [],
    loading: false,
    pagination: {
      page: 1,
      size: 20,
      total: 0,
      totalPages: 0
    },
    filters: {
      type: '',
      priority: '',
      search: ''
    }
  }),

  getters: {
    // 获取今日日程数量
    todayScheduleCount() {
      return this.todaySchedules.length
    },

    // 获取按类型分组的日程
    schedulesByType() {
      return this.schedules.reduce((acc, schedule) => {
        if (!acc[schedule.type]) {
          acc[schedule.type] = []
        }
        acc[schedule.type].push(schedule)
        return acc
      }, {})
    },

    // 获取按优先级分组的日程
    schedulesByPriority() {
      return this.schedules.reduce((acc, schedule) => {
        if (!acc[schedule.priority]) {
          acc[schedule.priority] = []
        }
        acc[schedule.priority].push(schedule)
        return acc
      }, {})
    },

    // 获取即将到来的日程（未来7天）
    upcomingSchedules() {
      const now = new Date()
      const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      return this.schedules.filter(schedule => {
        const startTime = new Date(schedule.start_time)
        return startTime >= now && startTime <= nextWeek
      }).sort((a, b) => new Date(a.start_time) - new Date(b.start_time))
    },

    // 获取高优先级日程
    highPrioritySchedules() {
      return this.schedules.filter(schedule => 
        schedule.priority === 'high' || schedule.priority === 'urgent'
      )
    }
  },

  actions: {
    /**
     * 获取日程列表
     */
    async fetchSchedules(params = {}) {
      try {
        this.loading = true
        console.log('🔍 获取日程列表请求:', params)

        const queryParams = {
          page: params.page || this.pagination.page,
          size: params.size || this.pagination.size,
          ...this.filters,
          ...params
        }

        const response = await axios.get('/v1/schedules', { params: queryParams })

        console.log('✅ 日程列表响应:', response.data)

        if (response.data.success) {
          const responseData = response.data.data || {}
          
          // 确保日程数据是数组
          const scheduleData = responseData.schedules || responseData || []
          if (!Array.isArray(scheduleData)) {
            console.error('日程数据不是数组格式:', scheduleData)
            this.schedules = []
          } else {
            this.schedules = scheduleData
          }

          // 更新分页信息
          if (responseData.pagination) {
            this.pagination = {
              page: responseData.pagination.page || 1,
              size: responseData.pagination.size || 20,
              total: responseData.pagination.total || 0,
              totalPages: responseData.pagination.totalPages || 0
            }
          }

          console.log('✅ 日程数据加载成功:', this.schedules.length)
        } else {
          console.error('日程API返回失败:', response.data.message)
          this.schedules = []
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取日程列表失败:', error)
        // API失败时使用模拟数据
        this.schedules = [
          {
            id: 1,
            title: '项目会议',
            type: 'meeting',
            start_time: new Date().toISOString(),
            end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
            priority: 'high',
            status: 'pending',
            description: '讨论项目进度'
          },
          {
            id: 2,
            title: '客户拜访',
            type: 'work',
            start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            end_time: new Date(Date.now() + 26 * 60 * 60 * 1000).toISOString(),
            priority: 'medium',
            status: 'pending',
            description: '拜访重要客户'
          }
        ]
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取今日日程
     */
    async fetchTodaySchedules() {
      try {
        console.log('🔍 获取今日日程...')
        
        const response = await axios.get('/v1/schedules/today')
        
        if (response.data.success) {
          const scheduleData = response.data.data?.schedules || []
          this.todaySchedules = Array.isArray(scheduleData) ? scheduleData : []
          console.log('✅ 今日日程加载成功:', this.todaySchedules.length)
        } else {
          this.todaySchedules = []
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取今日日程失败:', error)
        this.todaySchedules = []
        throw error
      }
    },

    /**
     * 获取日程详情
     */
    async fetchScheduleById(id) {
      try {
        console.log('🔍 获取日程详情:', id)
        
        const response = await axios.get(`/v1/schedules/${id}`)
        
        if (response.data.success) {
          this.currentSchedule = response.data.data
          console.log('✅ 日程详情获取成功')
        } else {
          this.currentSchedule = null
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取日程详情失败:', error)
        this.currentSchedule = null
        throw error
      }
    },

    /**
     * 创建日程
     */
    async createSchedule(scheduleData) {
      try {
        this.loading = true
        console.log('🔧 创建日程:', scheduleData)

        const response = await axios.post('/v1/schedules', scheduleData)

        if (response.data.success) {
          const newSchedule = response.data.data
          this.schedules.unshift(newSchedule)
          console.log('✅ 日程创建成功')
          
          // 如果是今天的日程，也添加到今日日程
          const today = new Date().toDateString()
          const scheduleDate = new Date(newSchedule.start_time).toDateString()
          if (today === scheduleDate) {
            this.todaySchedules.unshift(newSchedule)
          }
        }

        return response.data
      } catch (error) {
        console.error('❌ 创建日程失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 更新日程
     */
    async updateSchedule(id, scheduleData) {
      try {
        this.loading = true
        console.log('🔧 更新日程:', id, scheduleData)

        const response = await axios.put(`/v1/schedules/${id}`, scheduleData)

        if (response.data.success) {
          const updatedSchedule = response.data.data
          
          // 更新列表中的日程
          const index = this.schedules.findIndex(s => s.id === id)
          if (index !== -1) {
            this.schedules[index] = updatedSchedule
          }

          // 更新今日日程
          const todayIndex = this.todaySchedules.findIndex(s => s.id === id)
          if (todayIndex !== -1) {
            this.todaySchedules[todayIndex] = updatedSchedule
          }

          // 更新当前日程
          if (this.currentSchedule?.id === id) {
            this.currentSchedule = updatedSchedule
          }

          console.log('✅ 日程更新成功')
        }

        return response.data
      } catch (error) {
        console.error('❌ 更新日程失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除日程
     */
    async deleteSchedule(id) {
      try {
        this.loading = true
        console.log('🗑️ 删除日程:', id)

        const response = await axios.delete(`/v1/schedules/${id}`)

        if (response.data.success) {
          // 从列表中移除
          this.schedules = this.schedules.filter(s => s.id !== id)
          this.todaySchedules = this.todaySchedules.filter(s => s.id !== id)
          
          // 清除当前日程
          if (this.currentSchedule?.id === id) {
            this.currentSchedule = null
          }

          console.log('✅ 日程删除成功')
        }

        return response.data
      } catch (error) {
        console.error('❌ 删除日程失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 更新过滤条件
     */
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    /**
     * 清除当前日程
     */
    clearCurrentSchedule() {
      this.currentSchedule = null
    },

    /**
     * 重置状态
     */
    resetState() {
      this.schedules = []
      this.currentSchedule = null
      this.todaySchedules = []
      this.pagination = {
        page: 1,
        size: 20,
        total: 0,
        totalPages: 0
      }
      this.filters = {
        type: '',
        priority: '',
        search: ''
      }
    }
  }
})
