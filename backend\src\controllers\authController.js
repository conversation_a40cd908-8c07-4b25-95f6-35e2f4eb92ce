const authService = require('../services/authService');
const { validationResult } = require('express-validator');

class AuthController {
  /**
   * 用户注册
   */
  async register(req, res) {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const result = await authService.register(req.body);

      res.status(201).json({
        success: true,
        data: result.user,
        message: result.message
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '注册失败'
      });
    }
  }

  /**
   * 用户登录
   */
  async login(req, res) {
    try {
      // 添加调试日志
      console.log('🔍 Login request received:', {
        body: req.body,
        headers: req.headers['content-type'],
        method: req.method
      });

      // 检查验证错误
      const errors = validationResult(req);
      console.log('🔍 Validation errors:', errors.array());
      
      if (!errors.isEmpty()) {
        console.log('❌ Validation failed:', errors.array());
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const { credential, password, username } = req.body;
      const loginCredential = credential || username; // 兼容两种字段名
      const ip = req.ip || req.connection.remoteAddress;

      console.log('🔍 Login attempt:', {
        credential: loginCredential,
        hasPassword: !!password,
        ip
      });

      const result = await authService.login(loginCredential, password, ip);

      // 设置httpOnly cookie存储刷新令牌
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
      });

      res.json({
        success: true,
        data: {
          user: result.user,
          accessToken: result.tokens.accessToken,
          tokenType: result.tokens.tokenType,
          expiresIn: result.tokens.expiresIn
        },
        message: result.message
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(401).json({
        success: false,
        message: error.message || '登录失败'
      });
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(req, res) {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          message: '刷新令牌缺失'
        });
      }

      const result = await authService.refreshToken(refreshToken);

      // 更新refreshToken cookie
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
      });

      res.json({
        success: true,
        data: {
          accessToken: result.tokens.accessToken,
          tokenType: result.tokens.tokenType,
          expiresIn: result.tokens.expiresIn
        },
        message: result.message
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        message: error.message || '令牌刷新失败'
      });
    }
  }

  /**
   * 用户登出
   */
  async logout(req, res) {
    try {
      // 清除refreshToken cookie
      res.clearCookie('refreshToken');

      // TODO: 可以将当前令牌加入黑名单
      // await tokenBlacklistService.addToBlacklist(req.token);

      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: '登出失败'
      });
    }
  }

  /**
   * 更改密码
   */
  async changePassword(req, res) {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const userId = req.user.id;

      const result = await authService.changePassword(userId, currentPassword, newPassword);

      res.json({
        success: true,
        message: result.message
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '密码更改失败'
      });
    }
  }

  /**
   * 请求重置密码
   */
  async requestPasswordReset(req, res) {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const { email } = req.body;
      const result = await authService.requestPasswordReset(email);

      res.json({
        success: true,
        message: result.message,
        ...(process.env.NODE_ENV === 'development' && { resetToken: result.resetToken })
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        success: false,
        message: '重置密码请求失败'
      });
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(req, res) {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const { resetToken, newPassword } = req.body;
      const result = await authService.resetPassword(resetToken, newPassword);

      res.json({
        success: true,
        message: result.message
      });
    } catch (error) {
      console.error('Password reset error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '密码重置失败'
      });
    }
  }

  /**
   * 获取当前用户信息
   */
  async getProfile(req, res) {
    try {
      const user = await authService.getUserProfile(req.user.id);

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(404).json({
        success: false,
        message: error.message || '获取用户信息失败'
      });
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(req, res) {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
      }

      const userId = req.user.id;
      const result = await authService.updateUserProfile(userId, req.body);

      res.json({
        success: true,
        data: result.user,
        message: result.message
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '用户信息更新失败'
      });
    }
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(req, res) {
    try {
      const { token } = req.params;
      const result = await authService.verifyEmail(token);

      res.json({
        success: true,
        message: result.message
      });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '邮箱验证失败'
      });
    }
  }

  /**
   * 检查认证状态
   */
  async checkAuth(req, res) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未认证'
        });
      }

      res.json({
        success: true,
        data: {
          authenticated: true,
          user: req.user
        }
      });
    } catch (error) {
      console.error('Check auth error:', error);
      res.status(500).json({
        success: false,
        message: '认证状态检查失败'
      });
    }
  }
}

module.exports = new AuthController(); 