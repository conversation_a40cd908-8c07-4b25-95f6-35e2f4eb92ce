const { AttendanceRecord, Employee, User } = require('../models');
const { ValidationError, BusinessError } = require('../shared/errors');
const logger = require('../shared/logger');

class AttendanceService {
  // 员工打卡
  async clockIn(employeeId, data) {
    try {
      const { location, latitude, longitude, remarks } = data;

      // 检查员工是否存在
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new ValidationError('员工不存在');
      }

      // 检查今日是否已经上班打卡
      const todayRecords = await AttendanceRecord.getTodayAttendance(employeeId);
      const hasClockIn = todayRecords.some(record => record.clock_type === 'clock_in');
      
      if (hasClockIn) {
        throw new BusinessError('今日已完成上班打卡');
      }

      // 创建上班打卡记录
      const clockTime = new Date();
      const attendanceRecord = await AttendanceRecord.create({
        employee_id: employeeId,
        clock_type: 'clock_in',
        clock_time: clockTime,
        location,
        latitude,
        longitude,
        remarks,
        status: this._calculateClockStatus('clock_in', clockTime)
      });

      logger.info(`员工 ${employeeId} 完成上班打卡`, { employeeId, clockTime });

      return attendanceRecord;
    } catch (error) {
      logger.error('上班打卡失败:', error);
      throw error;
    }
  }

  // 员工下班打卡
  async clockOut(employeeId, data) {
    try {
      const { location, latitude, longitude, remarks } = data;

      // 检查员工是否存在
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new ValidationError('员工不存在');
      }

      // 检查今日是否已完成上班打卡
      const todayRecords = await AttendanceRecord.getTodayAttendance(employeeId);
      const hasClockIn = todayRecords.some(record => record.clock_type === 'clock_in');
      const hasClockOut = todayRecords.some(record => record.clock_type === 'clock_out');
      
      if (!hasClockIn) {
        throw new BusinessError('请先完成上班打卡');
      }

      if (hasClockOut) {
        throw new BusinessError('今日已完成下班打卡');
      }

      // 创建下班打卡记录
      const clockTime = new Date();
      const attendanceRecord = await AttendanceRecord.create({
        employee_id: employeeId,
        clock_type: 'clock_out',
        clock_time: clockTime,
        location,
        latitude,
        longitude,
        remarks,
        status: this._calculateClockStatus('clock_out', clockTime)
      });

      logger.info(`员工 ${employeeId} 完成下班打卡`, { employeeId, clockTime });

      return attendanceRecord;
    } catch (error) {
      logger.error('下班打卡失败:', error);
      throw error;
    }
  }

  // 获取考勤记录
  async getAttendanceRecords(options) {
    try {
      return await AttendanceRecord.getAttendanceRecords(options);
    } catch (error) {
      logger.error('获取考勤记录失败:', error);
      throw error;
    }
  }

  // 获取员工今日考勤状态
  async getTodayAttendanceStatus(employeeId) {
    try {
      const todayRecords = await AttendanceRecord.getTodayAttendance(employeeId);
      
      const clockIn = todayRecords.find(record => record.clock_type === 'clock_in');
      const clockOut = todayRecords.find(record => record.clock_type === 'clock_out');

      return {
        date: new Date().toISOString().split('T')[0],
        hasClockIn: !!clockIn,
        hasClockOut: !!clockOut,
        clockInTime: clockIn ? clockIn.clock_time : null,
        clockOutTime: clockOut ? clockOut.clock_time : null,
        workDuration: this._calculateWorkDuration(clockIn, clockOut)
      };
    } catch (error) {
      logger.error('获取今日考勤状态失败:', error);
      throw error;
    }
  }

  // 获取考勤统计报表
  async getAttendanceStats(employeeId, startDate, endDate) {
    try {
      return await AttendanceRecord.getAttendanceStats(employeeId, startDate, endDate);
    } catch (error) {
      logger.error('获取考勤统计失败:', error);
      throw error;
    }
  }

  // 获取部门考勤汇总
  async getDepartmentAttendanceStats(departmentId, date) {
    try {
      // 获取部门所有员工
      const employees = await Employee.findAll({
        where: { department_id: departmentId },
        include: [{
          model: User,
          as: 'user',
          attributes: ['real_name']
        }]
      });

      const stats = [];
      
      for (const employee of employees) {
        const todayRecords = await AttendanceRecord.findAll({
          where: {
            employee_id: employee.id,
            clock_time: {
              [require('sequelize').Op.between]: [
                new Date(date + ' 00:00:00'),
                new Date(date + ' 23:59:59')
              ]
            }
          },
          order: [['clock_time', 'ASC']]
        });

        const clockIn = todayRecords.find(record => record.clock_type === 'clock_in');
        const clockOut = todayRecords.find(record => record.clock_type === 'clock_out');

        stats.push({
          employeeId: employee.id,
          employeeName: employee.user.real_name,
          hasClockIn: !!clockIn,
          hasClockOut: !!clockOut,
          clockInTime: clockIn ? clockIn.clock_time : null,
          clockOutTime: clockOut ? clockOut.clock_time : null,
          status: this._getAttendanceStatus(clockIn, clockOut)
        });
      }

      return stats;
    } catch (error) {
      logger.error('获取部门考勤汇总失败:', error);
      throw error;
    }
  }

  // 计算打卡状态（正常、迟到、早退、加班）
  _calculateClockStatus(clockType, clockTime) {
    const hour = clockTime.getHours();
    const minute = clockTime.getMinutes();
    const timeInMinutes = hour * 60 + minute;

    if (clockType === 'clock_in') {
      // 上班时间：9:00，迟到阈值：9:15
      const workStartTime = 9 * 60; // 9:00
      const lateThreshold = 9 * 60 + 15; // 9:15
      
      if (timeInMinutes <= workStartTime) {
        return 'normal';
      } else if (timeInMinutes <= lateThreshold) {
        return 'late';
      } else {
        return 'late';
      }
    } else {
      // 下班时间：18:00，早退阈值：17:30，加班阈值：19:00
      const workEndTime = 18 * 60; // 18:00
      const earlyThreshold = 17 * 60 + 30; // 17:30
      const overtimeThreshold = 19 * 60; // 19:00
      
      if (timeInMinutes < earlyThreshold) {
        return 'early';
      } else if (timeInMinutes >= overtimeThreshold) {
        return 'overtime';
      } else {
        return 'normal';
      }
    }
  }

  // 计算工作时长
  _calculateWorkDuration(clockIn, clockOut) {
    if (!clockIn || !clockOut) {
      return null;
    }

    const startTime = new Date(clockIn.clock_time);
    const endTime = new Date(clockOut.clock_time);
    const durationMs = endTime - startTime;
    const durationHours = Math.round(durationMs / (1000 * 60 * 60) * 100) / 100;

    return durationHours;
  }

  // 获取考勤状态
  _getAttendanceStatus(clockIn, clockOut) {
    if (!clockIn && !clockOut) {
      return 'absent';
    } else if (clockIn && !clockOut) {
      return 'incomplete';
    } else if (clockIn && clockOut) {
      const hasLate = clockIn.status === 'late';
      const hasEarly = clockOut.status === 'early';
      const hasOvertime = clockOut.status === 'overtime';
      
      if (hasLate && hasEarly) {
        return 'late_and_early';
      } else if (hasLate) {
        return 'late';
      } else if (hasEarly) {
        return 'early';
      } else if (hasOvertime) {
        return 'overtime';
      } else {
        return 'normal';
      }
    }
    
    return 'unknown';
  }
}

module.exports = new AttendanceService(); 