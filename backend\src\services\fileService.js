const { File, FileShare, Employee, User } = require('../models');
const { ValidationError } = require('../shared/errors');
const logger = require('../shared/logger');
const {
  minioClient,
  DEFAULT_BUCKET,
  generateObjectName,
  getPresignedUrl,
  getPresignedUploadUrl,
  fileExists,
  deleteFile,
  copyFile,
  getFileInfo
} = require('../config/minio');
const multer = require('multer');
const path = require('path');
const mime = require('mime-types');

class FileService {
  // 配置multer用于文件上传
  static getMulterConfig() {
    const storage = multer.memoryStorage();
    
    const fileFilter = (req, file, cb) => {
      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/csv',
        'application/zip', 'application/x-rar-compressed'
      ];

      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new ValidationError('不支持的文件类型'), false);
      }
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: 100 * 1024 * 1024 // 100MB
      }
    });
  }

  // 上传文件
  async uploadFile(fileBuffer, originalName, userId, options = {}) {
    try {
      const {
        folderPath = '/',
        description = '',
        isPublic = false,
        tags = []
      } = options;

      // 生成对象名称
      const objectName = generateObjectName(userId, originalName);
      
      // 获取文件信息
      const fileSize = fileBuffer.length;
      const mimeType = mime.lookup(originalName) || 'application/octet-stream';
      const fileType = this.getFileType(mimeType);

      // 上传到MinIO
      await minioClient.putObject(DEFAULT_BUCKET, objectName, fileBuffer, fileSize, {
        'Content-Type': mimeType,
        'X-Uploaded-By': userId.toString(),
        'X-Original-Name': originalName
      });

      // 保存文件元数据到数据库
      const file = await File.create({
        original_name: originalName,
        object_name: objectName,
        file_size: fileSize,
        file_type: fileType,
        mime_type: mimeType,
        folder_path: folderPath,
        description,
        uploaded_by: userId,
        is_public: isPublic,
        tags,
        metadata: {
          upload_ip: null, // 可从请求中获取
          user_agent: null
        }
      });

      logger.info(`文件上传成功: ${originalName} (${objectName})`);
      return await this.getFileById(file.id);
    } catch (error) {
      logger.error('文件上传失败:', error);
      throw error;
    }
  }

  // 获取文件类型
  getFileType(mimeType) {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'spreadsheet';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'presentation';
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) return 'archive';
    if (mimeType.startsWith('text/')) return 'text';
    return 'other';
  }

  // 获取用户文件列表
  async getUserFiles(userId, options = {}) {
    try {
      return await File.getUserFiles(userId, options);
    } catch (error) {
      logger.error('获取用户文件列表失败:', error);
      throw error;
    }
  }

  // 获取共享文件列表
  async getSharedFiles(userId, options = {}) {
    try {
      return await File.getSharedFiles(userId, options);
    } catch (error) {
      logger.error('获取共享文件列表失败:', error);
      throw error;
    }
  }

  // 获取文件详情
  async getFileById(fileId) {
    try {
      const file = await File.findByPk(fileId, {
        include: [
          {
            model: Employee,
            as: 'uploader',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: File,
            as: 'versions',
            order: [['version', 'DESC']]
          }
        ]
      });

      if (!file) {
        throw new ValidationError('文件不存在');
      }

      return file;
    } catch (error) {
      logger.error('获取文件详情失败:', error);
      throw error;
    }
  }

  // 获取文件下载URL
  async getDownloadUrl(fileId, userId, expiresIn = 3600) {
    try {
      const file = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = file.canAccessBy(userId);
      if (!permissions.includes('read')) {
        throw new ValidationError('无权限访问此文件');
      }

      // 生成预签名URL
      const downloadUrl = await getPresignedUrl(file.object_name, expiresIn);
      
      return {
        downloadUrl,
        fileName: file.original_name,
        fileSize: file.file_size,
        mimeType: file.mime_type
      };
    } catch (error) {
      logger.error('获取文件下载URL失败:', error);
      throw error;
    }
  }

  // 删除文件
  async deleteFile(fileId, userId) {
    try {
      const file = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = file.canAccessBy(userId);
      if (!permissions.includes('delete')) {
        throw new ValidationError('无权限删除此文件');
      }

      // 软删除文件记录
      await file.softDelete();

      // 删除MinIO中的文件（可选，也可以保留用于恢复）
      // await deleteFile(file.object_name);

      logger.info(`文件已删除: ${file.original_name} (ID: ${fileId})`);
      return true;
    } catch (error) {
      logger.error('删除文件失败:', error);
      throw error;
    }
  }

  // 创建文件分享
  async createFileShare(fileId, userId, shareData) {
    try {
      const file = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = file.canAccessBy(userId);
      if (!permissions.includes('share')) {
        throw new ValidationError('无权限分享此文件');
      }

      const share = await FileShare.createShare(fileId, userId, shareData);
      
      // 生成分享链接
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const shareUrl = share.generateShareUrl(baseUrl);

      logger.info(`文件分享已创建: ${file.original_name} (分享ID: ${share.id})`);
      
      return {
        ...share.toJSON(),
        shareUrl
      };
    } catch (error) {
      logger.error('创建文件分享失败:', error);
      throw error;
    }
  }

  // 通过分享令牌访问文件
  async accessSharedFile(shareToken, accessCode = null, userId = null) {
    try {
      const share = await FileShare.getByShareToken(shareToken, accessCode);
      
      if (!share) {
        throw new ValidationError('分享链接无效或已过期');
      }

      // 检查访问权限
      if (!share.canAccessBy(userId)) {
        throw new ValidationError('无权限访问此分享文件');
      }

      // 记录访问日志
      if (userId) {
        await share.logAccess(userId, 'view');
      }

      return {
        file: share.file,
        share: {
          id: share.id,
          permissions: share.permissions,
          shareMessage: share.share_message,
          sharer: share.sharer
        }
      };
    } catch (error) {
      logger.error('访问分享文件失败:', error);
      throw error;
    }
  }

  // 获取分享文件的下载URL
  async getSharedFileDownloadUrl(shareToken, accessCode = null, userId = null) {
    try {
      const shareData = await this.accessSharedFile(shareToken, accessCode, userId);
      const { file, share } = shareData;

      // 检查下载权限
      if (!share.permissions.includes('read')) {
        throw new ValidationError('无权限下载此文件');
      }

      // 生成下载URL
      const downloadUrl = await getPresignedUrl(file.object_name, 3600);

      // 记录下载日志
      if (userId) {
        const shareRecord = await FileShare.findByPk(share.id);
        await shareRecord.logAccess(userId, 'download');
      }

      return {
        downloadUrl,
        fileName: file.original_name,
        fileSize: file.file_size,
        mimeType: file.mime_type
      };
    } catch (error) {
      logger.error('获取分享文件下载URL失败:', error);
      throw error;
    }
  }

  // 获取用户的分享记录
  async getUserShares(userId, options = {}) {
    try {
      return await FileShare.getUserShares(userId, options);
    } catch (error) {
      logger.error('获取用户分享记录失败:', error);
      throw error;
    }
  }

  // 停用分享
  async deactivateShare(shareId, userId) {
    try {
      const share = await FileShare.findByPk(shareId, {
        include: [{
          model: File,
          as: 'file'
        }]
      });

      if (!share) {
        throw new ValidationError('分享记录不存在');
      }

      // 检查权限（只有分享者可以停用）
      if (share.shared_by !== userId) {
        throw new ValidationError('无权限停用此分享');
      }

      await share.deactivate();
      
      logger.info(`分享已停用: ${share.file.original_name} (分享ID: ${shareId})`);
      return true;
    } catch (error) {
      logger.error('停用分享失败:', error);
      throw error;
    }
  }

  // 更新文件信息
  async updateFile(fileId, userId, updateData) {
    try {
      const file = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = file.canAccessBy(userId);
      if (!permissions.includes('write')) {
        throw new ValidationError('无权限修改此文件');
      }

      const {
        description,
        folderPath,
        isPublic,
        tags
      } = updateData;

      await file.update({
        description: description !== undefined ? description : file.description,
        folder_path: folderPath !== undefined ? folderPath : file.folder_path,
        is_public: isPublic !== undefined ? isPublic : file.is_public,
        tags: tags !== undefined ? tags : file.tags
      });

      logger.info(`文件信息已更新: ${file.original_name} (ID: ${fileId})`);
      return await this.getFileById(fileId);
    } catch (error) {
      logger.error('更新文件信息失败:', error);
      throw error;
    }
  }

  // 创建文件新版本
  async createFileVersion(fileId, userId, fileBuffer, versionNote = '') {
    try {
      const originalFile = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = originalFile.canAccessBy(userId);
      if (!permissions.includes('write')) {
        throw new ValidationError('无权限创建文件版本');
      }

      // 生成新的对象名称
      const newObjectName = generateObjectName(userId, originalFile.original_name, 'versions');
      
      // 上传新版本到MinIO
      await minioClient.putObject(
        DEFAULT_BUCKET,
        newObjectName,
        fileBuffer,
        fileBuffer.length,
        {
          'Content-Type': originalFile.mime_type,
          'X-Uploaded-By': userId.toString(),
          'X-Original-Name': originalFile.original_name,
          'X-Version-Note': versionNote
        }
      );

      // 创建新版本记录
      const newVersion = await originalFile.createVersion(newObjectName, versionNote);

      logger.info(`文件新版本已创建: ${originalFile.original_name} v${newVersion.version}`);
      return await this.getFileById(newVersion.id);
    } catch (error) {
      logger.error('创建文件版本失败:', error);
      throw error;
    }
  }

  // 获取文件统计信息
  async getFileStats(userId) {
    try {
      const [storageUsage, fileTypeStats, shareStats] = await Promise.all([
        File.getStorageUsage(userId),
        File.getFileTypeStats(userId),
        FileShare.getShareStats(userId)
      ]);

      return {
        storage: storageUsage,
        fileTypes: fileTypeStats,
        shares: shareStats
      };
    } catch (error) {
      logger.error('获取文件统计信息失败:', error);
      throw error;
    }
  }

  // 搜索文件
  async searchFiles(userId, keyword, options = {}) {
    try {
      const searchOptions = {
        ...options,
        keyword
      };

      return await this.getUserFiles(userId, searchOptions);
    } catch (error) {
      logger.error('搜索文件失败:', error);
      throw error;
    }
  }

  // 获取文件预览信息
  async getFilePreview(fileId, userId) {
    try {
      const file = await this.getFileById(fileId);
      
      // 检查权限
      const permissions = file.canAccessBy(userId);
      if (!permissions.includes('read')) {
        throw new ValidationError('无权限预览此文件');
      }

      // 根据文件类型返回预览信息
      const previewData = {
        id: file.id,
        name: file.original_name,
        type: file.file_type,
        mimeType: file.mime_type,
        size: file.file_size,
        canPreview: this.canPreview(file.file_type),
        previewUrl: null
      };

      // 如果支持预览，生成预览URL
      if (previewData.canPreview) {
        previewData.previewUrl = await getPresignedUrl(file.object_name, 3600);
      }

      return previewData;
    } catch (error) {
      logger.error('获取文件预览失败:', error);
      throw error;
    }
  }

  // 检查文件是否支持预览
  canPreview(fileType) {
    const previewableTypes = ['image', 'pdf', 'text'];
    return previewableTypes.includes(fileType);
  }

  // 清理过期分享
  async cleanupExpiredShares() {
    try {
      const cleanedCount = await FileShare.cleanupExpiredShares();
      logger.info(`已清理 ${cleanedCount} 个过期分享`);
      return cleanedCount;
    } catch (error) {
      logger.error('清理过期分享失败:', error);
      throw error;
    }
  }
}

module.exports = new FileService(); 