const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Role extends Model {
    static associate(models) {
      // 多对多关联 - 用户可以有多个角色，角色可以分配给多个用户
      Role.belongsToMany(models.User, {
        through: models.UserRole,
        foreignKey: 'role_id',
        otherKey: 'user_id',
        as: 'users'
      });
    }

    // 静态方法：根据代码查找角色
    static async findByCode(code) {
      return this.findOne({
        where: { code, is_active: true }
      });
    }

    // 静态方法：获取活跃角色列表
    static async getActiveRoles() {
      return this.findAll({
        where: { is_active: true },
        order: [['name', 'ASC']]
      });
    }

    // 实例方法：检查是否有指定权限
    hasPermission(permission) {
      if (!this.permissions || !Array.isArray(this.permissions)) {
        return false;
      }
      return this.permissions.includes(permission);
    }

    // 实例方法：添加权限
    async addPermission(permission) {
      const permissions = this.permissions || [];
      if (!permissions.includes(permission)) {
        permissions.push(permission);
        return this.update({ permissions });
      }
      return this;
    }

    // 实例方法：移除权限
    async removePermission(permission) {
      const permissions = this.permissions || [];
      const index = permissions.indexOf(permission);
      if (index > -1) {
        permissions.splice(index, 1);
        return this.update({ permissions });
      }
      return this;
    }
  }

  Role.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [1, 50]
      }
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [1, 20],
        isAlphanumeric: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: '角色权限JSON配置'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Role',
    tableName: 'roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  return Role;
};
