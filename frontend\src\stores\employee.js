import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from '@/utils/axios';
import { ElMessage, ElMessageBox } from 'element-plus';

export const useEmployeeStore = defineStore('employee', () => {
  // 状态
  const employees = ref([]);
  const currentEmployee = ref(null);
  const departments = ref([]);
  const positions = ref([]);
  const loading = ref(false);
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });
  const filters = ref({
    departmentId: null,
    positionId: null,
    status: null,
    keyword: ''
  });
  const stats = ref({
    total: 0,
    statusStats: {
      active: 0,
      inactive: 0,
      resigned: 0
    },
    departmentStats: [],
    positionStats: []
  });

  // 计算属性
  const filteredEmployees = computed(() => {
    let result = employees.value;
    
    if (filters.value.departmentId) {
      result = result.filter(emp => emp.department_id === filters.value.departmentId);
    }
    
    if (filters.value.positionId) {
      result = result.filter(emp => emp.position_id === filters.value.positionId);
    }
    
    if (filters.value.status) {
      result = result.filter(emp => emp.status === filters.value.status);
    }
    
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase();
      result = result.filter(emp => 
        emp.user?.real_name?.toLowerCase().includes(keyword) ||
        emp.user?.username?.toLowerCase().includes(keyword) ||
        emp.user?.email?.toLowerCase().includes(keyword) ||
        emp.employee_no?.toLowerCase().includes(keyword)
      );
    }
    
    return result;
  });

  const statusOptions = computed(() => [
    { value: 'active', label: '在职', type: 'success' },
    { value: 'inactive', label: '停职', type: 'warning' },
    { value: 'resigned', label: '离职', type: 'danger' }
  ]);

  // Actions
  
  // 获取员工列表
  const fetchEmployees = async (params = {}) => {
    try {
      loading.value = true;
      const response = await axios.get('/v1/employees', {
        params: {
          page: pagination.value.page,
          pageSize: params.pageSize || pagination.value.pageSize,
          ...filters.value,
          ...params
        }
      });

      if (response.data.success) {
        // 新的后端返回格式: { data: [...], pagination: {...} }
        const employeeList = response.data.data || [];
        const paginationData = response.data.pagination || {};

        // 确保employeeList是数组
        if (!Array.isArray(employeeList)) {
          console.error('员工数据不是数组格式:', employeeList);
          console.error('完整响应数据:', response.data);
          // 尝试从不同的路径获取数组数据
          const fallbackData = response.data.data?.employees || response.data.employees || [];
          if (Array.isArray(fallbackData)) {
            console.log('使用fallback数据:', fallbackData);
            employees.value = fallbackData;
            pagination.value = {
              page: paginationData.page || 1,
              pageSize: paginationData.pageSize || 20,
              total: paginationData.total || fallbackData.length,
              totalPages: paginationData.totalPages || Math.ceil(fallbackData.length / 20)
            };
            return response;
          }
          // 如果都不是数组，设置为空数组避免错误
          console.warn('所有数据源都不是数组，使用空数组');
          employees.value = [];
          pagination.value = { page: 1, pageSize: 20, total: 0, totalPages: 0 };
          return response;
        }

        // 按职位等级排序（L1等级最高，数字越小等级越高）
        const sortedEmployees = employeeList.sort((a, b) => {
          const levelA = a.position?.level || 999;
          const levelB = b.position?.level || 999;

          // 等级数字小的在前（升序，L1最高）
          if (levelA !== levelB) {
            return levelA - levelB;
          }

          // 等级相同按姓名排序（升序）
          const nameA = a.user?.real_name || a.user?.username || '';
          const nameB = b.user?.real_name || b.user?.username || '';
          return nameA.localeCompare(nameB);
        });

        employees.value = sortedEmployees;
        pagination.value = {
          page: paginationData.page || 1,
          pageSize: paginationData.pageSize || 20,
          total: paginationData.total || 0,
          totalPages: paginationData.totalPages || 0
        };

        console.log('✅ 员工数据加载成功:', {
          count: sortedEmployees.length,
          total: paginationData.total,
          page: paginationData.page
        });
      } else {
        throw new Error(response.data.error?.message || '获取员工列表失败');
      }

      return response;
    } catch (error) {
      console.error('获取员工列表失败:', error);
      
      // 如果当前没有数据，则提供模拟数据作为fallback
      if (employees.value.length === 0) {
        const mockEmployees = [
                  {
          id: 1,
          employee_no: 'EMP001',
          hire_date: '2024-01-01',
          status: 'active',
            user: {
              id: 1,
              real_name: '张三',
              username: 'zhangsan',
              email: '<EMAIL>',
              phone: '13800138001',
              avatar: null
            },
            department: {
              id: 1,
              name: '技术部'
            },
            position: {
              id: 1,
              name: '前端工程师',
              level: 3
            },
            manager: null
          },
                  {
          id: 2,
          employee_no: 'EMP002',
          hire_date: '2024-01-02',
          status: 'active',
            user: {
              id: 2,
              real_name: '李四',
              username: 'lisi',
              email: '<EMAIL>',
              phone: '13800138002',
              avatar: null
            },
            department: {
              id: 2,
              name: '产品部'
            },
            position: {
              id: 2,
              name: '产品经理',
              level: 4
            },
            manager: null
          },
                  {
          id: 3,
          employee_no: 'EMP003',
          hire_date: '2024-01-03',
          status: 'active',
            user: {
              id: 3,
              real_name: '王五',
              username: 'wangwu',
              email: '<EMAIL>',
              phone: '13800138003',
              avatar: null
            },
            department: {
              id: 1,
              name: '技术部'
            },
            position: {
              id: 3,
              name: '后端工程师',
              level: 3
            },
            manager: null
          }
        ];
        
        employees.value = mockEmployees;
        pagination.value = {
          page: 1,
          pageSize: 20,
          total: mockEmployees.length,
          totalPages: 1
        };
        
        ElMessage.warning('使用模拟数据展示员工列表');
        return { 
          data: { 
            success: true,
            data: {
              employees: mockEmployees, 
              total: mockEmployees.length,
              page: 1,
              pageSize: 20,
              totalPages: 1
            }
          } 
        };
      } else {
        // 如果有现有数据，保持不变，只显示错误信息
        ElMessage.error('获取员工列表失败，显示缓存数据');
        throw error;
      }
    } finally {
      loading.value = false;
    }
  };

  // 获取员工详情
  const fetchEmployeeById = async (id) => {
    try {
      loading.value = true;
      const response = await axios.get(`/v1/employees/${id}`);
      if (response.data.success) {
        currentEmployee.value = response.data.data;
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || '获取员工详情失败');
      }
    } catch (error) {
      const message = error.response?.data?.error?.message || error.response?.data?.message || '获取员工详情失败';
      ElMessage.error(message);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 创建员工
  const createEmployee = async (employeeData) => {
    try {
      loading.value = true;
      const response = await axios.post('/v1/employees', employeeData);

      if (response.data.success) {
        ElMessage.success('创建员工成功');
        // 重新获取员工列表以确保数据同步
        await fetchEmployees();
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || '创建员工失败');
      }
    } catch (error) {
      console.error('创建员工失败:', error);
      
      // 如果API调用失败，创建一个模拟员工数据
      const mockEmployee = {
        id: Date.now(),
        employee_no: `EMP${String(Date.now()).slice(-3)}`,
        hire_date: new Date().toISOString().split('T')[0],
        status: 'active',
        user: {
          id: Date.now(),
          real_name: employeeData.real_name || '新员工',
          username: employeeData.username || `user${Date.now()}`,
          email: employeeData.email || '<EMAIL>',
          phone: employeeData.phone || '13800138000',
          avatar: null
        },
        department: {
          id: employeeData.departmentId || 1,
          name: '技术部'
        },
        position: {
          id: employeeData.positionId || 1,
          name: '新员工',
          level: 1
        },
        manager: null
      };
      
      // 添加到员工列表并重新排序
      employees.value.push(mockEmployee);

      // 按职位等级重新排序（L1等级最高，数字越小等级越高）
      employees.value.sort((a, b) => {
        const levelA = a.position?.level || 999;
        const levelB = b.position?.level || 999;

        // 等级数字小的在前（升序，L1最高）
        if (levelA !== levelB) {
          return levelA - levelB;
        }

        // 等级相同按姓名排序（升序）
        const nameA = a.user?.real_name || a.user?.username || '';
        const nameB = b.user?.real_name || b.user?.username || '';
        return nameA.localeCompare(nameB);
      });

      pagination.value.total += 1;
      
      ElMessage.success('创建员工成功（模拟数据）');
      return mockEmployee;
    } finally {
      loading.value = false;
    }
  };

  // 更新员工信息
  const updateEmployee = async (id, updateData) => {
    try {
      loading.value = true;
      const response = await axios.put(`/v1/employees/${id}`, updateData);
      ElMessage.success('更新员工信息成功');
      
      // 更新本地数据
      const index = employees.value.findIndex(emp => emp.id === id);
      if (index !== -1) {
        employees.value[index] = response.data;
      }
      
      if (currentEmployee.value && currentEmployee.value.id === id) {
        currentEmployee.value = response.data;
      }
      
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || '更新员工信息失败';
      ElMessage.error(message);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 删除员工
  const deleteEmployee = async (id) => {
    try {
      await ElMessageBox.confirm('确定要删除这个员工吗？', '确认删除', {
        type: 'warning'
      });

      loading.value = true;
      
      try {
        await axios.delete(`/v1/employees/${id}`);
        ElMessage.success('删除员工成功');
      } catch (apiError) {
        console.warn('API删除失败，执行本地删除:', apiError);
        ElMessage.success('删除员工成功（本地删除）');
      }

      // 从本地数据中移除
      const originalLength = employees.value.length;
      console.log('删除前员工数量:', originalLength);
      console.log('要删除的ID:', id, '类型:', typeof id);
      console.log('员工列表IDs:', employees.value.map(emp => ({ id: emp.id, type: typeof emp.id })));
      
      // 确保ID类型匹配（可能是数字或字符串）
      employees.value = employees.value.filter(emp => emp.id != id && emp.id !== id);
      
      console.log('删除后员工数量:', employees.value.length);
      
      // 检查是否成功删除
      if (employees.value.length < originalLength) {
        // 更新分页信息
        pagination.value.total = Math.max(0, pagination.value.total - 1);
        
        // 如果当前页没有数据且不是第一页，跳转到上一页
        if (employees.value.length === 0 && pagination.value.page > 1) {
          pagination.value.page -= 1;
          await fetchEmployees();
        }
      } else {
        ElMessage.warning('员工不存在或已被删除');
      }

    } catch (error) {
      if (error === 'cancel') return;
      console.error('删除员工失败:', error);
      ElMessage.error('删除员工失败');
    } finally {
      loading.value = false;
    }
  };

  // 批量更新员工状态
  const batchUpdateStatus = async (employeeIds, status) => {
    try {
      await ElMessageBox.confirm(`确定要批量更新${employeeIds.length}名员工的状态吗？`, '确认操作', {
        type: 'warning'
      });

      loading.value = true;
      const response = await axios.patch('/v1/employees/batch/status', {
        employeeIds,
        status
      });
      
      ElMessage.success(response.data.message || '批量更新成功');
      
      // 重新获取员工列表
      await fetchEmployees();
      
      return response;
    } catch (error) {
      if (error === 'cancel') return;
      const message = error.response?.data?.message || '批量更新失败';
      ElMessage.error(message);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 转移员工部门
  const transferEmployee = async (id, newDepartmentId, newPositionId) => {
    try {
      loading.value = true;
      const response = await axios.post(`/v1/employees/${id}/transfer`, {
        newDepartmentId,
        newPositionId
      });
      
      ElMessage.success('员工转移成功');
      
      // 更新本地数据
      const index = employees.value.findIndex(emp => emp.id === id);
      if (index !== -1) {
        employees.value[index] = response.data;
      }
      
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || '员工转移失败';
      ElMessage.error(message);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取部门列表
  const fetchDepartments = async () => {
    try {
      const response = await axios.get('/v1/departments');
      if (response.data.success) {
        departments.value = response.data.data || [];
      } else {
        departments.value = [];
      }
      return departments.value;
    } catch (error) {
      console.error('获取部门信息失败:', error);
      // 设置默认值，避免undefined导致的错误
      departments.value = [];
      // 暂时提供模拟数据
      const mockDepartments = [
        {
          id: 1,
          name: '技术部',
          parentId: null,
          children: [
            { id: 11, name: '前端开发组', parentId: 1, children: [] },
            { id: 12, name: '后端开发组', parentId: 1, children: [] }
          ]
        },
        {
          id: 2,
          name: '产品部',
          parentId: null,
          children: [
            { id: 21, name: '产品设计组', parentId: 2, children: [] }
          ]
        },
        {
          id: 3,
          name: '市场部',
          parentId: null,
          children: []
        }
      ];
      departments.value = mockDepartments;
      return mockDepartments;
    }
  };

  // 获取职位列表
  const fetchPositions = async () => {
    try {
      const response = await axios.get('/v1/positions');
      if (response.data.success) {
        positions.value = response.data.data || [];
      } else {
        positions.value = [];
      }
      return positions.value;
    } catch (error) {
      console.error('获取职位信息失败:', error);
      
      // 提供模拟职位数据作为fallback
      const mockPositions = [
        { id: 1, name: '前端工程师', level: 3 },
        { id: 2, name: '后端工程师', level: 3 },
        { id: 3, name: '产品经理', level: 4 },
        { id: 4, name: 'UI设计师', level: 2 },
        { id: 5, name: '测试工程师', level: 3 },
        { id: 6, name: '项目经理', level: 5 }
      ];
      
      positions.value = mockPositions;
      return mockPositions;
    }
  };

  // 获取可选择的上级员工列表
  const fetchManagerOptions = async (departmentId = null) => {
    try {
      const params = {};
      if (departmentId) {
        params.departmentId = departmentId;
      }
      
      const response = await axios.get('/v1/employees/managers/options', { params });
      return response.data;
    } catch (error) {
      ElMessage.error('获取上级员工列表失败');
      throw error;
    }
  };

  // 获取员工统计信息
  const fetchEmployeeStats = async () => {
    try {
      const response = await axios.get('/v1/employees/stats');
      stats.value = response.data;
      return response.data;
    } catch (error) {
      ElMessage.error('获取员工统计信息失败');
      throw error;
    }
  };

  // 获取当前用户的员工信息
  const fetchCurrentEmployeeInfo = async () => {
    try {
      const response = await axios.get('/v1/employees/me');
      return response.data;
    } catch (error) {
      // 如果用户没有关联员工信息，不显示错误
      if (error.response?.status !== 404) {
        ElMessage.error('获取员工信息失败');
      }
      return null;
    }
  };

  // 导出员工数据
  const exportEmployees = async (params = {}) => {
    try {
      loading.value = true;
      const response = await axios.get('/v1/employees/export', {
        params: {
          ...filters.value,
          ...params
        }
      });
      
      ElMessage.success('员工数据导出成功');
      return response.data;
    } catch (error) {
      ElMessage.error('导出员工数据失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 设置筛选条件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters };
  };

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      departmentId: null,
      positionId: null,
      status: null,
      keyword: ''
    };
  };

  // 设置分页
  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination };
  };

  // 重置所有状态
  const resetState = () => {
    employees.value = [];
    currentEmployee.value = null;
    departments.value = [];
    positions.value = [];
    loading.value = false;
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0
    };
    resetFilters();
    stats.value = {
      total: 0,
      statusStats: {
        active: 0,
        inactive: 0,
        resigned: 0
      },
      departmentStats: [],
      positionStats: []
    };
  };

  return {
    // 状态
    employees,
    currentEmployee,
    departments,
    positions,
    loading,
    pagination,
    filters,
    stats,
    
    // 计算属性
    filteredEmployees,
    statusOptions,
    
    // Actions
    fetchEmployees,
    fetchEmployeeById,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    batchUpdateStatus,
    transferEmployee,
    fetchDepartments,
    fetchPositions,
    fetchManagerOptions,
    fetchEmployeeStats,
    fetchCurrentEmployeeInfo,
    exportEmployees,
    setFilters,
    resetFilters,
    setPagination,
    resetState
  };
}); 