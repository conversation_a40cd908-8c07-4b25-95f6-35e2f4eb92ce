/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015, 2016 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import * as Crypto from "crypto";
import JSONParser from 'json-stream';
import Through2 from 'through2';
import { isFunction } from "./internal/helper.mjs";
import * as xmlParsers from "./xml-parsers.mjs";

// getConcater returns a stream that concatenates the input and emits
// the concatenated output when 'end' has reached. If an optional
// parser function is passed upon reaching the 'end' of the stream,
// `parser(concatenated_data)` will be emitted.
export function getConcater(parser, emitError) {
  var objectMode = false;
  var bufs = [];
  if (parser && !isFunction(parser)) {
    throw new TypeError('parser should be of type "function"');
  }
  if (parser) {
    objectMode = true;
  }
  return Through2({
    objectMode
  }, function (chunk, enc, cb) {
    bufs.push(chunk);
    cb();
  }, function (cb) {
    if (emitError) {
      cb(parser(Buffer.concat(bufs).toString()));
      // cb(e) would mean we have to emit 'end' by explicitly calling this.push(null)
      this.push(null);
      return;
    }
    if (bufs.length) {
      if (parser) {
        this.push(parser(Buffer.concat(bufs).toString()));
      } else {
        this.push(Buffer.concat(bufs));
      }
    }
    cb();
  });
}

// A through stream that calculates md5sum and sha256sum
export function getHashSummer(enableSHA256) {
  var md5 = Crypto.createHash('md5');
  var sha256 = Crypto.createHash('sha256');
  return Through2.obj(function (chunk, enc, cb) {
    if (enableSHA256) {
      sha256.update(chunk);
    } else {
      md5.update(chunk);
    }
    cb();
  }, function (cb) {
    var md5sum = '';
    var sha256sum = '';
    if (enableSHA256) {
      sha256sum = sha256.digest('hex');
    } else {
      md5sum = md5.digest('base64');
    }
    var hashData = {
      md5sum,
      sha256sum
    };
    this.push(hashData);
    this.push(null);
    cb();
  });
}

// Following functions return a stream object that parses XML
// and emits suitable Javascript objects.

// Parses CopyObject response.
export function getCopyObjectTransformer() {
  return getConcater(xmlParsers.parseCopyObject);
}

// Parses listMultipartUploads response.
export function getListMultipartTransformer() {
  return getConcater(xmlParsers.parseListMultipart);
}

// Parses listObjects response.
export function getListObjectsTransformer() {
  return getConcater(xmlParsers.parseListObjects);
}

// Parses listObjects response.
export function getListObjectsV2Transformer() {
  return getConcater(xmlParsers.parseListObjectsV2);
}

// Parses listObjects with metadata response.
export function getListObjectsV2WithMetadataTransformer() {
  return getConcater(xmlParsers.parseListObjectsV2WithMetadata);
}

// Parses completeMultipartUpload response.
export function getCompleteMultipartTransformer() {
  return getConcater(xmlParsers.parseCompleteMultipart);
}

// Parses GET/SET BucketNotification response
export function getBucketNotificationTransformer() {
  return getConcater(xmlParsers.parseBucketNotification);
}

// Parses a notification.
export function getNotificationTransformer() {
  // This will parse and return each object.
  return new JSONParser();
}
export function bucketVersioningTransformer() {
  return getConcater(xmlParsers.parseBucketVersioningConfig);
}
export function getTagsTransformer() {
  return getConcater(xmlParsers.parseTagging);
}
export function lifecycleTransformer() {
  return getConcater(xmlParsers.parseLifecycleConfig);
}
export function objectLockTransformer() {
  return getConcater(xmlParsers.parseObjectLockConfig);
}
export function objectRetentionTransformer() {
  return getConcater(xmlParsers.parseObjectRetentionConfig);
}
export function bucketEncryptionTransformer() {
  return getConcater(xmlParsers.parseBucketEncryptionConfig);
}
export function objectLegalHoldTransformer() {
  return getConcater(xmlParsers.parseObjectLegalHoldConfig);
}
export function uploadPartTransformer() {
  return getConcater(xmlParsers.uploadPartParser);
}
export function selectObjectContentTransformer() {
  return getConcater();
}
export function removeObjectsTransformer() {
  return getConcater(xmlParsers.removeObjectsParser);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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