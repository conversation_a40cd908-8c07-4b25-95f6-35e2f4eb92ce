import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'

export const useMeetingStore = defineStore('meeting', () => {
  // 状态
  const meetings = ref([])
  const meetingRooms = ref([])
  const currentMeeting = ref(null)
  const myInvitations = ref([])
  const calendarEvents = ref([])
  const loading = ref(false)
  const totalMeetings = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const upcomingMeetings = computed(() => {
    const now = new Date()
    return meetings.value.filter(meeting => 
      new Date(meeting.start_time) > now && meeting.status === 'scheduled'
    ).slice(0, 5)
  })

  const todayMeetings = computed(() => {
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)
    
    return meetings.value.filter(meeting => {
      const meetingDate = new Date(meeting.start_time)
      return meetingDate >= todayStart && meetingDate < todayEnd
    })
  })

  const pendingInvitations = computed(() => {
    return myInvitations.value.filter(invitation => invitation.status === 'invited')
  })

  // 获取会议室列表
  const fetchMeetingRooms = async (params = {}) => {
    try {
      loading.value = true
      const response = await axios.get('/v1/meetings/rooms', { params })

      if (response.data.success) {
        const roomData = response.data.data || []

        // 确保会议室数据是数组
        if (!Array.isArray(roomData)) {
          console.error('会议室数据不是数组格式:', roomData);
          console.error('完整响应数据:', response.data);
          meetingRooms.value = [];
        } else {
          meetingRooms.value = roomData;
        }
      } else {
        // 如果API失败，使用模拟数据
        meetingRooms.value = [
          { id: 1, name: '大会议室A', location: '1楼', capacity: 20, equipment: '投影仪,白板,音响', description: '适合大型会议' },
          { id: 2, name: '小会议室B', location: '2楼', capacity: 8, equipment: '投影仪,白板', description: '适合小组讨论' },
          { id: 3, name: '视频会议室C', location: '3楼', capacity: 12, equipment: '视频会议设备,投影仪,音响', description: '适合远程会议' }
        ]
      }

      return meetingRooms.value
    } catch (error) {
      console.error('获取会议室列表失败:', error)
      ElMessage.error('获取会议室列表失败')
      // API失败时使用模拟数据
      meetingRooms.value = [
        { id: 1, name: '大会议室A', location: '1楼', capacity: 20, equipment: '投影仪,白板,音响', description: '适合大型会议' },
        { id: 2, name: '小会议室B', location: '2楼', capacity: 8, equipment: '投影仪,白板', description: '适合小组讨论' },
        { id: 3, name: '视频会议室C', location: '3楼', capacity: 12, equipment: '视频会议设备,投影仪,音响', description: '适合远程会议' }
      ]
      return meetingRooms.value
    } finally {
      loading.value = false
    }
  }

  // 检查会议室可用性
  const checkRoomAvailability = async (roomId, startTime, endTime, excludeMeetingId = null) => {
    try {
      const params = {
        startTime,
        endTime
      }
      if (excludeMeetingId) {
        params.excludeMeetingId = excludeMeetingId
      }

      const response = await axios.get(`/v1/meetings/rooms/${roomId}/availability`, { params })
      return response.data
    } catch (error) {
      console.error('检查会议室可用性失败:', error)
      ElMessage.error('检查会议室可用性失败')
      throw error
    }
  }

  // 获取会议列表
  const fetchMeetings = async (params = {}) => {
    try {
      loading.value = true
      const queryParams = {
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      }
      
      const response = await axios.get('/v1/meetings', { params: queryParams })

      if (response.data.success) {
        const responseData = response.data.data || {}
        const paginationData = response.data.pagination || responseData.pagination || {}

        const meetingData = responseData.meetings || responseData || []

        // 确保会议数据是数组
        if (!Array.isArray(meetingData)) {
          console.error('会议数据不是数组格式:', meetingData);
          console.error('完整响应数据:', response.data);
          // 尝试从不同的路径获取数组数据
          const fallbackData = response.data.data?.meetings || response.data.meetings || [];
          if (Array.isArray(fallbackData)) {
            console.log('使用fallback会议数据:', fallbackData);
            meetings.value = fallbackData;
          } else {
            console.warn('所有会议数据源都不是数组，使用空数组');
            meetings.value = [];
          }
        } else {
          meetings.value = meetingData;
        }

        totalMeetings.value = paginationData.total || 0
        currentPage.value = paginationData.page || 1

        console.log('✅ 会议数据加载成功:', {
          count: meetings.value.length,
          total: totalMeetings.value,
          page: currentPage.value
        })
      } else {
        // API失败时使用模拟数据
        const mockMeetings = [
          {
            id: 1,
            title: '项目评审会议',
            organizer: { real_name: '系统管理员' },
            room: { name: '大会议室A', location: '1楼' },
            start_time: new Date().toISOString(),
            end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
            status: 'scheduled',
            description: '项目进度评审',
            participants: []
          }
        ]
        meetings.value = mockMeetings
        totalMeetings.value = mockMeetings.length
      }

      return { data: meetings.value, total: totalMeetings.value }
    } catch (error) {
      console.error('获取会议列表失败:', error)
      // API失败时使用模拟数据
      const mockMeetings = [
        {
          id: 1,
          title: '项目评审会议',
          organizer: { real_name: '系统管理员' },
          room: { name: '大会议室A', location: '1楼' },
          start_time: new Date().toISOString(),
          end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
          status: 'scheduled',
          description: '项目进度评审',
          participants: []
        }
      ]
      meetings.value = mockMeetings
      totalMeetings.value = mockMeetings.length
      return { data: meetings.value, total: totalMeetings.value }
    } finally {
      loading.value = false
    }
  }

  // 获取日历事件
  const fetchCalendarEvents = async (startDate, endDate) => {
    try {
      const response = await axios.get('/v1/meetings/calendar/events', {
        params: { startDate, endDate }
      })
      calendarEvents.value = response.data
      return response.data
    } catch (error) {
      console.error('获取日历事件失败:', error)
      ElMessage.error('获取日历事件失败')
      throw error
    }
  }

  // 创建会议
  const createMeeting = async (meetingData) => {
    try {
      loading.value = true
      const response = await axios.post('/v1/meetings', meetingData)

      if (response.data.success) {
        // 更新本地状态
        const newMeeting = response.data.data
        meetings.value.unshift(newMeeting)
        totalMeetings.value += 1

        ElMessage.success('创建会议成功')
        return newMeeting
      }
    } catch (error) {
      console.error('创建会议失败:', error)
      const message = error.response?.data?.error?.message || error.response?.data?.message || '创建会议失败'
      ElMessage.error(message)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取会议详情
  const fetchMeetingById = async (meetingId) => {
    try {
      loading.value = true
      const response = await axios.get(`/v1/meetings/${meetingId}`)
      currentMeeting.value = response.data
      return response.data
    } catch (error) {
      console.error('获取会议详情失败:', error)
      ElMessage.error('获取会议详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新会议
  const updateMeeting = async (meetingId, updateData) => {
    try {
      loading.value = true
      const response = await axios.put(`/v1/meetings/${meetingId}`, updateData)
      
      // 更新本地状态
      const updatedMeeting = response.data
      const index = meetings.value.findIndex(m => m.id === meetingId)
      if (index !== -1) {
        meetings.value[index] = updatedMeeting
      }
      if (currentMeeting.value?.id === meetingId) {
        currentMeeting.value = updatedMeeting
      }
      
      ElMessage.success('更新会议成功')
      return updatedMeeting
    } catch (error) {
      console.error('更新会议失败:', error)
      ElMessage.error(error.response?.data?.message || '更新会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 取消会议
  const cancelMeeting = async (meetingId, reason = '') => {
    try {
      loading.value = true
      await axios.post(`/v1/meetings/${meetingId}/cancel`, { reason })

      // 更新本地状态
      const index = meetings.value.findIndex(m => m.id === meetingId)
      if (index !== -1) {
        meetings.value[index].status = 'cancelled'
        meetings.value[index].cancellation_reason = reason
      }

      ElMessage.success('取消会议成功')
    } catch (error) {
      console.error('取消会议失败:', error)
      ElMessage.error(error.response?.data?.message || '取消会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除会议
  const deleteMeeting = async (meetingId) => {
    try {
      loading.value = true
      const response = await axios.delete(`/v1/meetings/${meetingId}`)

      if (response.data.success) {
        // 从本地数据中移除
        const index = meetings.value.findIndex(m => m.id === meetingId)
        if (index !== -1) {
          meetings.value.splice(index, 1)
          totalMeetings.value -= 1
        }

        ElMessage.success('删除会议成功')
        return response.data
      } else {
        throw new Error(response.data.message || '删除会议失败')
      }
    } catch (error) {
      console.error('删除会议失败:', error)
      ElMessage.error(error.response?.data?.message || '删除会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 开始会议
  const startMeeting = async (meetingId) => {
    try {
      loading.value = true
      const response = await axios.post(`/v1/meetings/${meetingId}/start`)
      
      // 更新本地状态
      const updatedMeeting = response.data
      const index = meetings.value.findIndex(m => m.id === meetingId)
      if (index !== -1) {
        meetings.value[index] = updatedMeeting
      }
      
      ElMessage.success('开始会议成功')
      return updatedMeeting
    } catch (error) {
      console.error('开始会议失败:', error)
      ElMessage.error(error.response?.data?.message || '开始会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 结束会议
  const endMeeting = async (meetingId) => {
    try {
      loading.value = true
      const response = await axios.post(`/v1/meetings/${meetingId}/end`)
      
      // 更新本地状态
      const updatedMeeting = response.data
      const index = meetings.value.findIndex(m => m.id === meetingId)
      if (index !== -1) {
        meetings.value[index] = updatedMeeting
      }
      
      ElMessage.success('结束会议成功')
      return updatedMeeting
    } catch (error) {
      console.error('结束会议失败:', error)
      ElMessage.error(error.response?.data?.message || '结束会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取我的会议邀请
  const fetchMyInvitations = async (status = null) => {
    try {
      loading.value = true
      const params = status ? { status } : {}
      const response = await axios.get('/v1/meetings/my/invitations', { params })
      myInvitations.value = response.data
      return response.data
    } catch (error) {
      console.error('获取会议邀请失败:', error)
      ElMessage.error('获取会议邀请失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 回应会议邀请
  const respondToInvitation = async (meetingId, status, reason = '') => {
    try {
      loading.value = true
      await axios.post(`/v1/meetings/${meetingId}/respond`, { status, reason })
      
      // 更新本地状态
      const invitation = myInvitations.value.find(inv => inv.meeting_id === meetingId)
      if (invitation) {
        invitation.status = status
        invitation.response_reason = reason
        invitation.responded_at = new Date().toISOString()
      }
      
      ElMessage.success('回应会议邀请成功')
    } catch (error) {
      console.error('回应会议邀请失败:', error)
      ElMessage.error(error.response?.data?.message || '回应会议邀请失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取我组织的会议
  const fetchMyMeetings = async (params = {}) => {
    try {
      loading.value = true
      const response = await axios.get('/v1/meetings/my/organized', { params })
      return response.data
    } catch (error) {
      console.error('获取我组织的会议失败:', error)
      ElMessage.error('获取我组织的会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取我参加的会议
  const fetchMyParticipatingMeetings = async (params = {}) => {
    try {
      loading.value = true
      const response = await axios.get('/v1/meetings/my/participating', { params })
      return response.data
    } catch (error) {
      console.error('获取我参加的会议失败:', error)
      ElMessage.error('获取我参加的会议失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取会议统计
  const fetchMeetingStats = async (startDate, endDate) => {
    try {
      const response = await axios.get('/v1/meetings/stats', {
        params: { startDate, endDate }
      })
      return response.data
    } catch (error) {
      console.error('获取会议统计失败:', error)
      ElMessage.error('获取会议统计失败')
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    meetings.value = []
    meetingRooms.value = []
    currentMeeting.value = null
    myInvitations.value = []
    calendarEvents.value = []
    loading.value = false
    totalMeetings.value = 0
    currentPage.value = 1
  }

  // 设置分页
  const setPage = (page) => {
    currentPage.value = page
  }

  const setPageSize = (size) => {
    pageSize.value = size
    currentPage.value = 1
  }

  return {
    // 状态
    meetings,
    meetingRooms,
    currentMeeting,
    myInvitations,
    calendarEvents,
    loading,
    totalMeetings,
    currentPage,
    pageSize,

    // 计算属性
    upcomingMeetings,
    todayMeetings,
    pendingInvitations,

    // 方法
    fetchMeetingRooms,
    checkRoomAvailability,
    fetchMeetings,
    fetchCalendarEvents,
    createMeeting,
    fetchMeetingById,
    updateMeeting,
    cancelMeeting,
    deleteMeeting,
    startMeeting,
    endMeeting,
    fetchMyInvitations,
    respondToInvitation,
    fetchMyMeetings,
    fetchMyParticipatingMeetings,
    fetchMeetingStats,
    resetState,
    setPage,
    setPageSize
  }
}) 