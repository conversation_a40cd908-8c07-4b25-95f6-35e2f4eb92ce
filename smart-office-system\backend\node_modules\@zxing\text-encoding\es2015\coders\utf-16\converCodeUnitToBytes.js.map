{"version": 3, "file": "converCodeUnitToBytes.js", "sourceRoot": "", "sources": ["../../../../src/coders/utf-16/converCodeUnitToBytes.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,UAAU,sBAAsB,CAAC,SAAiB,EAAE,OAAgB;IACxE,kCAAkC;IAClC,MAAM,KAAK,GAAG,SAAS,IAAI,CAAC,CAAC;IAE7B,sCAAsC;IACtC,MAAM,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC;IAEjC,qCAAqC;IACrC,2CAA2C;IAC3C,IAAI,OAAO;QACT,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxB,6CAA6C;IAC7C,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxB,CAAC"}