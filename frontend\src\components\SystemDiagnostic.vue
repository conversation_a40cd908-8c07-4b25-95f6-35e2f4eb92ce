<template>
  <div class="system-diagnostic">
    <!-- 系统状态卡片 -->
    <el-card class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>系统状态诊断</span>
          <el-button 
            type="primary" 
            size="small" 
            :loading="checking"
            @click="runDiagnostic"
          >
            {{ checking ? '检测中...' : '重新检测' }}
          </el-button>
        </div>
      </template>

      <!-- API健康状态 -->
      <div class="status-item">
        <div class="status-label">
          <el-icon :class="getStatusIcon(apiStatus.status)">
            <component :is="getStatusIconComponent(apiStatus.status)" />
          </el-icon>
          API服务状态
        </div>
        <div class="status-value">
          <el-tag :type="getStatusTagType(apiStatus.status)">
            {{ apiStatus.message }}
          </el-tag>
          <span v-if="apiStatus.lastCheck" class="status-time">
            最后检查: {{ formatTime(apiStatus.lastCheck) }}
          </span>
        </div>
      </div>

      <!-- 网络连接状态 -->
      <div class="status-item">
        <div class="status-label">
          <el-icon :class="getStatusIcon(networkStatus.status)">
            <component :is="getStatusIconComponent(networkStatus.status)" />
          </el-icon>
          网络连接状态
        </div>
        <div class="status-value">
          <el-tag :type="getStatusTagType(networkStatus.status)">
            {{ networkStatus.message }}
          </el-tag>
          <span v-if="networkStatus.speed" class="status-detail">
            响应时间: {{ networkStatus.speed }}ms
          </span>
        </div>
      </div>

      <!-- 认证状态 -->
      <div class="status-item">
        <div class="status-label">
          <el-icon :class="getStatusIcon(authStatus.status)">
            <component :is="getStatusIconComponent(authStatus.status)" />
          </el-icon>
          用户认证状态
        </div>
        <div class="status-value">
          <el-tag :type="getStatusTagType(authStatus.status)">
            {{ authStatus.message }}
          </el-tag>
          <span v-if="authStatus.user" class="status-detail">
            用户: {{ authStatus.user }}
          </span>
        </div>
      </div>

      <!-- 数据库状态 -->
      <div class="status-item">
        <div class="status-label">
          <el-icon :class="getStatusIcon(databaseStatus.status)">
            <component :is="getStatusIconComponent(databaseStatus.status)" />
          </el-icon>
          数据库状态
        </div>
        <div class="status-value">
          <el-tag :type="getStatusTagType(databaseStatus.status)">
            {{ databaseStatus.message }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 错误历史记录 -->
    <el-card v-if="errorHistory.length > 0" class="error-history-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>最近错误记录</span>
          <el-button 
            type="danger" 
            size="small" 
            @click="clearErrorHistory"
          >
            清空记录
          </el-button>
        </div>
      </template>

      <div class="error-list">
        <div 
          v-for="(error, index) in errorHistory.slice(0, 5)" 
          :key="index"
          class="error-item"
        >
          <div class="error-time">{{ formatTime(error.timestamp) }}</div>
          <div class="error-type">{{ error.type }}</div>
          <div class="error-message">{{ error.message }}</div>
          <el-button 
            v-if="error.canRetry"
            type="text" 
            size="small"
            @click="retryFailedOperation(error)"
          >
            重试
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 诊断建议 -->
    <el-card v-if="diagnosticSuggestions.length > 0" class="suggestions-card" shadow="hover">
      <template #header>
        <span>诊断建议</span>
      </template>

      <div class="suggestions-list">
        <div 
          v-for="(suggestion, index) in diagnosticSuggestions" 
          :key="index"
          class="suggestion-item"
        >
          <el-icon class="suggestion-icon">
            <InfoFilled />
          </el-icon>
          <span class="suggestion-text">{{ suggestion.message }}</span>
          <el-button 
            v-if="suggestion.action"
            type="primary" 
            size="small"
            @click="executeSuggestion(suggestion)"
          >
            {{ suggestion.actionText }}
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  CircleCheckFilled, 
  CircleCloseFilled, 
  WarningFilled,
  InfoFilled,
  Loading
} from '@element-plus/icons-vue'
import { apiUtils } from '@/utils/axios'
import { useUserStore } from '@/stores/user'

// Props
const props = defineProps({
  autoCheck: {
    type: Boolean,
    default: true
  },
  checkInterval: {
    type: Number,
    default: 30000 // 30秒
  }
})

// 响应式数据
const checking = ref(false)
const userStore = useUserStore()

// 系统状态
const apiStatus = reactive({
  status: 'unknown', // success, warning, error, unknown
  message: '检测中...',
  lastCheck: null
})

const networkStatus = reactive({
  status: 'unknown',
  message: '检测中...',
  speed: null
})

const authStatus = reactive({
  status: 'unknown',
  message: '检测中...',
  user: null
})

const databaseStatus = reactive({
  status: 'unknown',
  message: '检测中...'
})

// 错误历史记录
const errorHistory = ref([])

// 诊断建议
const diagnosticSuggestions = ref([])

// 定时器
let diagnosticTimer = null

// 方法
const runDiagnostic = async () => {
  if (checking.value) return
  
  checking.value = true
  
  try {
    await Promise.all([
      checkApiHealth(),
      checkNetworkConnection(),
      checkAuthStatus(),
      checkDatabaseStatus()
    ])
    
    generateDiagnosticSuggestions()
    
  } catch (error) {
    console.error('诊断检查失败:', error)
    ElMessage.error('诊断检查失败')
  } finally {
    checking.value = false
  }
}

// API健康检查
const checkApiHealth = async () => {
  try {
    const startTime = Date.now()
    const isHealthy = await apiUtils.checkHealth()
    const responseTime = Date.now() - startTime
    
    if (isHealthy) {
      apiStatus.status = 'success'
      apiStatus.message = `正常 (${responseTime}ms)`
    } else {
      apiStatus.status = 'error'
      apiStatus.message = '服务不可用'
    }
  } catch (error) {
    apiStatus.status = 'error'
    apiStatus.message = '连接失败'
  }
  
  apiStatus.lastCheck = new Date()
}

// 网络连接检查
const checkNetworkConnection = async () => {
  try {
    const startTime = Date.now()
    
    // 使用在线状态API和ping测试
    if (!navigator.onLine) {
      networkStatus.status = 'error'
      networkStatus.message = '离线'
      networkStatus.speed = null
      return
    }
    
    // 简单的网络延迟测试
    await fetch('https://www.baidu.com/favicon.ico', { 
      mode: 'no-cors',
      cache: 'no-cache'
    })
    
    const responseTime = Date.now() - startTime
    networkStatus.speed = responseTime
    
    if (responseTime < 500) {
      networkStatus.status = 'success'
      networkStatus.message = '良好'
    } else if (responseTime < 2000) {
      networkStatus.status = 'warning'
      networkStatus.message = '较慢'
    } else {
      networkStatus.status = 'error'
      networkStatus.message = '很慢'
    }
  } catch (error) {
    networkStatus.status = 'error'
    networkStatus.message = '连接异常'
    networkStatus.speed = null
  }
}

// 认证状态检查
const checkAuthStatus = async () => {
  try {
    const isValid = await apiUtils.validateToken()
    
    if (isValid && userStore.userInfo) {
      authStatus.status = 'success'
      authStatus.message = '已认证'
      authStatus.user = userStore.userInfo.real_name || userStore.userInfo.username
    } else if (localStorage.getItem('token')) {
      authStatus.status = 'warning'
      authStatus.message = 'Token无效'
      authStatus.user = null
    } else {
      authStatus.status = 'error'
      authStatus.message = '未登录'
      authStatus.user = null
    }
  } catch (error) {
    authStatus.status = 'error'
    authStatus.message = '认证检查失败'
    authStatus.user = null
  }
}

// 数据库状态检查（通过API）
const checkDatabaseStatus = async () => {
  try {
    // 通过一个简单的API调用来检查数据库状态
    const response = await fetch('http://localhost:3001/api/v1/schedules?page=1&size=1', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      databaseStatus.status = 'success'
      databaseStatus.message = '正常'
    } else if (response.status === 500) {
      databaseStatus.status = 'error'
      databaseStatus.message = '数据库错误'
    } else {
      databaseStatus.status = 'warning'
      databaseStatus.message = '状态未知'
    }
  } catch (error) {
    databaseStatus.status = 'error'
    databaseStatus.message = '检查失败'
  }
}

// 生成诊断建议
const generateDiagnosticSuggestions = () => {
  const suggestions = []
  
  if (apiStatus.status === 'error') {
    suggestions.push({
      message: 'API服务不可用，请检查后端服务是否正常运行',
      action: 'checkBackend',
      actionText: '检查后端'
    })
  }
  
  if (networkStatus.status === 'error') {
    suggestions.push({
      message: '网络连接异常，请检查网络设置',
      action: 'checkNetwork',
      actionText: '网络诊断'
    })
  }
  
  if (authStatus.status === 'error' || authStatus.status === 'warning') {
    suggestions.push({
      message: '认证状态异常，建议重新登录',
      action: 'reauth',
      actionText: '重新登录'
    })
  }
  
  if (databaseStatus.status === 'error') {
    suggestions.push({
      message: '数据库连接异常，请联系系统管理员',
      action: 'contactAdmin',
      actionText: '联系管理员'
    })
  }
  
  // 性能建议
  if (networkStatus.speed && networkStatus.speed > 1000) {
    suggestions.push({
      message: '网络响应较慢，建议检查网络质量',
      action: null,
      actionText: null
    })
  }
  
  diagnosticSuggestions.value = suggestions
}

// 执行建议操作
const executeSuggestion = (suggestion) => {
  switch (suggestion.action) {
    case 'checkBackend':
      ElNotification({
        title: '后端检查',
        message: '请确保后端服务在 http://localhost:3001 正常运行',
        type: 'info',
        duration: 10000
      })
      break
      
    case 'checkNetwork':
      ElNotification({
        title: '网络诊断',
        message: '请检查网络连接，尝试访问其他网站验证网络状态',
        type: 'info',
        duration: 8000
      })
      break
      
    case 'reauth':
      // 清除token并跳转登录
      userStore.logout()
      ElMessage.success('请重新登录')
      break
      
    case 'contactAdmin':
      ElNotification({
        title: '联系管理员',
        message: '数据库连接异常，请联系系统管理员进行处理',
        type: 'warning',
        duration: 0
      })
      break
  }
}

// 添加错误到历史记录
const addErrorToHistory = (error) => {
  const errorRecord = {
    timestamp: new Date(),
    type: error.classification?.type || 'UNKNOWN_ERROR',
    message: error.userMessage || error.message,
    canRetry: error.canRetry || false,
    originalError: error
  }
  
  errorHistory.value.unshift(errorRecord)
  
  // 保持最多20条记录
  if (errorHistory.value.length > 20) {
    errorHistory.value = errorHistory.value.slice(0, 20)
  }
}

// 清空错误历史
const clearErrorHistory = () => {
  errorHistory.value = []
  ElMessage.success('错误记录已清空')
}

// 重试失败的操作
const retryFailedOperation = (error) => {
  ElMessage.info('正在重试操作...')
  // 这里可以根据错误类型执行相应的重试逻辑
  // 暂时只是重新运行诊断
  runDiagnostic()
}

// 工具方法
const getStatusIcon = (status) => {
  return {
    'status-icon': true,
    'status-success': status === 'success',
    'status-warning': status === 'warning',
    'status-error': status === 'error',
    'status-unknown': status === 'unknown'
  }
}

const getStatusIconComponent = (status) => {
  switch (status) {
    case 'success':
      return CircleCheckFilled
    case 'warning':
      return WarningFilled
    case 'error':
      return CircleCloseFilled
    default:
      return Loading
  }
}

const getStatusTagType = (status) => {
  switch (status) {
    case 'success':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 启动自动检查
const startAutoCheck = () => {
  if (props.autoCheck && !diagnosticTimer) {
    diagnosticTimer = setInterval(runDiagnostic, props.checkInterval)
  }
}

// 停止自动检查
const stopAutoCheck = () => {
  if (diagnosticTimer) {
    clearInterval(diagnosticTimer)
    diagnosticTimer = null
  }
}

// 生命周期
onMounted(() => {
  runDiagnostic()
  startAutoCheck()
  
  // 监听全局错误事件（如果有的话）
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.classification) {
      addErrorToHistory(event.reason)
    }
  })
})

onUnmounted(() => {
  stopAutoCheck()
})

// 暴露方法给父组件
defineExpose({
  runDiagnostic,
  addErrorToHistory,
  clearErrorHistory
})
</script>

<style scoped>
.system-diagnostic {
  max-width: 800px;
  margin: 0 auto;
}

.status-card,
.error-history-card,
.suggestions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.status-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-time,
.status-detail {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-icon.status-success {
  color: #67c23a;
}

.status-icon.status-warning {
  color: #e6a23c;
}

.status-icon.status-error {
  color: #f56c6c;
}

.status-icon.status-unknown {
  color: #909399;
}

.error-list {
  max-height: 300px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.error-item:last-child {
  border-bottom: none;
}

.error-time {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.error-type {
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.error-message {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.suggestions-list {
  space-y: 12px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  gap: 12px;
}

.suggestion-icon {
  color: #409eff;
  font-size: 16px;
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}
</style> 