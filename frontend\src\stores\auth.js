import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from '../utils/axios'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const accessToken = ref(localStorage.getItem('accessToken') || null)
  const isLoading = ref(false)
  const error = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!accessToken.value)
  const userRoles = computed(() => user.value?.roles?.map(role => role.code) || [])
  const isAdmin = computed(() => userRoles.value.includes('ADMIN') || userRoles.value.includes('SUPER_ADMIN'))

  // 设置认证token
  const setToken = (token) => {
    accessToken.value = token
    if (token) {
      localStorage.setItem('accessToken', token)
      localStorage.setItem('token', token)
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('token')
      delete axios.defaults.headers.common['Authorization']
    }
  }

  // 设置用户信息
  const setUser = (userData) => {
    user.value = userData
  }

  // 清除认证状态
  const clearAuth = () => {
    user.value = null
    accessToken.value = null
    
    // 清理所有localStorage中的认证相关数据
    localStorage.removeItem('accessToken')
    localStorage.removeItem('token')
    localStorage.removeItem('userProfile')
    localStorage.removeItem('userPermissions')
    localStorage.removeItem('lastLoginTime')
    localStorage.removeItem('refreshToken')
    
    // 清理axios默认headers
    delete axios.defaults.headers.common['Authorization']
    
    // 重置其他store状态
    try {
      // 动态导入其他store避免循环依赖
      import('@/stores/employee').then(({ useEmployeeStore }) => {
        const employeeStore = useEmployeeStore()
        if (employeeStore.resetState) {
          employeeStore.resetState()
        }
      }).catch(err => console.warn('Employee store reset failed:', err))
      
      import('@/stores/meeting').then(({ useMeetingStore }) => {
        const meetingStore = useMeetingStore()
        if (meetingStore.resetState) {
          meetingStore.resetState()
        }
      }).catch(err => console.warn('Meeting store reset failed:', err))
      
      // 清理其他可能的store状态
      const storeKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('employee_') || 
        key.startsWith('meeting_') || 
        key.startsWith('department_') ||
        key.startsWith('user_') ||
        key.startsWith('cache_')
      )
      storeKeys.forEach(key => localStorage.removeItem(key))
      
    } catch (err) {
      console.warn('Store reset failed:', err)
    }
  }

  // 用户注册
  const register = async (userData) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await axios.post('/v1/auth/register', userData)
      
      if (response.data.success) {
        return response.data
      } else {
        throw new Error(response.data.message || '注册失败')
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '注册失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // 用户登录
  const login = async (credential, password) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await axios.post('/v1/auth/login', {
        credential,
        password
      })

      if (response.data.success) {
        const { user: userData, accessToken } = response.data.data
        
        setToken(accessToken)
        setUser(userData)
        
        return response.data
      } else {
        throw new Error(response.data.message || '登录失败')
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '登录失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // 用户登出
  const logout = async () => {
    try {
      isLoading.value = true
      
      // 调用登出API（可选）
      try {
        await axios.post('/v1/auth/logout')
      } catch (err) {
        console.warn('Logout API call failed:', err)
      }
      
      clearAuth()
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 刷新令牌
  const refreshToken = async () => {
    try {
      const response = await axios.post('/v1/auth/refresh')
      
      if (response.data.success) {
        const { token: newToken } = response.data.data
        setToken(newToken)
        return newToken
      } else {
        throw new Error('令牌刷新失败')
      }
    } catch (err) {
      console.error('Token refresh failed:', err)
      clearAuth()
      throw err
    }
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const response = await axios.get('/v1/auth/profile')
      
      if (response.data.success) {
        setUser(response.data.data)
        return response.data.data
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (err) {
      console.error('Fetch user profile failed:', err)
      if (err.response?.status === 401) {
        clearAuth()
      }
      throw err
    }
  }

  // 更新用户信息
  const updateProfile = async (updateData) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await axios.put('/v1/auth/profile', updateData)
      
      if (response.data.success) {
        setUser(response.data.data)
        return response.data
      } else {
        throw new Error(response.data.message || '更新用户信息失败')
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '更新失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // 更改密码
  const changePassword = async (currentPassword, newPassword) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await axios.post('/v1/auth/change-password', {
        oldPassword: currentPassword,
        newPassword
      })
      
      if (response.data.success) {
        return response.data
      } else {
        throw new Error(response.data.message || '密码更改失败')
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '密码更改失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // 检查用户权限
  const hasPermission = (permission) => {
    if (!user.value || !user.value.roles) return false
    
    // 特殊处理：admin用户拥有所有权限
    if (user.value.username === 'admin') {
      return true
    }
    
    return user.value.roles.some(role => {
      if (!role.permissions) return false
      return role.permissions.some(p => {
        if (p === permission) return true
        if (p.endsWith(':*')) {
          const prefix = p.slice(0, -1)
          return permission.startsWith(prefix)
        }
        return false
      })
    })
  }

  // 检查用户角色
  const hasRole = (roleCode) => {
    if (!user.value || !user.value.roles) return false
    
    // 特殊处理：admin用户拥有所有角色
    if (user.value.username === 'admin') {
      return true
    }
    
    return user.value.roles.some(role => role.code === roleCode)
  }

  // 初始化认证状态 - 增强版本
  const initAuth = async () => {
    try {
      // 优先从localStorage获取accessToken
      const storedToken = localStorage.getItem('accessToken') || localStorage.getItem('token')
      
      if (storedToken) {
        // 设置token到axios和store
        setToken(storedToken)
        
        try {
          // 尝试获取用户信息验证token有效性
          await fetchUserProfile()
          
          // 记录最后登录时间
          localStorage.setItem('lastLoginTime', new Date().toISOString())
          
          console.log('认证状态恢复成功')
        } catch (err) {
          console.error('Token验证失败，清理认证状态:', err)
          
          // Token无效，清理所有状态
          clearAuth()
          
          // 如果是401错误，说明token过期或无效
          if (err.response?.status === 401) {
            console.log('Token已过期，需要重新登录')
          }
          
          throw err
        }
      } else {
        console.log('未找到存储的token，用户需要登录')
      }
    } catch (err) {
      console.error('认证初始化失败:', err)
      
      // 确保清理状态
      clearAuth()
      
      // 添加数据持久化失败的错误处理
      try {
        localStorage.setItem('authInitError', JSON.stringify({
          message: err.message,
          timestamp: new Date().toISOString()
        }))
      } catch (storageErr) {
        console.error('无法保存错误信息到localStorage:', storageErr)
      }
      
      throw err
    }
  }

  return {
    // 状态
    user,
    accessToken,
    isLoading,
    error,
    
    // 计算属性
    isAuthenticated,
    userRoles,
    isAdmin,
    
    // 方法
    login,
    logout,
    register,
    refreshToken,
    fetchUserProfile,
    updateProfile,
    changePassword,
    hasPermission,
    hasRole,
    initAuth,
    clearAuth
  }
}) 