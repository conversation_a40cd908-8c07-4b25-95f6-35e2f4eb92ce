const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Department extends Model {
    static associate(models) {
      // 自关联：上级部门
      Department.belongsTo(Department, {
        foreignKey: 'parent_id',
        as: 'parent'
      });

      // 自关联：下级部门
      Department.hasMany(Department, {
        foreignKey: 'parent_id',
        as: 'children'
      });

      // 关联到员工
      Department.hasMany(models.Employee, {
        foreignKey: 'department_id',
        as: 'employees'
      });

      // 关联到部门负责人
      Department.belongsTo(models.Employee, {
        foreignKey: 'manager_id',
        as: 'manager'
      });
    }

    // 静态方法：获取部门树形结构
    static async getDepartmentTree() {
      const departments = await this.findAll({
        include: [
          {
            model: Department,
            as: 'children',
            include: [
              {
                model: Department,
                as: 'children',
                include: [
                  {
                    model: Department,
                    as: 'children'
                  }
                ]
              }
            ]
          },
          {
            model: sequelize.models.Employee,
            as: 'manager',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        where: { parent_id: null },
        order: [['sort_order', 'ASC'], ['name', 'ASC']]
      });

      return departments;
    }

    // 静态方法：获取所有部门的扁平列表
    static async getAllDepartments() {
      return this.findAll({
        include: [
          {
            model: Department,
            as: 'parent',
            attributes: ['id', 'name']
          },
          {
            model: sequelize.models.Employee,
            as: 'manager',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        order: [['sort_order', 'ASC'], ['name', 'ASC']]
      });
    }

    // 静态方法：根据ID获取部门及其子部门的所有ID
    static async getDepartmentAndChildrenIds(departmentId) {
      const department = await this.findByPk(departmentId, {
        include: [{
          model: Department,
          as: 'children',
          include: [{
            model: Department,
            as: 'children',
            include: [{
              model: Department,
              as: 'children'
            }]
          }]
        }]
      });

      if (!department) return [];

      const ids = [departmentId];
      
      const collectIds = (dept) => {
        if (dept.children && dept.children.length > 0) {
          dept.children.forEach(child => {
            ids.push(child.id);
            collectIds(child);
          });
        }
      };

      collectIds(department);
      return ids;
    }

    // 实例方法：获取部门路径（从根部门到当前部门）
    async getDepartmentPath() {
      const path = [this];
      let current = this;

      while (current.parent_id) {
        current = await Department.findByPk(current.parent_id);
        if (current) {
          path.unshift(current);
        } else {
          break;
        }
      }

      return path;
    }

    // 实例方法：获取所有子部门（递归）
    async getAllChildren() {
      const children = await Department.findAll({
        where: { parent_id: this.id },
        include: [{
          model: Department,
          as: 'children'
        }]
      });

      const allChildren = [...children];
      for (const child of children) {
        const grandChildren = await child.getAllChildren();
        allChildren.push(...grandChildren);
      }

      return allChildren;
    }

    // 实例方法：检查是否可以删除部门
    async canDelete() {
      // 检查是否有子部门
      const childrenCount = await Department.count({
        where: { parent_id: this.id }
      });

      if (childrenCount > 0) {
        return { canDelete: false, reason: '该部门下还有子部门，无法删除' };
      }

      // 检查是否有员工
      const employeeCount = await sequelize.models.Employee.count({
        where: { department_id: this.id }
      });

      if (employeeCount > 0) {
        return { canDelete: false, reason: '该部门下还有员工，无法删除' };
      }

      return { canDelete: true };
    }

    // 静态方法：生成部门编码
    static async generateDepartmentCode(parentId = null) {
      let prefix = 'D';
      
      if (parentId) {
        const parent = await this.findByPk(parentId);
        if (parent) {
          prefix = parent.code;
        }
      }

      // 查找同级部门的最大编号
      const siblings = await this.findAll({
        where: { parent_id: parentId },
        order: [['code', 'DESC']],
        limit: 1
      });

      let nextNumber = 1;
      if (siblings.length > 0) {
        const lastCode = siblings[0].code;
        const match = lastCode.match(/(\d+)$/);
        if (match) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      const codeLength = parentId ? 2 : 3; // 一级部门3位，子部门2位
      return `${prefix}${nextNumber.toString().padStart(codeLength, '0')}`;
    }

    // 重写toJSON方法，格式化输出
    toJSON() {
      const values = Object.assign({}, this.get());
      
      // 添加层级信息
      if (this.parent_id) {
        values.level = this.code.replace(/[^\d]/g, '').length / 2;
      } else {
        values.level = 1;
      }

      return values;
    }
  }

  Department.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '部门名称'
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: '部门编码'
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id'
      },
      comment: '上级部门ID'
    },
    manager_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '部门负责人ID'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '部门描述'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序顺序'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active',
      comment: '部门状态'
    },
    location: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '部门位置'
    },
    contact_phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '部门联系电话'
    },
    contact_email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '部门邮箱'
    },
    budget: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: '部门预算'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Department',
    tableName: 'departments',
    timestamps: true,
    underscored: true,
    indexes: [
      { fields: ['code'] },
      { fields: ['parent_id'] },
      { fields: ['manager_id'] },
      { fields: ['status'] },
      { fields: ['sort_order'] }
    ]
  });

  return Department;
}; 