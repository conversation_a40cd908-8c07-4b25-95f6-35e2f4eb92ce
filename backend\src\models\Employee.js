const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Employee extends Model {
    static associate(models) {
      // 关联到用户表（一对一）
      Employee.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });

      // 关联到部门表
      Employee.belongsTo(models.Department, {
        foreignKey: 'department_id',
        as: 'department'
      });

      // 关联到职位表
      Employee.belongsTo(models.Position, {
        foreignKey: 'position_id',
        as: 'position'
      });

      // 关联到直属上级（自关联）
      Employee.belongsTo(Employee, {
        foreignKey: 'direct_supervisor_id',
        as: 'manager'
      });

      // 关联到下属员工（自关联）
      Employee.hasMany(Employee, {
        foreignKey: 'direct_supervisor_id',
        as: 'subordinates'
      });

      // 关联到考勤记录
      Employee.hasMany(models.AttendanceRecord, {
        foreignKey: 'employee_id',
        as: 'attendanceRecords'
      });

      // 关联到请假申请
      Employee.hasMany(models.LeaveRequest, {
        foreignKey: 'employee_id',
        as: 'leaveRequests'
      });

      // 关联到会议参与者
      Employee.hasMany(models.MeetingParticipant, {
        foreignKey: 'employee_id',
        as: 'meetingParticipants'
      });
    }

    // 静态方法：获取员工列表（支持分页和筛选）
    static async getEmployeeList(options = {}) {
      const {
        page = 1,
        pageSize = 20,
        departmentId,
        positionId,
        status,
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const where = {};
      const include = [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'real_name', 'phone', 'avatar', 'status']
        },
        {
          model: sequelize.models.Department,
          as: 'department',
          attributes: ['id', 'name', 'code']
        },
        {
          model: sequelize.models.Position,
          as: 'position',
          attributes: ['id', 'name', 'level']
        },
        {
          model: Employee,
          as: 'manager',
          attributes: ['id', 'employee_no'],
          include: [{
            model: sequelize.models.User,
            as: 'user',
            attributes: ['real_name']
          }]
        }
      ];

      // 部门筛选
      if (departmentId) {
        where.department_id = departmentId;
      }

      // 职位筛选
      if (positionId) {
        where.position_id = positionId;
      }

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 关键词搜索
      if (keyword) {
        include[0].where = {
          [sequelize.Sequelize.Op.or]: [
            { real_name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
            { username: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
            { email: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
          ]
        };
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include,
        limit: pageSize,
        offset,
        order: [[sortBy, sortOrder]],
        distinct: true
      });

      return {
        employees: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：根据部门获取员工
    static async getEmployeesByDepartment(departmentId) {
      return this.findAll({
        where: { department_id: departmentId },
        include: [
          {
            model: sequelize.models.User,
            as: 'user',
            attributes: ['id', 'real_name', 'email', 'phone', 'avatar']
          },
          {
            model: sequelize.models.Position,
            as: 'position',
            attributes: ['id', 'name', 'level']
          }
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // 实例方法：获取下属员工
    async getSubordinates() {
      return Employee.findAll({
        where: { direct_supervisor_id: this.id },
        include: [
          {
            model: sequelize.models.User,
            as: 'user',
            attributes: ['id', 'real_name', 'email', 'phone']
          },
          {
            model: sequelize.models.Department,
            as: 'department',
            attributes: ['id', 'name']
          },
          {
            model: sequelize.models.Position,
            as: 'position',
            attributes: ['id', 'name', 'level']
          }
        ]
      });
    }

    // 实例方法：检查是否是某员工的上级
    async isManagerOf(employeeId) {
      const subordinate = await Employee.findByPk(employeeId);
      if (!subordinate) return false;

      // 检查直接下属
      if (subordinate.direct_supervisor_id === this.id) return true;

      // 递归检查间接下属
      if (subordinate.direct_supervisor_id) {
        const manager = await Employee.findByPk(subordinate.direct_supervisor_id);
        if (manager) {
          return await this.isManagerOf(manager.id);
        }
      }

      return false;
    }

    // 实例方法：生成员工编号
    static async generateEmployeeNo() {
      const year = new Date().getFullYear();
      const prefix = `EMP${year}`;
      
      // 查找当年最大编号
      const lastEmployee = await this.findOne({
        where: {
          employee_no: {
            [sequelize.Sequelize.Op.like]: `${prefix}%`
          }
        },
        order: [['employee_no', 'DESC']]
      });

      let nextNumber = 1;
      if (lastEmployee) {
        const lastNumber = parseInt(lastEmployee.employee_no.substring(prefix.length));
        nextNumber = lastNumber + 1;
      }

      return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
    }

    // 重写toJSON方法，格式化输出
    toJSON() {
      const values = Object.assign({}, this.get());
      
      // 格式化日期 - 安全处理不同格式的日期
      if (values.hire_date) {
        if (typeof values.hire_date === 'string') {
          values.hire_date = values.hire_date.split('T')[0]; // 如果已经是字符串，只取日期部分
        } else if (values.hire_date instanceof Date) {
          values.hire_date = values.hire_date.toISOString().split('T')[0];
        }
      }
      if (values.birth_date) {
        if (typeof values.birth_date === 'string') {
          values.birth_date = values.birth_date.split('T')[0]; // 如果已经是字符串，只取日期部分
        } else if (values.birth_date instanceof Date) {
          values.birth_date = values.birth_date.toISOString().split('T')[0];
        }
      }

      return values;
    }
  }

  Employee.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    employee_no: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: '员工编号',
      field: 'employee_no'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '关联用户ID'
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'departments',
        key: 'id'
      },
      comment: '部门ID'
    },
    position_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'positions',
        key: 'id'
      },
      comment: '职位ID'
    },
    direct_supervisor_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '直属上级ID',
      field: 'direct_supervisor_id'
    },
    hire_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '入职日期'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'resigned'),
      allowNull: false,
      defaultValue: 'active',
      comment: '员工状态'
    },
    salary: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '基础薪资'
    },
    work_location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '工作地点'
    },
    emergency_contact: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '紧急联系人'
    },
    emergency_phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '紧急联系电话'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注信息'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Employee',
    tableName: 'employees',
    timestamps: true,
    underscored: true,
    indexes: [
      { fields: ['employee_no'] },
      { fields: ['user_id'] },
      { fields: ['department_id'] },
      { fields: ['position_id'] },
      { fields: ['direct_supervisor_id'] },
      { fields: ['status'] },
      { fields: ['hire_date'] }
    ]
  });

  return Employee;
}; 