const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class AttendanceRecord extends Model {
    static associate(models) {
      // 关联到员工表
      AttendanceRecord.belongsTo(models.Employee, {
        foreignKey: 'employee_id',
        as: 'employee'
      });
    }

    // 静态方法：获取员工考勤记录
    static async getAttendanceRecords(options = {}) {
      const {
        employeeId,
        startDate,
        endDate,
        page = 1,
        pageSize = 20,
        clockType
      } = options;

      const where = {};
      
      if (employeeId) {
        where.employee_id = employeeId;
      }
      
      if (startDate && endDate) {
        where.clock_time = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }
      
      if (clockType) {
        where.clock_type = clockType;
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.Employee,
            as: 'employee',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [['clock_time', 'DESC']]
      });

      return {
        records: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：检查今日是否已打卡
    static async getTodayAttendance(employeeId) {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

      return this.findAll({
        where: {
          employee_id: employeeId,
          clock_time: {
            [sequelize.Sequelize.Op.between]: [startOfDay, endOfDay]
          }
        },
        order: [['clock_time', 'ASC']]
      });
    }

    // 静态方法：获取考勤统计
    static async getAttendanceStats(employeeId, startDate, endDate) {
      const where = {
        employee_id: employeeId,
        clock_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      };

      const records = await this.findAll({
        where,
        order: [['clock_time', 'ASC']]
      });

      // 按日期分组统计
      const dailyStats = {};
      records.forEach(record => {
        // 安全处理日期格式
        let date;
        if (typeof record.clock_time === 'string') {
          date = record.clock_time.split('T')[0];
        } else if (record.clock_time instanceof Date) {
          date = record.clock_time.toISOString().split('T')[0];
        } else {
          date = new Date(record.clock_time).toISOString().split('T')[0];
        }
        
        if (!dailyStats[date]) {
          dailyStats[date] = {
            date,
            clockIn: null,
            clockOut: null,
            status: 'absent'
          };
        }
        
        if (record.clock_type === 'clock_in') {
          dailyStats[date].clockIn = record.clock_time;
        } else {
          dailyStats[date].clockOut = record.clock_time;
        }
        
        // 更新状态
        if (dailyStats[date].clockIn) {
          dailyStats[date].status = dailyStats[date].clockOut ? 'normal' : 'incomplete';
        }
      });

      return Object.values(dailyStats);
    }
  }

  AttendanceRecord.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    employee_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    clock_type: {
      type: DataTypes.ENUM('clock_in', 'clock_out'),
      allowNull: false,
      comment: '打卡类型：上班打卡/下班打卡'
    },
    clock_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '打卡时间'
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '打卡地点'
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
      comment: '纬度'
    },
    longitude: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true,
      comment: '经度'
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    },
    status: {
      type: DataTypes.ENUM('normal', 'late', 'early', 'overtime'),
      defaultValue: 'normal',
      comment: '考勤状态'
    }
  }, {
    sequelize,
    modelName: 'AttendanceRecord',
    tableName: 'attendance_records',
    timestamps: true,
    paranoid: true,
    comment: '考勤记录表'
  });

  return AttendanceRecord;
}; 