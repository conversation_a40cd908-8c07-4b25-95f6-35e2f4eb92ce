const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class File extends Model {
    static associate(models) {
      // 关联到上传者
      File.belongsTo(models.Employee, {
        foreignKey: 'uploaded_by',
        as: 'uploader'
      });

      // 关联到文件分享
      File.hasMany(models.FileShare, {
        foreignKey: 'file_id',
        as: 'shares'
      });

      // 关联到文件版本（自关联）
      File.belongsTo(models.File, {
        foreignKey: 'parent_file_id',
        as: 'parentFile'
      });

      File.hasMany(models.File, {
        foreignKey: 'parent_file_id',
        as: 'versions'
      });
    }

    // 静态方法：获取用户的文件列表
    static async getUserFiles(userId, options = {}) {
      const {
        page = 1,
        pageSize = 20,
        folder,
        fileType,
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const where = {
        uploaded_by: userId,
        is_deleted: false
      };

      if (folder) {
        where.folder_path = { [sequelize.Sequelize.Op.like]: `${folder}%` };
      }

      if (fileType) {
        where.file_type = fileType;
      }

      if (keyword) {
        where[sequelize.Sequelize.Op.or] = [
          { original_name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { description: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
        ];
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.Employee,
            as: 'uploader',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [[sortBy, sortOrder]],
        distinct: true
      });

      return {
        files: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：获取共享给用户的文件
    static async getSharedFiles(userId, options = {}) {
      const {
        page = 1,
        pageSize = 20,
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        include: [
          {
            model: sequelize.models.FileShare,
            as: 'shares',
            where: {
              shared_with: userId,
              is_active: true,
              [sequelize.Sequelize.Op.or]: [
                { expires_at: null },
                { expires_at: { [sequelize.Sequelize.Op.gt]: new Date() } }
              ]
            }
          },
          {
            model: sequelize.models.Employee,
            as: 'uploader',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        where: {
          is_deleted: false,
          ...(keyword && {
            [sequelize.Sequelize.Op.or]: [
              { original_name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
              { description: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
            ]
          })
        },
        limit: pageSize,
        offset,
        order: [[sortBy, sortOrder]],
        distinct: true
      });

      return {
        files: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 实例方法：检查用户是否有访问权限
    canAccessBy(userId) {
      // 文件所有者有完全权限
      if (this.uploaded_by === userId) {
        return ['read', 'write', 'delete', 'share'];
      }

      // 公开文件只有读取权限
      if (this.is_public) {
        return ['read'];
      }

      // 检查是否有分享权限（需要联查FileShare表）
      return [];
    }

    // 实例方法：获取文件下载URL
    async getDownloadUrl(expiresIn = 3600) {
      const { getPresignedUrl } = require('../config/minio');
      return getPresignedUrl(this.object_name, expiresIn);
    }

    // 实例方法：创建新版本
    async createVersion(newObjectName, versionNote = '') {
      const newVersion = await File.create({
        original_name: this.original_name,
        object_name: newObjectName,
        file_size: this.file_size, // 需要重新获取
        file_type: this.file_type,
        mime_type: this.mime_type,
        folder_path: this.folder_path,
        description: this.description,
        uploaded_by: this.uploaded_by,
        parent_file_id: this.parent_file_id || this.id,
        version: (this.version || 1) + 1,
        version_note: versionNote,
        is_public: this.is_public
      });

      return newVersion;
    }

    // 实例方法：软删除文件
    async softDelete() {
      this.is_deleted = true;
      this.deleted_at = new Date();
      return this.save();
    }

    // 静态方法：获取文件类型统计
    static async getFileTypeStats(userId) {
      const stats = await this.findAll({
        where: {
          uploaded_by: userId,
          is_deleted: false
        },
        attributes: [
          'file_type',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('SUM', sequelize.col('file_size')), 'total_size']
        ],
        group: ['file_type'],
        raw: true
      });

      return stats.reduce((acc, stat) => {
        acc[stat.file_type] = {
          count: parseInt(stat.count),
          totalSize: parseInt(stat.total_size) || 0
        };
        return acc;
      }, {});
    }

    // 静态方法：获取用户存储使用情况
    static async getStorageUsage(userId) {
      const result = await this.findOne({
        where: {
          uploaded_by: userId,
          is_deleted: false
        },
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'file_count'],
          [sequelize.fn('SUM', sequelize.col('file_size')), 'total_size'],
          [sequelize.fn('AVG', sequelize.col('file_size')), 'avg_file_size']
        ],
        raw: true
      });

      return {
        fileCount: parseInt(result.file_count) || 0,
        totalSize: parseInt(result.total_size) || 0,
        avgFileSize: parseInt(result.avg_file_size) || 0
      };
    }
  }

  File.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    original_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '原始文件名'
    },
    object_name: {
      type: DataTypes.STRING(500),
      allowNull: false,
      unique: true,
      comment: 'MinIO中的对象名称'
    },
    file_size: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '文件大小（字节）'
    },
    file_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '文件类型'
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'MIME类型'
    },
    folder_path: {
      type: DataTypes.STRING(500),
      defaultValue: '/',
      comment: '文件夹路径'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件描述'
    },
    uploaded_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '上传者ID'
    },
    parent_file_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'files',
        key: 'id'
      },
      comment: '父文件ID（用于版本管理）'
    },
    version: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: '版本号'
    },
    version_note: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '版本说明'
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否公开'
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否已删除'
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '删除时间'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '文件元数据'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '文件标签'
    }
  }, {
    sequelize,
    modelName: 'File',
    tableName: 'files',
    timestamps: true,
    paranoid: false, // 使用软删除，不使用paranoid
    comment: '文件信息表',
    indexes: [
      {
        fields: ['uploaded_by', 'is_deleted']
      },
      {
        fields: ['folder_path']
      },
      {
        fields: ['file_type']
      },
      {
        fields: ['parent_file_id']
      },
      {
        fields: ['object_name'],
        unique: true
      }
    ]
  });

  return File;
}; 