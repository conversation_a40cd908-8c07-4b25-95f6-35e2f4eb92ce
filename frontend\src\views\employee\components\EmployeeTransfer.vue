<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="员工调动"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="employeeData" class="transfer-form">
      <!-- 当前信息 -->
      <el-card class="current-info">
        <template #header>
          <h4>当前信息</h4>
        </template>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="姓名">
            {{ employeeData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="工号">
            {{ employeeData.employeeId }}
          </el-descriptions-item>
          <el-descriptions-item label="当前部门">
            {{ employeeData.department }}
          </el-descriptions-item>
          <el-descriptions-item label="当前职位">
            {{ employeeData.position }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 调动信息 -->
      <el-form
        ref="formRef"
        :model="transferData"
        :rules="transferRules"
        label-width="100px"
        class="transfer-form-content"
      >
        <el-form-item label="目标部门" prop="targetDepartment">
          <el-select 
            v-model="transferData.targetDepartment" 
            placeholder="请选择目标部门"
            style="width: 100%"
          >
            <el-option label="技术部" value="技术部" />
            <el-option label="产品部" value="产品部" />
            <el-option label="运营部" value="运营部" />
            <el-option label="人事部" value="人事部" />
            <el-option label="财务部" value="财务部" />
            <el-option label="市场部" value="市场部" />
          </el-select>
        </el-form-item>

        <el-form-item label="目标职位" prop="targetPosition">
          <el-input 
            v-model="transferData.targetPosition" 
            placeholder="请输入目标职位"
          />
        </el-form-item>

        <el-form-item label="调动类型" prop="transferType">
          <el-radio-group v-model="transferData.transferType">
            <el-radio label="promotion">晋升</el-radio>
            <el-radio label="transfer">平调</el-radio>
            <el-radio label="demotion">降职</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="transferData.effectiveDate"
            type="date"
            placeholder="请选择生效日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="调动原因" prop="reason">
          <el-input
            v-model="transferData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入调动原因"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="transferData.remarks"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认调动
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  employeeData: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'submit'])

// 响应式数据
const formRef = ref()
const loading = ref(false)

const transferData = reactive({
  targetDepartment: '',
  targetPosition: '',
  transferType: 'transfer',
  effectiveDate: '',
  reason: '',
  remarks: ''
})

// 表单验证规则
const transferRules = {
  targetDepartment: [
    { required: true, message: '请选择目标部门', trigger: 'change' }
  ],
  targetPosition: [
    { required: true, message: '请输入目标职位', trigger: 'blur' }
  ],
  transferType: [
    { required: true, message: '请选择调动类型', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入调动原因', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(transferData, {
    targetDepartment: '',
    targetPosition: '',
    transferType: 'transfer',
    effectiveDate: '',
    reason: '',
    remarks: ''
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交调动申请
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 确认调动
    await ElMessageBox.confirm(
      `确定要将 ${props.employeeData?.name} 调动到 ${transferData.targetDepartment} 担任 ${transferData.targetPosition} 吗？`,
      '确认调动',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    
    // 构造调动数据
    const transferInfo = {
      employeeId: props.employeeData?.id,
      employeeName: props.employeeData?.name,
      fromDepartment: props.employeeData?.department,
      fromPosition: props.employeeData?.position,
      ...transferData
    }
    
    // 触发提交事件
    emit('submit', transferInfo)
    
    ElMessage.success('员工调动申请提交成功')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('调动申请失败:', error)
      ElMessage.error('调动申请失败，请重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.transfer-form {
  max-height: 600px;
  overflow-y: auto;
}

.current-info {
  margin-bottom: 20px;
}

.current-info h4 {
  margin: 0;
  color: #303133;
}

.transfer-form-content {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
