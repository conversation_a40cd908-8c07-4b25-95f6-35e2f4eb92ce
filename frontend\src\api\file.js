import request from '@/utils/request';

export const fileAPI = {
  // 上传文件
  uploadFile(formData, config = {}) {
    return request({
      url: '/v1/files/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    });
  },

  // 获取用户文件列表
  getUserFiles(params) {
    return request({
      url: '/v1/files',
      method: 'get',
      params
    });
  },

  // 获取共享文件列表
  getSharedFiles(params) {
    return request({
      url: '/v1/files/shared',
      method: 'get',
      params
    });
  },

  // 获取文件详情
  getFileById(fileId) {
    return request({
      url: `/v1/files/${fileId}`,
      method: 'get'
    });
  },

  // 获取文件下载URL
  getDownloadUrl(fileId, params) {
    return request({
      url: `/v1/files/${fileId}/download`,
      method: 'get',
      params
    });
  },

  // 删除文件
  deleteFile(fileId) {
    return request({
      url: `/v1/files/${fileId}`,
      method: 'delete'
    });
  },

  // 更新文件信息
  updateFile(fileId, data) {
    return request({
      url: `/files/${fileId}`,
      method: 'put',
      data
    });
  },

  // 创建文件分享
  createFileShare(fileId, data) {
    return request({
      url: `/files/${fileId}/share`,
      method: 'post',
      data
    });
  },

  // 获取用户分享记录
  getUserShares(params) {
    return request({
      url: '/files/shares/my',
      method: 'get',
      params
    });
  },

  // 停用分享
  deactivateShare(shareId) {
    return request({
      url: `/files/shares/${shareId}`,
      method: 'delete'
    });
  },

  // 搜索文件
  searchFiles(params) {
    return request({
      url: '/files/search',
      method: 'get',
      params
    });
  },

  // 获取文件统计信息
  getFileStats() {
    return request({
      url: '/files/stats',
      method: 'get'
    });
  },

  // 获取文件预览信息
  getFilePreview(fileId) {
    return request({
      url: `/files/${fileId}/preview`,
      method: 'get'
    });
  },

  // 创建文件新版本
  createFileVersion(fileId, formData) {
    return request({
      url: `/files/${fileId}/versions`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // 批量删除文件
  batchDeleteFiles(data) {
    return request({
      url: '/files/batch/delete',
      method: 'post',
      data
    });
  },

  // 移动文件
  moveFiles(data) {
    return request({
      url: '/files/batch/move',
      method: 'post',
      data
    });
  },

  // 访问分享文件
  accessSharedFile(shareToken, params) {
    return request({
      url: `/files/share/${shareToken}`,
      method: 'get',
      params
    });
  },

  // 获取分享文件下载URL
  getSharedFileDownloadUrl(shareToken, params) {
    return request({
      url: `/files/share/${shareToken}/download`,
      method: 'get',
      params
    });
  },

  // 清理过期分享（管理员）
  cleanupExpiredShares() {
    return request({
      url: '/files/admin/cleanup-expired-shares',
      method: 'post'
    });
  }
}; 