const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
require('dotenv').config();

const { sequelize } = require('./models');
const routes = require('./routes');
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const attendanceRoutes = require('./routes/attendance');
const meetingRoutes = require('./routes/meeting');
const scheduleRoutes = require('./routes/schedule');
const { errorHandler, notFound } = require('./middleware/errorMiddleware');
const logger = require('./shared/logger');
const { initializeMinio } = require('./config/minio');

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://*************:5173',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 通用中间件
app.use(compression());
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// 简单健康检查 - 直接在根级别
app.get('/health', (req, res) => {
  try {
    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: 'SQLite',
      version: '1.0.0'
    });
  } catch (error) {
    logger.error('健康检查错误:', error);
    res.status(500).json({
      status: 'ERROR',
      message: error.message
    });
  }
});

// API健康检查 - 放在限流中间件之前
app.get('/api/health', (req, res) => {
  try {
    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: 'SQLite',
      version: '1.0.0',
      api: 'v1'
    });
  } catch (error) {
    logger.error('API健康检查错误:', error);
    res.status(500).json({
      status: 'ERROR',
      message: error.message
    });
  }
});

// 限流中间件（开发环境放宽限制）
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: process.env.NODE_ENV === 'production' ? 100 : 10000, // 开发环境10000次，生产环境100次
  message: '请求过于频繁，请稍后再试',
  skip: (req) => {
    // 跳过健康检查、登录接口、dashboard接口的限流
    return req.path === '/api/health' ||
           req.path === '/health' ||
           req.path.includes('/auth/login') ||
           req.path.includes('/dashboard') ||
           req.path.includes('/auth/profile')
  }
});
app.use('/api/', limiter);

// API路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/dashboard', require('./routes/dashboard'));
app.use('/api/v1/employees', employeeRoutes);
app.use('/api/v1/attendance', attendanceRoutes);
app.use('/api/v1/meetings', meetingRoutes);
app.use('/api/v1/schedules', scheduleRoutes);
app.use('/api/v1/workflow', require('./routes/workflow'));
app.use('/api/v1/files', require('./routes/file'));
app.use('/api/v1', require('./routes/supply'));

// 临时设置路由
app.use('/api/v1/setup', require('./routes/setup'));

// 添加departments和positions的专用路由
const employeeController = require('./controllers/employeeController');
const { authenticateToken, requirePermissions } = require('./middleware/auth');

// Department APIs
app.get('/api/v1/departments', authenticateToken, requirePermissions(['employee:read']), employeeController.getDepartmentTree);

// Position APIs  
app.get('/api/v1/positions', authenticateToken, requirePermissions(['employee:read']), employeeController.getPositionsForSelect);

app.use('/api', routes);

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 数据库连接和服务器启动
async function startServer() {
  try {
    console.log('🚀 启动智能办公系统服务器...');

    // 测试数据库连接
    try {
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功 (development环境)');
      logger.info('数据库连接已建立');

      // 同步数据库模型（开发环境）
      if (process.env.NODE_ENV !== 'production') {
        await sequelize.sync({ alter: true });
        console.log('✅ 数据库模型已同步');
        logger.info('数据库模型已同步');
      }
    } catch (dbError) {
      console.warn('⚠️  数据库连接失败，但继续启动服务器:', dbError.message);
      logger.warn('数据库连接失败:', dbError.message);
    }

    // 跳过MinIO初始化（开发环境）
    if (process.env.NODE_ENV === 'production') {
      try {
        await initializeMinio();
        console.log('✅ MinIO服务已初始化');
        logger.info('MinIO服务已初始化');
      } catch (minioError) {
        console.warn('⚠️  MinIO初始化失败:', minioError.message);
        logger.warn('MinIO初始化失败:', minioError.message);
      }
    } else {
      console.log('⏭️  跳过MinIO初始化 (开发模式)');
      logger.info('开发模式：跳过MinIO初始化');
    }

    // 启动服务器
    const server = app.listen(PORT, () => {
      console.log(`🎉 服务器启动成功！`);
      console.log(`📍 地址: http://localhost:${PORT}`);
      console.log(`📍 健康检查: http://localhost:${PORT}/health`);
      console.log(`📍 API文档: http://localhost:${PORT}/api`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`👤 管理员登录: admin / 123456`);
      logger.info(`服务器运行在端口 ${PORT}`);
    });

    // 处理服务器错误
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用`);
        logger.error(`端口 ${PORT} 已被占用`);
      } else {
        console.error('❌ 服务器错误:', error);
        logger.error('服务器错误:', error);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  try {
    await sequelize.close();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error);
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  logger.info('收到SIGINT信号，正在关闭服务器...');
  try {
    await sequelize.close();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error);
  }
  process.exit(0);
});

startServer();

module.exports = app; 