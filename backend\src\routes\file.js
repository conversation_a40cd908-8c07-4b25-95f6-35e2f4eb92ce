const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');

// 配置multer中间件
const storage = multer.memoryStorage();
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain', 'text/csv',
    'application/zip', 'application/x-rar-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  }
});

// 应用认证中间件到所有路由（除了公开分享链接）
router.use('/share/:shareToken', (req, res, next) => {
  // 分享链接可以不需要认证
  next();
});

router.use(authenticateToken);

// 文件上传
router.post('/upload', upload.single('file'), fileController.uploadFile);

// 获取用户文件列表
router.get('/', fileController.getUserFiles);

// 获取共享文件列表
router.get('/shared', fileController.getSharedFiles);

// 搜索文件
router.get('/search', fileController.searchFiles);

// 获取文件统计信息
router.get('/stats', fileController.getFileStats);

// 获取文件详情
router.get('/:fileId', fileController.getFileById);

// 获取文件下载URL
router.get('/:fileId/download', fileController.getDownloadUrl);

// 获取文件预览信息
router.get('/:fileId/preview', fileController.getFilePreview);

// 更新文件信息
router.put('/:fileId', fileController.updateFile);

// 删除文件
router.delete('/:fileId', fileController.deleteFile);

// 创建文件新版本
router.post('/:fileId/versions', upload.single('file'), fileController.createFileVersion);

// 创建文件分享
router.post('/:fileId/share', fileController.createFileShare);

// 获取用户的分享记录
router.get('/shares/my', fileController.getUserShares);

// 停用分享
router.delete('/shares/:shareId', fileController.deactivateShare);

// 批量操作
router.post('/batch/delete', fileController.batchDeleteFiles);
router.post('/batch/move', fileController.moveFiles);

// 管理员功能
router.post('/admin/cleanup-expired-shares', fileController.cleanupExpiredShares);

// 公开分享链接访问（不需要认证）
router.get('/share/:shareToken', fileController.accessSharedFile);
router.get('/share/:shareToken/download', fileController.getSharedFileDownloadUrl);

module.exports = router; 