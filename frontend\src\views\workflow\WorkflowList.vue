<template>
  <div class="workflow-list">
    <div class="page-header">
      <h1>工作流管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        发起申请
      </el-button>
    </div>

    <!-- 工作流表格 -->
    <el-card class="table-card">
      <el-table
        :data="workflowList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="title" label="申请标题" min-width="200" />
        <el-table-column label="申请类型" width="120">
          <template #default="{ row }">
            <el-tag>{{ getTypeText(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicantName" label="申请人" width="120" />
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="160" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewWorkflow(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建申请对话框 -->
    <el-dialog v-model="showCreateDialog" title="发起申请" width="600px">
      <el-form :model="applicationForm" label-width="100px">
        <el-form-item label="申请类型">
          <el-select v-model="applicationForm.type" placeholder="请选择申请类型">
            <el-option label="请假申请" value="leave" />
            <el-option label="报销申请" value="expense" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请标题">
          <el-input v-model="applicationForm.title" placeholder="请输入申请标题" />
        </el-form-item>
        <el-form-item label="申请说明">
          <el-input
            v-model="applicationForm.description"
            type="textarea"
            placeholder="请详细说明申请内容"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitApplication">提交申请</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="申请详情" width="600px">
      <div v-if="currentWorkflow">
        <p><strong>标题：</strong>{{ currentWorkflow.title }}</p>
        <p><strong>类型：</strong>{{ getTypeText(currentWorkflow.type) }}</p>
        <p><strong>申请人：</strong>{{ currentWorkflow.applicantName }}</p>
        <p><strong>状态：</strong>{{ getStatusText(currentWorkflow.status) }}</p>
        <p><strong>申请时间：</strong>{{ currentWorkflow.createTime }}</p>
        <p><strong>说明：</strong>{{ currentWorkflow.description }}</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentWorkflow = ref(null)

// 工作流列表
const workflowList = ref([
  {
    id: 1,
    title: '年假申请',
    type: 'leave',
    applicantName: '张三',
    status: 'pending',
    createTime: '2024-01-15 09:30:00',
    description: '申请年假'
  }
])

// 申请表单
const applicationForm = reactive({
  type: '',
  title: '',
  description: ''
})

// 方法
const loadWorkflowList = async () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const submitApplication = () => {
  ElMessage.success('申请提交成功')
  showCreateDialog.value = false
  Object.assign(applicationForm, { type: '', title: '', description: '' })
}

const viewWorkflow = (workflow) => {
  currentWorkflow.value = workflow
  showDetailDialog.value = true
}

// 工具方法
const getTypeText = (type) => {
  const typeMap = {
    leave: '请假申请',
    expense: '报销申请'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

onMounted(() => {
  loadWorkflowList()
})
</script>

<style scoped>
.workflow-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.table-card {
  margin-bottom: 20px;
}
</style> 