{"version": 3, "file": "Big5Encoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/big5/Big5Encoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,kDAA6D;AAC7D,0DAA6E;AAE7E;;;;KAIK;AACL;IAIE,qBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,6BAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,2BAAa;YAC9B,OAAO,mBAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,8BAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,2DAA2D;QAC3D,IAAM,OAAO,GAAG,6BAAmB,CAAC,UAAU,CAAC,CAAC;QAEhD,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,OAAO,wBAAY,CAAC,UAAU,CAAC,CAAC;QAElC,mDAAmD;QACnD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAE9C,8DAA8D;QAC9D,IAAI,IAAI,GAAG,IAAI;YACb,OAAO,wBAAY,CAAC,UAAU,CAAC,CAAC;QAElC,iCAAiC;QACjC,IAAM,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC;QAE5B,4DAA4D;QAC5D,aAAa;QACb,IAAM,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAE1C,6DAA6D;QAC7D,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;IAChC,CAAC;IACH,kBAAC;AAAD,CAAC,AA/CD,IA+CC;AA/CY,kCAAW"}