{"name": "smart-office-backend", "version": "1.0.0", "description": "智能办公系统后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "db:init": "node scripts/db-init.js", "db:migrate": "npx sequelize-cli db:migrate --config src/config/database.js --migrations-path database/migrations", "db:migrate:undo": "npx sequelize-cli db:migrate:undo --config src/config/database.js --migrations-path database/migrations", "db:seed": "npx sequelize-cli db:seed:all --config src/config/database.js --seeders-path database/seeders", "db:reset": "npm run db:migrate:undo && npm run db:migrate && npm run db:seed", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all"}, "keywords": ["office", "management", "express", "mysql"], "author": "Smart Office Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.3", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "minio": "^7.1.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "sequelize": "^6.35.0", "sqlite3": "^5.1.7", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}