'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('approval_steps', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_instance_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_instances',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      step_number: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '步骤序号'
      },
      step_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '步骤名称'
      },
      step_type: {
        type: Sequelize.ENUM('approval', 'notification', 'condition', 'script'),
        allowNull: false,
        defaultValue: 'approval',
        comment: '步骤类型'
      },
      approver_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '审批人ID'
      },
      approver_type: {
        type: Sequelize.ENUM('user', 'role', 'department', 'position', 'custom'),
        allowNull: true,
        comment: '审批人类型'
      },
      status: {
        type: Sequelize.ENUM('pending', 'in_progress', 'approved', 'rejected', 'skipped', 'timeout'),
        defaultValue: 'pending',
        allowNull: false
      },
      decision: {
        type: Sequelize.ENUM('approve', 'reject', 'forward', 'return'),
        allowNull: true,
        comment: '审批决定'
      },
      comments: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '审批意见'
      },
      attachments: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '审批附件JSON数组'
      },
      assigned_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '分配时间'
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '开始处理时间'
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '完成时间'
      },
      deadline: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '截止时间'
      },
      escalated_to: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '升级到的审批人ID'
      },
      escalated_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '升级时间'
      },
      delegate_from: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '委托来源ID'
      },
      step_config: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '步骤配置JSON'
      },
      is_parallel: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: '是否并行审批'
      },
      parallel_group: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '并行组标识'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('approval_steps', ['workflow_instance_id']);
    await queryInterface.addIndex('approval_steps', ['workflow_instance_id', 'step_number']);
    await queryInterface.addIndex('approval_steps', ['approver_id']);
    await queryInterface.addIndex('approval_steps', ['status']);
    await queryInterface.addIndex('approval_steps', ['step_type']);
    await queryInterface.addIndex('approval_steps', ['assigned_at']);
    await queryInterface.addIndex('approval_steps', ['deadline']);
    await queryInterface.addIndex('approval_steps', ['parallel_group']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('approval_steps');
  }
}; 