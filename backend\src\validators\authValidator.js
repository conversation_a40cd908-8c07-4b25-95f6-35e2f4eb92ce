const { body, param } = require('express-validator');

// 注册验证规则
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度应在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度应在6-128个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  body('real_name')
    .isLength({ min: 1, max: 50 })
    .withMessage('真实姓名不能为空且不超过50个字符')
    .trim(),
  
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码')
];

// 登录验证规则
const loginValidation = [
  body('credential')
    .notEmpty()
    .withMessage('用户名或邮箱不能为空')
    .trim(),
  
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
];

// 更改密码验证规则
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('新密码长度应在6-128个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

// 重置密码请求验证规则
const requestPasswordResetValidation = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail()
];

// 重置密码验证规则
const resetPasswordValidation = [
  body('resetToken')
    .notEmpty()
    .withMessage('重置令牌不能为空'),
  
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('新密码长度应在6-128个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

// 更新用户信息验证规则
const updateProfileValidation = [
  body('real_name')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('真实姓名不能为空且不超过50个字符')
    .trim(),
  
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('性别必须是 male、female 或 other'),
  
  body('birth_date')
    .optional()
    .isISO8601()
    .withMessage('请输入有效的出生日期'),
  
  body('avatar')
    .optional()
    .isURL()
    .withMessage('头像必须是有效的URL')
];

// 邮箱验证令牌验证规则
const verifyEmailValidation = [
  param('token')
    .notEmpty()
    .withMessage('验证令牌不能为空')
];

module.exports = {
  registerValidation,
  loginValidation,
  changePasswordValidation,
  requestPasswordResetValidation,
  resetPasswordValidation,
  updateProfileValidation,
  verifyEmailValidation
}; 