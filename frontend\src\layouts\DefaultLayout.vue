<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '250px'" class="sidebar" :class="{ collapsed: isCollapse }">
      <div class="logo">
        <el-icon v-if="!isCollapse" class="logo-icon"><OfficeBuilding /></el-icon>
        <span v-if="!isCollapse" class="logo-text">智能办公系统</span>
        <el-icon v-else><OfficeBuilding /></el-icon>
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409eff"
        class="sidebar-menu"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Odometer /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        
        <el-sub-menu index="/employees">
          <template #title>
            <el-icon><User /></el-icon>
            <span>员工管理</span>
          </template>
          <el-menu-item index="/employees">员工列表</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/attendance">
          <template #title>
            <el-icon><Clock /></el-icon>
            <span>考勤管理</span>
          </template>
          <el-menu-item index="/attendance">考勤记录</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/meetings">
          <template #title>
            <el-icon><Calendar /></el-icon>
            <span>会议管理</span>
          </template>
          <el-menu-item index="/meetings">会议列表</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/files">
          <template #title>
            <el-icon><Folder /></el-icon>
            <span>文件管理</span>
          </template>
          <el-menu-item index="/files">文件列表</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/workflows">
          <template #title>
            <el-icon><DocumentCopy /></el-icon>
            <span>工作流</span>
          </template>
          <el-menu-item index="/workflows">审批流程</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/schedules">
          <template #title>
            <el-icon><Calendar /></el-icon>
            <span>日程安排</span>
          </template>
          <el-menu-item index="/schedules">我的日程</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/supplies">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>办公用品</span>
          </template>
          <el-menu-item index="/supplies">用品管理</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <el-header class="header">
        <div class="header-left">
          <el-button 
            type="text" 
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path" :to="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" class="mr-2">{{ userStore.userInfo?.name?.charAt(0) || 'U' }}</el-avatar>
              <span>{{ userStore.userInfo?.name || '用户' }}</span>
              <el-icon class="ml-1"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 内容区域 -->
      <el-main class="content">
        <router-view />
      </el-main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  OfficeBuilding,
  Odometer,
  User,
  Clock,
  Calendar,
  Folder,
  DocumentCopy,
  Box,
  Expand,
  Fold,
  ArrowDown
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 侧边栏折叠状态
const isCollapse = ref(false)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbList = []
  
  // 添加首页
  if (route.path !== '/dashboard') {
    breadcrumbList.push({ title: '首页', path: '/dashboard' })
  }
  
  // 添加当前页面
  matched.forEach(item => {
    if (item.meta.title) {
      breadcrumbList.push({
        title: item.meta.title,
        path: item.path
      })
    }
  })
  
  return breadcrumbList
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 处理用户下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        userStore.logout()
        router.push('/login')
        ElMessage.success('退出登录成功')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，自动关闭移动端侧边栏
watch(route, () => {
  if (window.innerWidth < 768) {
    isCollapse.value = true
  }
})
</script>

<style lang="scss" scoped>
.layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  background: #304156;
  transition: width 0.3s;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    background: #2b3a4a;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    
    .logo-icon {
      font-size: 24px;
      margin-right: 8px;
    }
    
    .logo-text {
      white-space: nowrap;
    }
  }
  
  .sidebar-menu {
    border: none;
    min-height: calc(100vh - 60px);
  }
  
  &.collapsed .logo {
    padding: 0;
    
    .el-icon {
      font-size: 24px;
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .header-left {
    display: flex;
    align-items: center;
    
    .collapse-btn {
      margin-right: 16px;
      font-size: 18px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    z-index: 1000;
    height: 100vh;
    
    &.collapsed {
      width: 0 !important;
      overflow: hidden;
    }
  }
  
  .main-content {
    width: 100%;
  }
  
  .content {
    padding: 12px;
  }
}
</style> 