const tokenService = require('../services/tokenService');
const { User, Role } = require('../models');
const { sequelize } = require('../models');

/**
 * JWT认证中间件
 * 验证访问令牌的有效性
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = tokenService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失',
        code: 'TOKEN_MISSING'
      });
    }

    // 验证令牌
    const decoded = tokenService.verifyAccessToken(token);
    
    // 查询用户信息和角色
    const user = await User.findByPk(decoded.id, {
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        },
        {
          model: sequelize.models.Employee,
          as: 'employee',
          required: false
        }
      ],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账号已被禁用',
        code: 'USER_INACTIVE'
      });
    }

    // 将用户信息附加到请求对象
    req.user = user;
    req.token = token;
    
    // 如果用户有关联的Employee记录，添加employeeId字段
    if (user.employee) {
      req.user.employeeId = user.employee.id;
    }
    
    // 特殊处理：为admin用户添加超级管理员权限
    if (user.username === 'admin') {
      // 确保admin用户拥有SUPER_ADMIN角色和完整权限
      const superAdminRole = {
        id: 1,
        name: '超级管理员',
        code: 'SUPER_ADMIN',
        permissions: [
          'system:*',
          'user:*',
          'role:*',
          'department:*',
          'employee:*',
          'attendance:*',
          'leave:*',
          'meeting:*',
          'workflow:*',
          'report:*'
        ]
      };
      
      // 如果用户角色中没有SUPER_ADMIN，添加它
      const hasSuper = user.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (!hasSuper) {
        user.roles = user.roles || [];
        user.roles.unshift(superAdminRole); // 添加到最前面
      }
    }
    
    next();
  } catch (error) {
    console.error('JWT authentication error:', error);
    
    let message = '令牌验证失败';
    let code = 'TOKEN_INVALID';
    
    if (error.name === 'TokenExpiredError') {
      message = '访问令牌已过期';
      code = 'TOKEN_EXPIRED';
    } else if (error.name === 'JsonWebTokenError') {
      message = '无效的访问令牌';
      code = 'TOKEN_MALFORMED';
    }

    return res.status(401).json({
      success: false,
      message,
      code
    });
  }
};

/**
 * 权限检查中间件工厂
 * @param {string|Array} requiredPermissions - 所需权限
 * @returns {Function} 中间件函数
 */
const requirePermissions = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
      const user = req.user;

      // 检查用户是否有所需权限
      for (const permission of permissions) {
        const hasPermission = await user.hasPermission(permission);
        if (!hasPermission) {
          return res.status(403).json({
            success: false,
            message: `缺少权限: ${permission}`,
            code: 'INSUFFICIENT_PERMISSIONS',
            requiredPermission: permission
          });
        }
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: '权限检查失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * 角色检查中间件工厂
 * @param {string|Array} requiredRoles - 所需角色
 * @returns {Function} 中间件函数
 */
const requireRoles = (requiredRoles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      const user = req.user;

      // 检查用户是否有所需角色
      for (const role of roles) {
        const hasRole = await user.hasRole(role);
        if (!hasRole) {
          return res.status(403).json({
            success: false,
            message: `缺少角色: ${role}`,
            code: 'INSUFFICIENT_ROLES',
            requiredRole: role
          });
        }
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({
        success: false,
        message: '角色检查失败',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

/**
 * 可选认证中间件
 * 如果提供了令牌则验证，否则继续执行
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = tokenService.extractTokenFromHeader(authHeader);

    if (token) {
      // 如果有令牌，尝试验证
      try {
        const decoded = tokenService.verifyAccessToken(token);
        const user = await User.findByPk(decoded.id, {
          include: [{
            model: Role,
            as: 'roles',
            through: { attributes: [] }
          }],
          attributes: { exclude: ['password'] }
        });

        if (user && user.status === 'active') {
          req.user = user;
          req.token = token;
        }
      } catch (error) {
        // 令牌无效，但不阻止请求继续
        console.warn('Optional auth token invalid:', error.message);
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    next(); // 即使出错也继续执行
  }
};

/**
 * 管理员权限检查
 */
const requireAdmin = requireRoles(['ADMIN', 'SUPER_ADMIN']);

/**
 * 超级管理员权限检查
 */
const requireSuperAdmin = requireRoles('SUPER_ADMIN');

/**
 * 自己或管理员权限检查
 * 用户只能访问自己的资源，或者管理员可以访问所有资源
 */
const requireSelfOrAdmin = (userIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      const targetUserId = parseInt(req.params[userIdParam]);
      const currentUserId = req.user.id;

      // 检查是否是用户本人
      if (currentUserId === targetUserId) {
        return next();
      }

      // 检查是否是管理员
      const isAdmin = await req.user.hasRole(['ADMIN', 'SUPER_ADMIN'].includes);
      if (isAdmin) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: '只能访问自己的资源',
        code: 'ACCESS_DENIED'
      });
    } catch (error) {
      console.error('Self or admin check error:', error);
      return res.status(500).json({
        success: false,
        message: '权限检查失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requirePermissions,
  requireRoles,
  optionalAuth,
  requireAdmin,
  requireSuperAdmin,
  requireSelfOrAdmin
}; 