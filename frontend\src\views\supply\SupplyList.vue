<template>
  <div class="supply-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>办公用品管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="showRequestDialog = true">
          <el-icon><ShoppingCart /></el-icon>
          申请用品
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加用品
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="filterForm.search"
            placeholder="搜索名称、编码、品牌"
            clearable
            @keyup.enter="loadSupplyList"
          />
        </el-form-item>
        <el-form-item label="类别">
          <el-select v-model="filterForm.category" placeholder="选择类别" clearable @change="loadSupplyList">
            <el-option label="文具用品" value="stationery" />
            <el-option label="电子设备" value="electronics" />
            <el-option label="办公家具" value="furniture" />
            <el-option label="消耗品" value="consumables" />
            <el-option label="设备器材" value="equipment" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable @change="loadSupplyList">
            <el-option label="正常" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="停产" value="discontinued" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadSupplyList">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 用品列表 -->
    <div class="supply-list">
      <el-table
        :data="supplies"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="编码" width="120" />
        <el-table-column prop="name" label="名称" min-width="150" />
        <el-table-column prop="category" label="类别" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="unit_price" label="单价" width="100">
          <template #default="{ row }">
            {{ row.unit_price ? `¥${row.unit_price}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="current_stock" label="库存" width="100">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.current_stock <= row.min_stock }">
              {{ row.current_stock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editSupply(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="requestSupply(row)">
              申请
            </el-button>
            <el-button type="danger" size="small" @click="deleteSupply(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑用品对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editMode ? '编辑用品' : '添加用品'"
      width="600px"
      @close="resetSupplyForm"
    >
      <el-form
        ref="supplyFormRef"
        :model="supplyForm"
        :rules="supplyRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="supplyForm.name" placeholder="请输入用品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码" prop="code">
              <el-input v-model="supplyForm.code" placeholder="请输入用品编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类别" prop="category">
              <el-select v-model="supplyForm.category" placeholder="选择类别">
                <el-option label="文具用品" value="stationery" />
                <el-option label="电子设备" value="electronics" />
                <el-option label="办公家具" value="furniture" />
                <el-option label="消耗品" value="consumables" />
                <el-option label="设备器材" value="equipment" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌">
              <el-input v-model="supplyForm.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="型号">
              <el-input v-model="supplyForm.model" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位">
              <el-input v-model="supplyForm.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单价">
              <el-input-number
                v-model="supplyForm.unit_price"
                :precision="2"
                :min="0"
                placeholder="请输入单价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前库存">
              <el-input-number
                v-model="supplyForm.current_stock"
                :min="0"
                placeholder="请输入库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最低库存">
              <el-input-number
                v-model="supplyForm.min_stock"
                :min="0"
                placeholder="请输入最低库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存放位置">
              <el-input v-model="supplyForm.location" placeholder="请输入存放位置" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="供应商">
          <el-input v-model="supplyForm.supplier" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="供应商联系方式">
          <el-input v-model="supplyForm.supplier_contact" placeholder="请输入供应商联系方式" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="supplyForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入用品描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSupply" :loading="saving">
            {{ editMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 申请用品对话框 -->
    <el-dialog
      v-model="showRequestDialog"
      title="申请用品"
      width="500px"
      @close="resetRequestForm"
    >
      <el-form
        ref="requestFormRef"
        :model="requestForm"
        :rules="requestRules"
        label-width="100px"
      >
        <el-form-item label="选择用品" prop="supply_id">
          <el-select
            v-model="requestForm.supply_id"
            placeholder="请选择要申请的用品"
            filterable
            style="width: 100%"
            @change="handleSupplyChange"
          >
            <el-option
              v-for="supply in supplies"
              :key="supply.id"
              :label="`${supply.name} (${supply.code})`"
              :value="supply.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请数量" prop="quantity">
          <el-input-number
            v-model="requestForm.quantity"
            :min="1"
            placeholder="请输入申请数量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select v-model="requestForm.urgency" placeholder="选择紧急程度">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="期望日期">
          <el-date-picker
            v-model="requestForm.expected_date"
            type="date"
            placeholder="选择期望到货日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="申请用途" prop="purpose">
          <el-input
            v-model="requestForm.purpose"
            type="textarea"
            :rows="3"
            placeholder="请说明申请用途"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRequestDialog = false">取消</el-button>
          <el-button type="primary" @click="submitRequest" :loading="requesting">
            提交申请
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus, ShoppingCart } from '@element-plus/icons-vue'
import axios from '@/utils/axios'
import router from '@/router'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const requesting = ref(false)
const supplies = ref([])
const showCreateDialog = ref(false)
const showRequestDialog = ref(false)
const editMode = ref(false)
const supplyFormRef = ref()
const requestFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  search: '',
  category: '',
  status: ''
})

// 用品表单
const supplyForm = reactive({
  id: null,
  name: '',
  code: '',
  category: 'other',
  brand: '',
  model: '',
  unit: '个',
  unit_price: null,
  current_stock: 0,
  min_stock: 10,
  location: '',
  supplier: '',
  supplier_contact: '',
  description: ''
})

// 申请表单
const requestForm = reactive({
  supply_id: null,
  quantity: 1,
  urgency: 'medium',
  expected_date: '',
  purpose: ''
})

// 表单验证规则
const supplyRules = {
  name: [
    { required: true, message: '请输入用品名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入用品编码', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择用品类别', trigger: 'change' }
  ]
}

const requestRules = {
  supply_id: [
    { required: true, message: '请选择要申请的用品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入申请数量', trigger: 'blur' }
  ],
  purpose: [
    { required: true, message: '请说明申请用途', trigger: 'blur' }
  ]
}

// 方法定义
const loadSupplyList = async (showRetryOption = false) => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size
    }

    if (filterForm.search) {
      params.search = filterForm.search
    }

    if (filterForm.category) {
      params.category = filterForm.category
    }

    if (filterForm.status) {
      params.status = filterForm.status
    }

    const response = await axios.get('/v1/office-supplies', { 
      params,
      errorContext: { operation: '获取办公用品列表' }
    })

    if (response.data.success) {
      supplies.value = response.data.data.supplies
      pagination.total = response.data.data.pagination.total
      
      // 如果之前有错误，现在成功了，显示恢复消息
      if (showRetryOption) {
        ElMessage.success('办公用品列表加载成功')
      }
    }
  } catch (error) {
    console.error('获取用品列表失败:', error)
    
    // 使用增强的错误处理
    handleSupplyListError(error, 'load')
  } finally {
    loading.value = false
  }
}

// 增强的错误处理函数
const handleSupplyListError = (error, operation = 'load') => {
  const classification = error.classification || { severity: 'medium', canRetry: false }
  
  // 根据错误类型提供不同的处理方案
  if (classification.canRetry) {
    ElMessageBox.confirm(
      `${error.userMessage || '获取办公用品列表失败'}，是否重试？`,
      '操作失败',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false
      }
    ).then(() => {
      // 用户选择重试
      if (operation === 'load') {
        loadSupplyList(true)
      }
    }).catch(() => {
      // 用户取消，显示离线提示
      if (classification.isNetworkError) {
        showOfflineMode()
      }
    })
  } else if (classification.needsAuth) {
    // 认证错误，提示重新登录
    ElMessageBox.alert(
      '登录已过期，请重新登录',
      '认证失败',
      {
        confirmButtonText: '重新登录',
        type: 'warning'
      }
    ).then(() => {
      // 跳转到登录页
      router.push('/login')
    })
  } else {
    // 其他错误，显示错误信息和建议
    const suggestions = getSuggestionsByError(classification)
    if (suggestions.length > 0) {
      ElNotification({
        title: '操作建议',
        message: suggestions.join('；'),
        type: 'info',
        duration: 8000
      })
    }
  }
}

// 根据错误类型提供建议
const getSuggestionsByError = (classification) => {
  const suggestions = []
  
  switch (classification.suggestedAction) {
    case 'check_network':
      suggestions.push('请检查网络连接')
      suggestions.push('尝试刷新页面')
      break
    case 'check_server':
      suggestions.push('服务器可能暂时不可用')
      suggestions.push('请稍后再试或联系管理员')
      break
    case 'refresh_page':
      suggestions.push('请刷新页面重试')
      break
    case 'contact_support':
      suggestions.push('请联系技术支持')
      break
    default:
      suggestions.push('请稍后重试')
  }
  
  return suggestions
}

// 离线模式提示
const showOfflineMode = () => {
  ElNotification({
    title: '离线模式',
    message: '当前网络不可用，部分功能受限。网络恢复后将自动同步数据。',
    type: 'warning',
    duration: 0, // 不自动关闭
    showClose: true
  })
}

const saveSupply = async () => {
  try {
    await supplyFormRef.value.validate()

    saving.value = true

    const supplyData = {
      name: supplyForm.name,
      code: supplyForm.code,
      category: supplyForm.category,
      brand: supplyForm.brand,
      model: supplyForm.model,
      unit: supplyForm.unit,
      unit_price: supplyForm.unit_price,
      current_stock: supplyForm.current_stock,
      min_stock: supplyForm.min_stock,
      location: supplyForm.location,
      supplier: supplyForm.supplier,
      supplier_contact: supplyForm.supplier_contact,
      description: supplyForm.description
    }

    const operation = editMode.value ? '更新办公用品' : '创建办公用品'
    const config = { errorContext: { operation } }

    if (editMode.value) {
      await axios.put(`/v1/office-supplies/${supplyForm.id}`, supplyData, config)
      ElMessage.success('用品更新成功')
    } else {
      await axios.post('/v1/office-supplies', supplyData, config)
      ElMessage.success('用品创建成功')
    }

    showCreateDialog.value = false
    resetSupplyForm()
    await loadSupplyList()
  } catch (error) {
    console.error('保存用品失败:', error)
    
    // 使用增强的错误处理
    handleSaveSupplyError(error)
  } finally {
    saving.value = false
  }
}

// 保存用品错误处理
const handleSaveSupplyError = (error) => {
  const classification = error.classification || { severity: 'medium', canRetry: false }
  
  if (classification.type === 'VALIDATION_ERROR') {
    // 验证错误，显示具体的验证信息
    const errorData = error.response?.data?.error
    if (errorData?.validationErrors) {
      const validationMessages = errorData.validationErrors.map(err => 
        `${err.field}: ${err.message}`
      ).join('\n')
      
      ElMessageBox.alert(
        validationMessages,
        '数据验证失败',
        { type: 'warning' }
      )
    } else {
      ElMessage.warning(error.userMessage || '请检查输入数据的格式和完整性')
    }
  } else if (classification.type === 'CONFLICT_ERROR') {
    // 冲突错误（如编码重复）
    ElMessageBox.alert(
      error.userMessage || '用品编码已存在，请使用不同的编码',
      '数据冲突',
      { type: 'warning' }
    )
  } else if (classification.canRetry) {
    // 可重试的错误
    ElMessageBox.confirm(
      `${error.userMessage || '保存失败'}，是否重试？`,
      '保存失败',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      saveSupply()
    })
  } else {
    // 其他错误
    ElMessage.error(error.userMessage || '保存失败，请稍后重试')
  }
}

const editSupply = (supply) => {
  editMode.value = true
  supplyForm.id = supply.id
  supplyForm.name = supply.name
  supplyForm.code = supply.code
  supplyForm.category = supply.category
  supplyForm.brand = supply.brand || ''
  supplyForm.model = supply.model || ''
  supplyForm.unit = supply.unit
  supplyForm.unit_price = supply.unit_price
  supplyForm.current_stock = supply.current_stock
  supplyForm.min_stock = supply.min_stock
  supplyForm.location = supply.location || ''
  supplyForm.supplier = supply.supplier || ''
  supplyForm.supplier_contact = supply.supplier_contact || ''
  supplyForm.description = supply.description || ''
  showCreateDialog.value = true
}

const deleteSupply = async (id) => {
  try {
    await ElMessageBox.confirm('确认删除这个用品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.delete(`/v1/office-supplies/${id}`)
    ElMessage.success('用品删除成功')
    await loadSupplyList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用品失败:', error)
      ElMessage.error('删除用品失败')
    }
  }
}

const requestSupply = (supply) => {
  requestForm.supply_id = supply.id
  showRequestDialog.value = true
}

const submitRequest = async () => {
  try {
    await requestFormRef.value.validate()

    requesting.value = true

    const requestData = {
      supply_id: requestForm.supply_id,
      quantity: requestForm.quantity,
      urgency: requestForm.urgency,
      expected_date: requestForm.expected_date,
      purpose: requestForm.purpose
    }

    await axios.post('/v1/supply-requests', requestData)
    ElMessage.success('申请提交成功')

    showRequestDialog.value = false
    resetRequestForm()
  } catch (error) {
    console.error('提交申请失败:', error)
    ElMessage.error('提交申请失败')
  } finally {
    requesting.value = false
  }
}

const resetSupplyForm = () => {
  editMode.value = false
  supplyForm.id = null
  supplyForm.name = ''
  supplyForm.code = ''
  supplyForm.category = 'other'
  supplyForm.brand = ''
  supplyForm.model = ''
  supplyForm.unit = '个'
  supplyForm.unit_price = null
  supplyForm.current_stock = 0
  supplyForm.min_stock = 10
  supplyForm.location = ''
  supplyForm.supplier = ''
  supplyForm.supplier_contact = ''
  supplyForm.description = ''
}

const resetRequestForm = () => {
  requestForm.supply_id = null
  requestForm.quantity = 1
  requestForm.urgency = 'medium'
  requestForm.expected_date = ''
  requestForm.purpose = ''
}

const resetFilter = () => {
  filterForm.search = ''
  filterForm.category = ''
  filterForm.status = ''
  loadSupplyList()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadSupplyList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadSupplyList()
}

const handleSupplyChange = (supplyId) => {
  // 可以在这里根据选择的用品更新相关信息
}

// 工具方法
const getCategoryLabel = (category) => {
  const labels = {
    stationery: '文具用品',
    electronics: '电子设备',
    furniture: '办公家具',
    consumables: '消耗品',
    equipment: '设备器材',
    other: '其他'
  }
  return labels[category] || category
}

const getCategoryTagType = (category) => {
  const types = {
    stationery: '',
    electronics: 'success',
    furniture: 'warning',
    consumables: 'info',
    equipment: 'danger',
    other: ''
  }
  return types[category] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    active: '正常',
    inactive: '停用',
    discontinued: '停产'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    inactive: 'warning',
    discontinued: 'danger'
  }
  return types[status] || ''
}

// 页面加载时获取数据
onMounted(() => {
  loadSupplyList()
})
</script>

<style scoped>
.supply-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.low-stock {
  color: #f56c6c;
  font-weight: bold;
}
</style>
