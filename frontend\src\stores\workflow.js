import { defineStore } from 'pinia';
import { workflowAPI } from '@/api/workflow';

export const useWorkflowStore = defineStore('workflow', {
  state: () => ({
    // 工作流定义相关
    workflowDefinitions: [],
    activeDefinitions: [],
    currentDefinition: null,
    definitionStats: {
      total: 0,
      draft: 0,
      active: 0,
      inactive: 0
    },

    // 工作流实例相关
    workflowInstances: [],
    myInstances: [],
    currentInstance: null,
    instanceStats: {
      total: 0,
      running: 0,
      completed: 0,
      rejected: 0,
      cancelled: 0
    },

    // 审批任务相关
    pendingTasks: [],
    myTasks: [],
    currentTask: null,
    taskStats: {
      pending: 0,
      processed: 0,
      overdue: 0
    },

    // 通用状态
    loading: {
      definitions: false,
      instances: false,
      tasks: false,
      processing: false
    },
    pagination: {
      definitions: { page: 1, pageSize: 20, total: 0 },
      instances: { page: 1, pageSize: 20, total: 0 },
      tasks: { page: 1, pageSize: 20, total: 0 }
    },
    filters: {
      definitions: { category: '', status: '', keyword: '' },
      instances: { status: '', initiator: '', keyword: '' },
      tasks: { category: '', priority: '', overdue: false }
    }
  }),

  getters: {
    // 获取可用的工作流分类
    availableCategories() {
      const categories = this.workflowDefinitions.map(def => def.category);
      return [...new Set(categories)];
    },

    // 获取待处理任务数量
    pendingTaskCount() {
      return this.pendingTasks.length;
    },

    // 获取进行中的实例数量
    runningInstanceCount() {
      return this.workflowInstances.filter(instance => 
        instance.status === 'running'
      ).length;
    },

    // 获取工作流定义统计摘要
    definitionSummary() {
      return {
        total: this.workflowDefinitions.length,
        categories: this.availableCategories.length,
        active: this.workflowDefinitions.filter(def => def.status === 'active').length
      };
    },

    // 获取实例按状态分组
    instancesByStatus() {
      return this.workflowInstances.reduce((acc, instance) => {
        if (!acc[instance.status]) {
          acc[instance.status] = [];
        }
        acc[instance.status].push(instance);
        return acc;
      }, {});
    },

    // 获取优先级高的待处理任务
    highPriorityTasks() {
      return this.pendingTasks.filter(task => 
        task.instance?.priority === 'high' || task.instance?.priority === 'urgent'
      );
    }
  },

  actions: {
    // 工作流定义管理
    async fetchWorkflowDefinitions(options = {}) {
      this.loading.definitions = true;
      try {
        const params = {
          page: options.page || this.pagination.definitions.page,
          pageSize: options.pageSize || this.pagination.definitions.pageSize,
          ...this.filters.definitions,
          ...options
        };

        const response = await workflowAPI.getDefinitions(params);
        
        this.workflowDefinitions = response.data.definitions;
        this.pagination.definitions = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('获取工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.definitions = false;
      }
    },

    async fetchActiveDefinitions(category = null) {
      try {
        const response = await workflowAPI.getActiveDefinitions(category);
        this.activeDefinitions = response.data;
        return response.data;
      } catch (error) {
        console.error('获取活跃工作流定义失败:', error);
        throw error;
      }
    },

    async fetchDefinitionById(definitionId) {
      try {
        const response = await workflowAPI.getDefinitionById(definitionId);
        this.currentDefinition = response.data;
        return response.data;
      } catch (error) {
        console.error('获取工作流定义详情失败:', error);
        throw error;
      }
    },

    async createWorkflowDefinition(data) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.createDefinition(data);
        
        // 添加到列表顶部
        this.workflowDefinitions.unshift(response.data);
        
        return response.data;
      } catch (error) {
        console.error('创建工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    async updateWorkflowDefinition(definitionId, data) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.updateDefinition(definitionId, data);
        
        // 更新列表中的数据
        const index = this.workflowDefinitions.findIndex(def => def.id === definitionId);
        if (index !== -1) {
          this.workflowDefinitions[index] = response.data;
        }
        
        // 更新当前定义
        if (this.currentDefinition?.id === definitionId) {
          this.currentDefinition = response.data;
        }
        
        return response.data;
      } catch (error) {
        console.error('更新工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    async activateDefinition(definitionId) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.activateDefinition(definitionId);
        
        // 更新状态
        this.updateDefinitionInList(definitionId, { status: 'active' });
        
        return response.data;
      } catch (error) {
        console.error('激活工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    async deactivateDefinition(definitionId) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.deactivateDefinition(definitionId);
        
        // 更新状态
        this.updateDefinitionInList(definitionId, { status: 'inactive' });
        
        return response.data;
      } catch (error) {
        console.error('停用工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    async cloneDefinition(definitionId, newName) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.cloneDefinition(definitionId, { newName });
        
        // 添加到列表
        this.workflowDefinitions.unshift(response.data);
        
        return response.data;
      } catch (error) {
        console.error('复制工作流定义失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    // 工作流实例管理
    async fetchWorkflowInstances(options = {}) {
      this.loading.instances = true;
      try {
        const params = {
          page: options.page || this.pagination.instances.page,
          pageSize: options.pageSize || this.pagination.instances.pageSize,
          ...this.filters.instances,
          ...options
        };

        const response = await workflowAPI.getInstances(params);
        
        this.workflowInstances = response.data.instances;
        this.pagination.instances = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('获取工作流实例失败:', error);
        throw error;
      } finally {
        this.loading.instances = false;
      }
    },

    async fetchMyInstances(options = {}) {
      this.loading.instances = true;
      try {
        const response = await workflowAPI.getMyInstances(options);
        this.myInstances = response.data.instances;
        return response.data;
      } catch (error) {
        console.error('获取我的工作流实例失败:', error);
        throw error;
      } finally {
        this.loading.instances = false;
      }
    },

    async fetchInstanceById(instanceId) {
      try {
        const response = await workflowAPI.getInstanceById(instanceId);
        this.currentInstance = response.data;
        return response.data;
      } catch (error) {
        console.error('获取工作流实例详情失败:', error);
        throw error;
      }
    },

    async startWorkflowInstance(data) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.startInstance(data);
        
        // 添加到实例列表
        this.workflowInstances.unshift(response.data);
        this.myInstances.unshift(response.data);
        
        return response.data;
      } catch (error) {
        console.error('启动工作流实例失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    async cancelInstance(instanceId, reason) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.cancelInstance(instanceId, { reason });
        
        // 更新实例状态
        this.updateInstanceInList(instanceId, { status: 'cancelled' });
        
        return response.data;
      } catch (error) {
        console.error('取消工作流实例失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    // 审批任务管理
    async fetchPendingTasks(options = {}) {
      this.loading.tasks = true;
      try {
        const params = {
          page: options.page || this.pagination.tasks.page,
          pageSize: options.pageSize || this.pagination.tasks.pageSize,
          ...this.filters.tasks,
          ...options
        };

        const response = await workflowAPI.getPendingTasks(params);
        
        this.pendingTasks = response.data.tasks;
        this.pagination.tasks = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('获取待审批任务失败:', error);
        throw error;
      } finally {
        this.loading.tasks = false;
      }
    },

    async fetchTaskById(taskId) {
      try {
        const response = await workflowAPI.getTaskById(taskId);
        this.currentTask = response.data;
        return response.data;
      } catch (error) {
        console.error('获取审批任务详情失败:', error);
        throw error;
      }
    },

    async processTask(taskId, action, comment = '', attachments = []) {
      this.loading.processing = true;
      try {
        const response = await workflowAPI.processTask(taskId, {
          action,
          comment,
          attachments
        });

        // 从待处理任务中移除
        this.pendingTasks = this.pendingTasks.filter(task => task.id !== taskId);
        
        return response.data;
      } catch (error) {
        console.error('处理审批任务失败:', error);
        throw error;
      } finally {
        this.loading.processing = false;
      }
    },

    // 统计数据
    async fetchStatistics(options = {}) {
      try {
        const response = await workflowAPI.getStatistics(options);
        
        if (response.data.instances) {
          this.instanceStats = response.data.instances;
        }
        if (response.data.approvals) {
          this.taskStats = response.data.approvals;
        }
        
        return response.data;
      } catch (error) {
        console.error('获取工作流统计失败:', error);
        throw error;
      }
    },

    // 过滤器管理
    updateDefinitionFilters(filters) {
      this.filters.definitions = { ...this.filters.definitions, ...filters };
    },

    updateInstanceFilters(filters) {
      this.filters.instances = { ...this.filters.instances, ...filters };
    },

    updateTaskFilters(filters) {
      this.filters.tasks = { ...this.filters.tasks, ...filters };
    },

    // 清除数据
    clearCurrentDefinition() {
      this.currentDefinition = null;
    },

    clearCurrentInstance() {
      this.currentInstance = null;
    },

    clearCurrentTask() {
      this.currentTask = null;
    },

    // 工具方法
    updateDefinitionInList(definitionId, updates) {
      const index = this.workflowDefinitions.findIndex(def => def.id === definitionId);
      if (index !== -1) {
        this.workflowDefinitions[index] = { ...this.workflowDefinitions[index], ...updates };
      }
    },

    updateInstanceInList(instanceId, updates) {
      const index = this.workflowInstances.findIndex(instance => instance.id === instanceId);
      if (index !== -1) {
        this.workflowInstances[index] = { ...this.workflowInstances[index], ...updates };
      }
      
      const myIndex = this.myInstances.findIndex(instance => instance.id === instanceId);
      if (myIndex !== -1) {
        this.myInstances[myIndex] = { ...this.myInstances[myIndex], ...updates };
      }
    },

    // 重置状态
    resetState() {
      this.workflowDefinitions = [];
      this.activeDefinitions = [];
      this.currentDefinition = null;
      this.workflowInstances = [];
      this.myInstances = [];
      this.currentInstance = null;
      this.pendingTasks = [];
      this.myTasks = [];
      this.currentTask = null;
      
      this.pagination = {
        definitions: { page: 1, pageSize: 20, total: 0 },
        instances: { page: 1, pageSize: 20, total: 0 },
        tasks: { page: 1, pageSize: 20, total: 0 }
      };
      
      this.filters = {
        definitions: { category: '', status: '', keyword: '' },
        instances: { status: '', initiator: '', keyword: '' },
        tasks: { category: '', priority: '', overdue: false }
      };
    }
  }
}); 