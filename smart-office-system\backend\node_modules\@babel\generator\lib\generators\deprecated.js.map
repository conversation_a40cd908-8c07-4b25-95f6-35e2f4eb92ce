{"version": 3, "names": ["addDeprecatedGenerators", "PrinterClass", "deprecatedBabel7Generators", "Noop", "TSExpressionWithTypeArguments", "node", "print", "expression", "typeParameters", "DecimalLiteral", "raw", "getPossibleRaw", "format", "minified", "undefined", "word", "value", "Object", "assign", "prototype"], "sources": ["../../src/generators/deprecated.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport type DeprecatedBabel7ASTTypes =\n  | \"Noop\"\n  | \"TSExpressionWithTypeArguments\"\n  | \"DecimalLiteral\";\n\nexport function addDeprecatedGenerators(PrinterClass: typeof Printer) {\n  // Add Babel 7 generator methods that is removed in Babel 8\n  if (!process.env.BABEL_8_BREAKING) {\n    const deprecatedBabel7Generators = {\n      Noop(this: Printer) {},\n\n      TSExpressionWithTypeArguments(\n        this: Printer,\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        node: t.TSExpressionWithTypeArguments,\n      ) {\n        this.print(node.expression);\n        this.print(node.typeParameters);\n      },\n\n      DecimalLiteral(this: Printer, node: any) {\n        const raw = this.getPossibleRaw(node);\n        if (!this.format.minified && raw !== undefined) {\n          this.word(raw);\n          return;\n        }\n        this.word(node.value + \"m\");\n      },\n    } satisfies Record<\n      DeprecatedBabel7ASTTypes,\n      (this: Printer, node: any) => void\n    >;\n    Object.assign(PrinterClass.prototype, deprecatedBabel7Generators);\n  }\n}\n"], "mappings": ";;;;;;AAQO,SAASA,uBAAuBA,CAACC,YAA4B,EAAE;EAEjC;IACjC,MAAMC,0BAA0B,GAAG;MACjCC,IAAIA,CAAA,EAAgB,CAAC,CAAC;MAEtBC,6BAA6BA,CAG3BC,IAAqC,EACrC;QACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,UAAU,CAAC;QAC3B,IAAI,CAACD,KAAK,CAACD,IAAI,CAACG,cAAc,CAAC;MACjC,CAAC;MAEDC,cAAcA,CAAgBJ,IAAS,EAAE;QACvC,MAAMK,GAAG,GAAG,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC;QACrC,IAAI,CAAC,IAAI,CAACO,MAAM,CAACC,QAAQ,IAAIH,GAAG,KAAKI,SAAS,EAAE;UAC9C,IAAI,CAACC,IAAI,CAACL,GAAG,CAAC;UACd;QACF;QACA,IAAI,CAACK,IAAI,CAACV,IAAI,CAACW,KAAK,GAAG,GAAG,CAAC;MAC7B;IACF,CAGC;IACDC,MAAM,CAACC,MAAM,CAACjB,YAAY,CAACkB,SAAS,EAAEjB,0BAA0B,CAAC;EACnE;AACF", "ignoreList": []}