'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('workflow_definitions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '工作流名称'
      },
      code: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '工作流编码'
      },
      category: {
        type: Sequelize.ENUM('leave', 'expense', 'purchase', 'recruitment', 'other'),
        allowNull: false,
        comment: '工作流类别'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '工作流描述'
      },
      version: {
        type: Sequelize.STRING(10),
        allowNull: false,
        defaultValue: '1.0',
        comment: '版本号'
      },
      steps_config: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '步骤配置JSON，定义审批流程和规则'
      },
      form_config: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '表单配置JSON，定义表单字段和验证规则'
      },
      conditions: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '触发条件JSON，定义何时使用此工作流'
      },
      auto_approval_rules: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '自动审批规则JSON'
      },
      escalation_rules: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '升级规则JSON，超时处理等'
      },
      notification_config: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '通知配置JSON'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
        comment: '创建者ID'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_definitions', ['code']);
    await queryInterface.addIndex('workflow_definitions', ['category']);
    await queryInterface.addIndex('workflow_definitions', ['is_active']);
    await queryInterface.addIndex('workflow_definitions', ['creator_id']);
    await queryInterface.addIndex('workflow_definitions', ['version']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('workflow_definitions');
  }
}; 