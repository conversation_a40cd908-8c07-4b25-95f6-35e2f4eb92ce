<template>
  <div class="file-list">
    <div class="page-header">
      <h1>文件管理</h1>
      <div class="header-actions">
        <el-upload
          :action="uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :show-file-list="false"
          multiple
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            上传文件
          </el-button>
        </el-upload>
        <el-button @click="createFolder">
          <el-icon><FolderAdd /></el-icon>
          新建文件夹
        </el-button>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <el-card class="breadcrumb-card">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item @click="navigateToFolder('')" class="breadcrumb-link">
          根目录
        </el-breadcrumb-item>
        <el-breadcrumb-item
          v-for="(folder, index) in breadcrumbPath"
          :key="index"
          @click="navigateToFolder(folder.path)"
          :class="index === breadcrumbPath.length - 1 ? '' : 'breadcrumb-link'"
        >
          {{ folder.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </el-card>

    <!-- 筛选和操作区域 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件..."
            prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="fileTypeFilter" placeholder="文件类型" clearable>
            <el-option label="所有类型" value="" />
            <el-option label="文档" value="document" />
            <el-option label="图片" value="image" />
            <el-option label="视频" value="video" />
            <el-option label="压缩包" value="archive" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <div class="view-controls">
            <el-button-group>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : ''"
                @click="viewMode = 'grid'"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button
                :type="viewMode === 'list' ? 'primary' : ''"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 文件列表 -->
    <el-card class="file-content-card">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="file-grid">
        <div
          v-for="item in filteredFileList"
          :key="item.id"
          class="file-item"
          @click="handleItemClick(item)"
          @contextmenu.prevent="showContextMenu($event, item)"
        >
          <div class="file-icon">
            <el-icon v-if="item.type === 'folder'" :size="48" color="#409eff">
              <Folder />
            </el-icon>
            <el-icon v-else :size="48" :color="getFileIconColor(item.fileType)">
              <component :is="getFileIcon(item.fileType)" />
            </el-icon>
          </div>
          <div class="file-name" :title="item.name">{{ item.name }}</div>
          <div v-if="item.type === 'file'" class="file-info">
            <span class="file-size">{{ formatFileSize(item.size) }}</span>
            <span class="file-date">{{ formatDate(item.updateTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <el-table
        v-else
        :data="filteredFileList"
        v-loading="loading"
        stripe
        @row-click="handleItemClick"
        @row-contextmenu="showContextMenu"
      >
        <el-table-column label="名称" min-width="300">
          <template #default="{ row }">
            <div class="file-name-cell">
              <el-icon v-if="row.type === 'folder'" color="#409eff">
                <Folder />
              </el-icon>
              <el-icon v-else :color="getFileIconColor(row.fileType)">
                <component :is="getFileIcon(row.fileType)" />
              </el-icon>
              <span class="name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120">
          <template #default="{ row }">
            {{ row.type === 'file' ? formatFileSize(row.size) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="fileType" label="类型" width="100">
          <template #default="{ row }">
            {{ row.type === 'folder' ? '文件夹' : getFileTypeText(row.fileType) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="uploaderName" label="上传者" width="120" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.type === 'file'"
              type="primary"
              size="small"
              @click.stop="downloadFile(row)"
            >
              下载
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click.stop="renameItem(row)"
            >
              重命名
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click.stop="deleteItem(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenu.visible"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      class="context-menu"
      ref="contextMenuRef"
    >
      <div v-if="contextMenu.item?.type === 'file'" class="menu-item" @click="downloadFile(contextMenu.item)">
        <el-icon><Download /></el-icon>
        下载
      </div>
      <div class="menu-item" @click="renameItem(contextMenu.item)">
        <el-icon><Edit /></el-icon>
        重命名
      </div>
      <div class="menu-item" @click="copyItem(contextMenu.item)">
        <el-icon><CopyDocument /></el-icon>
        复制
      </div>
      <div class="menu-item danger" @click="deleteItem(contextMenu.item)">
        <el-icon><Delete /></el-icon>
        删除
      </div>
    </div>

    <!-- 新建文件夹对话框 -->
    <el-dialog v-model="showCreateFolderDialog" title="新建文件夹" width="400px">
      <el-form :model="folderForm" :rules="folderRules" ref="folderFormRef">
        <el-form-item label="文件夹名称" prop="name">
          <el-input
            v-model="folderForm.name"
            placeholder="请输入文件夹名称"
            @keyup.enter="createFolderConfirm"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolderConfirm">创建</el-button>
      </template>
    </el-dialog>

    <!-- 重命名对话框 -->
    <el-dialog v-model="showRenameDialog" title="重命名" width="400px">
      <el-form :model="renameForm" :rules="renameRules" ref="renameFormRef">
        <el-form-item label="新名称" prop="name">
          <el-input
            v-model="renameForm.name"
            placeholder="请输入新名称"
            @keyup.enter="renameConfirm"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRenameDialog = false">取消</el-button>
        <el-button type="primary" @click="renameConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  FolderAdd,
  Folder,
  Document,
  Picture,
  VideoCamera,
  Download,
  Edit,
  Delete,
  CopyDocument,
  Grid,
  List
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref('grid')
const searchKeyword = ref('')
const fileTypeFilter = ref('')
const currentPath = ref('')
const showCreateFolderDialog = ref(false)
const showRenameDialog = ref(false)
const folderFormRef = ref(null)
const renameFormRef = ref(null)
const contextMenuRef = ref(null)

// 上传配置
const uploadUrl = ref('/api/files/upload')

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 面包屑路径
const breadcrumbPath = computed(() => {
  if (!currentPath.value) return []
  const paths = currentPath.value.split('/')
  const result = []
  let path = ''
  paths.forEach((folder, index) => {
    path += (index === 0 ? '' : '/') + folder
    result.push({ name: folder, path })
  })
  return result
})

// 文件列表
const fileList = ref([
  {
    id: 1,
    name: '项目文档',
    type: 'folder',
    path: 'projects',
    updateTime: '2024-01-15 10:30:00',
    uploaderName: '张三'
  },
  {
    id: 2,
    name: '产品需求文档.docx',
    type: 'file',
    fileType: 'document',
    size: 1024000,
    path: 'product-requirements.docx',
    updateTime: '2024-01-15 09:15:00',
    uploaderName: '李四'
  },
  {
    id: 3,
    name: '系统架构图.png',
    type: 'file',
    fileType: 'image',
    size: 512000,
    path: 'architecture.png',
    updateTime: '2024-01-14 16:20:00',
    uploaderName: '王五'
  }
])

// 筛选后的文件列表
const filteredFileList = computed(() => {
  let result = fileList.value
  
  // 搜索筛选
  if (searchKeyword.value) {
    result = result.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  // 类型筛选
  if (fileTypeFilter.value) {
    result = result.filter(item =>
      item.type === 'folder' || item.fileType === fileTypeFilter.value
    )
  }
  
  return result
})

// 右键菜单
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  item: null
})

// 新建文件夹表单
const folderForm = reactive({
  name: ''
})

const folderRules = {
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 重命名表单
const renameForm = reactive({
  name: '',
  id: ''
})

const renameRules = {
  name: [
    { required: true, message: '请输入新名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleSearch = () => {
  pagination.page = 1
  // 这里可以添加搜索逻辑
}

const handleItemClick = (item) => {
  if (item.type === 'folder') {
    navigateToFolder(item.path)
  } else {
    // 预览文件或打开详情
    previewFile(item)
  }
}

const navigateToFolder = (path) => {
  currentPath.value = path
  loadFileList()
}

const loadFileList = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取文件列表
    setTimeout(() => {
      pagination.total = 100
      loading.value = false
    }, 1000)
  } catch (error) {
    ElMessage.error('加载文件列表失败')
    loading.value = false
  }
}

const beforeUpload = (file) => {
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过50MB')
    return false
  }
  return true
}

const handleUploadSuccess = (response) => {
  ElMessage.success('文件上传成功')
  loadFileList()
}

const handleUploadError = (error) => {
  ElMessage.error('文件上传失败')
}

const createFolder = () => {
  folderForm.name = ''
  showCreateFolderDialog.value = true
}

const createFolderConfirm = async () => {
  try {
    await folderFormRef.value.validate()
    // 这里应该调用API创建文件夹
    ElMessage.success('文件夹创建成功')
    showCreateFolderDialog.value = false
    loadFileList()
  } catch (error) {
    console.log('表单验证失败', error)
  }
}

const downloadFile = (file) => {
  // 创建下载链接
  const link = document.createElement('a')
  link.href = `/api/files/download/${file.id}`
  link.download = file.name
  link.click()
  ElMessage.success('开始下载')
}

const previewFile = (file) => {
  // 根据文件类型决定预览方式
  if (file.fileType === 'image') {
    // 图片预览
    window.open(`/api/files/preview/${file.id}`)
  } else {
    ElMessage.info('该文件类型暂不支持预览')
  }
}

const renameItem = (item) => {
  renameForm.name = item.name
  renameForm.id = item.id
  showRenameDialog.value = true
  hideContextMenu()
}

const renameConfirm = async () => {
  try {
    await renameFormRef.value.validate()
    // 这里应该调用API重命名
    ElMessage.success('重命名成功')
    showRenameDialog.value = false
    loadFileList()
  } catch (error) {
    console.log('表单验证失败', error)
  }
}

const copyItem = (item) => {
  // 复制到剪贴板（文件路径或链接）
  navigator.clipboard.writeText(item.name).then(() => {
    ElMessage.success('已复制到剪贴板')
  })
  hideContextMenu()
}

const deleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确认删除 "${item.name}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里应该调用API删除
    // const response = await api.delete(`/files/${item.id}`)
    
    // 暂时从本地列表中移除
    const index = fileList.value.findIndex(file => file.id === item.id)
    if (index > -1) {
      fileList.value.splice(index, 1)
      // 更新分页总数
      pagination.total = Math.max(0, pagination.total - 1)
    }
    
    ElMessage.success('删除成功')
    // 如果当前页没有数据且不是第一页，则跳转到上一页
    if (fileList.value.length === 0 && pagination.page > 1) {
      pagination.page -= 1
      loadFileList()
    }
  } catch (error) {
    // 用户取消删除
  }
  hideContextMenu()
}

const showContextMenu = (event, item) => {
  event.preventDefault()
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  contextMenu.item = item
}

const hideContextMenu = () => {
  contextMenu.visible = false
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadFileList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadFileList()
}

// 工具方法
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getFileIcon = (fileType) => {
  const iconMap = {
    document: Document,
    image: Picture,
    video: VideoCamera,
    archive: Document,
    other: Document
  }
  return iconMap[fileType] || Document
}

const getFileIconColor = (fileType) => {
  const colorMap = {
    document: '#409eff',
    image: '#67c23a',
    video: '#e6a23c',
    archive: '#f56c6c',
    other: '#909399'
  }
  return colorMap[fileType] || '#909399'
}

const getFileTypeText = (fileType) => {
  const textMap = {
    document: '文档',
    image: '图片',
    video: '视频',
    archive: '压缩包',
    other: '其他'
  }
  return textMap[fileType] || '未知'
}

// 生命周期
onMounted(() => {
  loadFileList()
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})
</script>

<style scoped>
.file-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.breadcrumb-card {
  margin-bottom: 20px;
}

.breadcrumb-link {
  cursor: pointer;
  color: #409eff;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.filter-card {
  margin-bottom: 20px;
}

.view-controls {
  display: flex;
  justify-content: flex-end;
}

.file-content-card {
  margin-bottom: 20px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.file-icon {
  margin-bottom: 8px;
}

.file-name {
  text-align: center;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name-cell .name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item.danger {
  color: #f56c6c;
}

.menu-item.danger:hover {
  background-color: #fef0f0;
}

@media (max-width: 768px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .file-list {
    padding: 12px;
  }
}
</style> 