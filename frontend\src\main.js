import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import router from './router'
import App from './App.vue'
import './styles/global.scss'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册权限指令
app.directive('permission', {
  mounted(el, binding) {
    // 简单的权限检查，暂时允许所有权限
    // 在实际项目中应该根据用户角色和权限进行检查
    const permission = binding.value
    // 暂时显示所有按钮，忽略权限检查
    el.style.display = ''
  },
  updated(el, binding) {
    const permission = binding.value
    // 暂时显示所有按钮，忽略权限检查
    el.style.display = ''
  }
})

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

app.mount('#app') 