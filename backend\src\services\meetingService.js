const { MeetingRoom, Meeting, MeetingParticipant, Employee, User, Department } = require('../models');
const { ValidationError } = require('../shared/errors');
const logger = require('../shared/logger');

class MeetingService {
  // 获取会议室列表
  async getMeetingRooms(options = {}) {
    try {
      return await MeetingRoom.getRoomList(options);
    } catch (error) {
      logger.error('获取会议室列表失败:', error);
      throw error;
    }
  }

  // 获取会议室详情
  async getMeetingRoomById(roomId) {
    try {
      const room = await MeetingRoom.findByPk(roomId);
      if (!room) {
        throw new ValidationError('会议室不存在');
      }
      return room;
    } catch (error) {
      logger.error('获取会议室详情失败:', error);
      throw error;
    }
  }

  // 检查会议室可用性
  async checkRoomAvailability(roomId, startTime, endTime, excludeMeetingId = null) {
    try {
      const room = await this.getMeetingRoomById(roomId);
      if (room.status !== 'active') {
        return {
          available: false,
          reason: '会议室当前不可用'
        };
      }

      const isAvailable = await MeetingRoom.checkAvailability(roomId, startTime, endTime, excludeMeetingId);
      
      if (!isAvailable) {
        const conflictingMeeting = await Meeting.checkConflict(roomId, startTime, endTime, excludeMeetingId);
        return {
          available: false,
          reason: '会议室时间冲突',
          conflictingMeeting: {
            id: conflictingMeeting.id,
            title: conflictingMeeting.title,
            startTime: conflictingMeeting.start_time,
            endTime: conflictingMeeting.end_time
          }
        };
      }

      return {
        available: true,
        room: room
      };
    } catch (error) {
      logger.error('检查会议室可用性失败:', error);
      throw error;
    }
  }

  // 创建会议
  async createMeeting(meetingData, organizerId) {
    try {
      const { 
        title, 
        agenda, 
        startTime, 
        endTime, 
        meetingRoomId, 
        participantIds = [],
        meetingType = 'internal',
        priority = 'medium',
        isRecurring = false,
        recurrenceRule = null,
        meetingLink = null,
        meetingPassword = null,
        notes = null
      } = meetingData;

      // 验证基本信息
      if (!title || !startTime || !endTime || !meetingRoomId) {
        throw new ValidationError('会议主题、开始时间、结束时间和会议室为必填项');
      }

      // 验证时间逻辑
      if (new Date(startTime) >= new Date(endTime)) {
        throw new ValidationError('开始时间必须早于结束时间');
      }

      // 验证组织者权限
      const organizer = await Employee.findByPk(organizerId);
      if (!organizer) {
        throw new ValidationError('组织者不存在');
      }

      // 检查会议室可用性
      const availability = await this.checkRoomAvailability(meetingRoomId, startTime, endTime);
      if (!availability.available) {
        throw new ValidationError(availability.reason, { conflict: availability.conflictingMeeting });
      }

      // 创建会议
      const meeting = await Meeting.create({
        title,
        agenda,
        start_time: startTime,
        end_time: endTime,
        meeting_room_id: meetingRoomId,
        organizer_id: organizerId,
        meeting_type: meetingType,
        priority,
        is_recurring: isRecurring,
        recurrence_rule: recurrenceRule,
        meeting_link: meetingLink,
        meeting_password: meetingPassword,
        notes
      });

      // 邀请参会人
      if (participantIds.length > 0) {
        await MeetingParticipant.inviteParticipants(meeting.id, participantIds, organizerId);
      }

      // 返回完整的会议信息
      return await this.getMeetingById(meeting.id);
    } catch (error) {
      logger.error('创建会议失败:', error);
      throw error;
    }
  }

  // 获取会议详情
  async getMeetingById(meetingId) {
    try {
      const meeting = await Meeting.findByPk(meetingId, {
        include: [
          {
            model: MeetingRoom,
            as: 'meetingRoom'
          },
          {
            model: Employee,
            as: 'organizer',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name', 'email']
            }]
          },
          {
            model: MeetingParticipant,
            as: 'participants',
            include: [{
              model: Employee,
              as: 'employee',
              include: [{
                model: User,
                as: 'user',
                attributes: ['real_name', 'email']
              }, {
                model: Department,
                as: 'department',
                attributes: ['name']
              }]
            }]
          }
        ]
      });

      if (!meeting) {
        throw new ValidationError('会议不存在');
      }

      return meeting;
    } catch (error) {
      logger.error('获取会议详情失败:', error);
      throw error;
    }
  }

  // 获取会议列表
  async getMeetings(options = {}) {
    try {
      return await Meeting.getMeetingList(options);
    } catch (error) {
      logger.error('获取会议列表失败:', error);
      throw error;
    }
  }

  // 获取日历事件
  async getCalendarEvents(startDate, endDate, userId = null) {
    try {
      let employeeId = null;
      if (userId) {
        const employee = await Employee.findOne({ where: { user_id: userId } });
        employeeId = employee?.id;
      }

      return await Meeting.getCalendarEvents(startDate, endDate, employeeId);
    } catch (error) {
      logger.error('获取日历事件失败:', error);
      throw error;
    }
  }

  // 更新会议
  async updateMeeting(meetingId, updateData, userId) {
    try {
      const meeting = await this.getMeetingById(meetingId);
      
      // 检查权限（只有组织者可以修改会议）
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee || meeting.organizer_id !== employee.id) {
        throw new ValidationError('只有会议组织者可以修改会议');
      }

      // 如果修改了时间或会议室，需要重新检查可用性
      if (updateData.startTime || updateData.endTime || updateData.meetingRoomId) {
        const startTime = updateData.startTime || meeting.start_time;
        const endTime = updateData.endTime || meeting.end_time;
        const roomId = updateData.meetingRoomId || meeting.meeting_room_id;

        const availability = await this.checkRoomAvailability(roomId, startTime, endTime, meetingId);
        if (!availability.available) {
          throw new ValidationError(availability.reason, { conflict: availability.conflictingMeeting });
        }
      }

      // 更新会议信息
      const fieldsToUpdate = {};
      const allowedFields = [
        'title', 'agenda', 'startTime', 'endTime', 'meetingRoomId', 
        'meetingType', 'priority', 'meetingLink', 'meetingPassword', 'notes'
      ];

      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          const dbField = field === 'startTime' ? 'start_time' :
                         field === 'endTime' ? 'end_time' :
                         field === 'meetingRoomId' ? 'meeting_room_id' :
                         field === 'meetingType' ? 'meeting_type' :
                         field === 'meetingLink' ? 'meeting_link' :
                         field === 'meetingPassword' ? 'meeting_password' :
                         field;
          fieldsToUpdate[dbField] = updateData[field];
        }
      });

      await meeting.update(fieldsToUpdate);

      // 如果更新了参会人列表
      if (updateData.participantIds) {
        // 删除现有参会人
        await MeetingParticipant.destroy({
          where: { meeting_id: meetingId }
        });
        
        // 重新邀请参会人
        if (updateData.participantIds.length > 0) {
          await MeetingParticipant.inviteParticipants(meetingId, updateData.participantIds, employee.id);
        }
      }

      return await this.getMeetingById(meetingId);
    } catch (error) {
      logger.error('更新会议失败:', error);
      throw error;
    }
  }

  // 取消会议
  async cancelMeeting(meetingId, reason, userId) {
    try {
      const meeting = await this.getMeetingById(meetingId);
      
      // 检查权限
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee || meeting.organizer_id !== employee.id) {
        throw new ValidationError('只有会议组织者可以取消会议');
      }

      if (meeting.status === 'cancelled') {
        throw new ValidationError('会议已经被取消');
      }

      await meeting.cancel(reason);
      return await this.getMeetingById(meetingId);
    } catch (error) {
      logger.error('取消会议失败:', error);
      throw error;
    }
  }

  // 开始会议
  async startMeeting(meetingId, userId) {
    try {
      const meeting = await this.getMeetingById(meetingId);
      
      // 检查权限
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee || meeting.organizer_id !== employee.id) {
        throw new ValidationError('只有会议组织者可以开始会议');
      }

      await meeting.start();
      return await this.getMeetingById(meetingId);
    } catch (error) {
      logger.error('开始会议失败:', error);
      throw error;
    }
  }

  // 结束会议
  async endMeeting(meetingId, userId) {
    try {
      const meeting = await this.getMeetingById(meetingId);
      
      // 检查权限
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee || meeting.organizer_id !== employee.id) {
        throw new ValidationError('只有会议组织者可以结束会议');
      }

      await meeting.end();
      return await this.getMeetingById(meetingId);
    } catch (error) {
      logger.error('结束会议失败:', error);
      throw error;
    }
  }

  // 获取用户的会议邀请
  async getUserInvitations(userId, status = null) {
    try {
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee) {
        throw new ValidationError('员工信息不存在');
      }

      return await MeetingParticipant.getUserInvitations(employee.id, status);
    } catch (error) {
      logger.error('获取用户会议邀请失败:', error);
      throw error;
    }
  }

  // 回应会议邀请
  async respondToInvitation(meetingId, userId, status, reason = null) {
    try {
      const employee = await Employee.findOne({ where: { user_id: userId } });
      if (!employee) {
        throw new ValidationError('员工信息不存在');
      }

      await MeetingParticipant.updateParticipantStatus(meetingId, employee.id, status, reason);
      return await this.getMeetingById(meetingId);
    } catch (error) {
      logger.error('回应会议邀请失败:', error);
      throw error;
    }
  }

  // 获取会议统计
  async getMeetingStats(startDate, endDate, userId = null) {
    try {
      let organizerId = null;
      if (userId) {
        const employee = await Employee.findOne({ where: { user_id: userId } });
        organizerId = employee?.id;
      }

      return await Meeting.getMeetingStats(startDate, endDate, organizerId);
    } catch (error) {
      logger.error('获取会议统计失败:', error);
      throw error;
    }
  }

  // 获取会议室使用统计
  async getRoomUsageStats(roomId, startDate, endDate) {
    try {
      const room = await this.getMeetingRoomById(roomId);
      return await room.getUsageStats(startDate, endDate);
    } catch (error) {
      logger.error('获取会议室使用统计失败:', error);
      throw error;
    }
  }
}

module.exports = new MeetingService(); 