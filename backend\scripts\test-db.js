#!/usr/bin/env node

const path = require('path');
const { sequelize, testConnection, syncDatabase } = require('../src/models');

async function testDatabase() {
  console.log('🧪 开始测试数据库连接和模型...\n');

  try {
    // 测试数据库连接
    console.log('1. 测试数据库连接...');
    const connected = await testConnection();
    if (!connected) {
      throw new Error('数据库连接失败');
    }

    // 测试模型同步（仅开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('2. 测试数据库同步...');
      await syncDatabase({ alter: true });
    }

    // 测试模型关联
    console.log('3. 验证模型关联...');
    const models = sequelize.models;
    const modelNames = Object.keys(models);
    console.log('   📋 已加载的模型:', modelNames.join(', '));

    // 验证关键关联
    const associations = {
      'User-Role': models.User.associations.roles,
      'Department-Children': models.Department.associations.children,
      'Employee-User': models.Employee.associations.user,
      'Meeting-Participants': models.Meeting?.associations?.participants
    };

    for (const [name, association] of Object.entries(associations)) {
      if (association) {
        console.log(`   ✅ ${name} 关联已建立`);
      } else {
        console.log(`   ⚠️  ${name} 关联未找到`);
      }
    }

    // 测试基本查询
    console.log('4. 测试基本查询...');
    
    // 测试角色表
    if (models.Role) {
      const roleCount = await models.Role.count();
      console.log(`   📊 角色表记录数: ${roleCount}`);
    }

    // 测试部门表
    if (models.Department) {
      const deptCount = await models.Department.count();
      console.log(`   📊 部门表记录数: ${deptCount}`);
    }

    // 测试权限检查方法
    if (models.Role) {
      const superAdminRole = await models.Role.findOne({ 
        where: { code: 'SUPER_ADMIN' } 
      });
      if (superAdminRole) {
        const hasPermission = superAdminRole.hasPermission('system:read');
        console.log(`   🔐 超级管理员权限检查: ${hasPermission ? '通过' : '失败'}`);
      }
    }

    console.log('\n✅ 数据库测试完成！\n');
    
    console.log('📊 测试总结:');
    console.log(`   ├── 数据库连接: ✅ 正常`);
    console.log(`   ├── 模型加载: ✅ ${modelNames.length} 个模型`);
    console.log(`   ├── 关联验证: ✅ 基础关联正常`);
    console.log(`   └── 基本查询: ✅ 正常`);

  } catch (error) {
    console.error('\n❌ 数据库测试失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('   1. 检查数据库服务是否启动');
    console.log('   2. 运行数据库迁移: npm run db:migrate');
    console.log('   3. 导入种子数据: npm run db:seed');
    console.log('   4. 检查模型文件是否完整');
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testDatabase().catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testDatabase }; 