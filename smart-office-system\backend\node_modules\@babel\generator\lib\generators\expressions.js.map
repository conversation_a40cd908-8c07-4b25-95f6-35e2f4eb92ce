{"version": 3, "names": ["_t", "require", "_index", "isCallExpression", "isLiteral", "isMemberExpression", "isNewExpression", "isPattern", "UnaryExpression", "node", "operator", "word", "space", "token", "print", "argument", "DoExpression", "async", "body", "ParenthesizedExpression", "exit", "enterDelimited", "expression", "rightParens", "UpdateExpression", "prefix", "ConditionalExpression", "test", "consequent", "alternate", "NewExpression", "parent", "callee", "format", "minified", "arguments", "length", "optional", "typeArguments", "typeParameters", "tokenMap", "endMatches", "printList", "shouldPrintTrailingComma", "SequenceExpression", "expressions", "ThisExpression", "Super", "_shouldPrintDecoratorsBeforeExport", "decoratorsBeforeExport", "start", "declaration", "Decorator", "newline", "OptionalMemberExpression", "computed", "property", "object", "TypeError", "value", "OptionalCallExpression", "CallExpression", "Import", "AwaitExpression", "YieldExpression", "delegate", "EmptyStatement", "semicolon", "ExpressionStatement", "tokenContext", "TokenContext", "expressionStatement", "AssignmentPattern", "left", "type", "typeAnnotation", "right", "AssignmentExpression", "_endsWithDiv", "BindExpression", "MemberExpression", "MetaProperty", "meta", "PrivateName", "id", "V8IntrinsicIdentifier", "name", "ModuleExpression", "indent", "directives", "dedent", "rightBrace"], "sources": ["../../src/generators/expressions.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isCallExpression,\n  isLiteral,\n  isMemberExpression,\n  isNewExpression,\n  isPattern,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { TokenContext } from \"../node/index.ts\";\n\nexport function UnaryExpression(this: Printer, node: t.UnaryExpression) {\n  const { operator } = node;\n  if (\n    operator === \"void\" ||\n    operator === \"delete\" ||\n    operator === \"typeof\" ||\n    // throwExpressions\n    operator === \"throw\"\n  ) {\n    this.word(operator);\n    this.space();\n  } else {\n    this.token(operator);\n  }\n\n  this.print(node.argument);\n}\n\nexport function DoExpression(this: Printer, node: t.DoExpression) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n  this.word(\"do\");\n  this.space();\n  this.print(node.body);\n}\n\nexport function ParenthesizedExpression(\n  this: Printer,\n  node: t.ParenthesizedExpression,\n) {\n  this.token(\"(\");\n  const exit = this.enterDelimited();\n  this.print(node.expression);\n  exit();\n  this.rightParens(node);\n}\n\nexport function UpdateExpression(this: Printer, node: t.UpdateExpression) {\n  if (node.prefix) {\n    this.token(node.operator);\n    this.print(node.argument);\n  } else {\n    this.print(node.argument, true);\n    this.token(node.operator);\n  }\n}\n\nexport function ConditionalExpression(\n  this: Printer,\n  node: t.ConditionalExpression,\n) {\n  this.print(node.test);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.consequent);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.alternate);\n}\n\nexport function NewExpression(\n  this: Printer,\n  node: t.NewExpression,\n  parent: t.Node,\n) {\n  this.word(\"new\");\n  this.space();\n  this.print(node.callee);\n  if (\n    this.format.minified &&\n    node.arguments.length === 0 &&\n    // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n    !node.optional &&\n    !isCallExpression(parent, { callee: node }) &&\n    !isMemberExpression(parent) &&\n    !isNewExpression(parent)\n  ) {\n    return;\n  }\n\n  this.print(node.typeArguments);\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n    this.print(node.typeParameters); // Legacy TS AST\n  }\n\n  // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n  if (node.optional) {\n    // TODO: This can never happen\n    this.token(\"?.\");\n  }\n\n  if (\n    node.arguments.length === 0 &&\n    this.tokenMap &&\n    !this.tokenMap.endMatches(node, \")\")\n  ) {\n    return;\n  }\n\n  this.token(\"(\");\n  const exit = this.enterDelimited();\n  this.printList(node.arguments, this.shouldPrintTrailingComma(\")\"));\n  exit();\n  this.rightParens(node);\n}\n\nexport function SequenceExpression(this: Printer, node: t.SequenceExpression) {\n  this.printList(node.expressions);\n}\n\nexport function ThisExpression(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function Super(this: Printer) {\n  this.word(\"super\");\n}\n\nexport function _shouldPrintDecoratorsBeforeExport(\n  this: Printer,\n  node: t.ExportDeclaration & { declaration: t.ClassDeclaration },\n) {\n  if (typeof this.format.decoratorsBeforeExport === \"boolean\") {\n    return this.format.decoratorsBeforeExport;\n  }\n  return (\n    typeof node.start === \"number\" && node.start === node.declaration.start\n  );\n}\n\nexport function Decorator(this: Printer, node: t.Decorator) {\n  this.token(\"@\");\n  this.print(node.expression);\n  this.newline();\n}\n\nexport function OptionalMemberExpression(\n  this: Printer,\n  node: t.OptionalMemberExpression,\n) {\n  let { computed } = node;\n  const { optional, property } = node;\n\n  this.print(node.object);\n\n  if (!computed && isMemberExpression(property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  // @ts-expect-error todo(flow->ts) maybe instead of typeof check specific literal types?\n  if (isLiteral(property) && typeof property.value === \"number\") {\n    computed = true;\n  }\n  if (optional) {\n    this.token(\"?.\");\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(property);\n    this.token(\"]\");\n  } else {\n    if (!optional) {\n      this.token(\".\");\n    }\n    this.print(property);\n  }\n}\n\nexport function OptionalCallExpression(\n  this: Printer,\n  node: t.OptionalCallExpression,\n) {\n  this.print(node.callee);\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n    this.print(node.typeParameters); // legacy TS AST\n  }\n\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  this.print(node.typeArguments);\n\n  this.token(\"(\");\n  const exit = this.enterDelimited();\n  this.printList(node.arguments);\n  exit();\n  this.rightParens(node);\n}\n\nexport function CallExpression(this: Printer, node: t.CallExpression) {\n  this.print(node.callee);\n\n  this.print(node.typeArguments);\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n    this.print(node.typeParameters); // legacy TS AST\n  }\n  this.token(\"(\");\n  const exit = this.enterDelimited();\n  this.printList(node.arguments, this.shouldPrintTrailingComma(\")\"));\n  exit();\n  this.rightParens(node);\n}\n\nexport function Import(this: Printer) {\n  this.word(\"import\");\n}\n\nexport function AwaitExpression(this: Printer, node: t.AwaitExpression) {\n  this.word(\"await\");\n  this.space();\n  this.print(node.argument);\n}\n\nexport function YieldExpression(this: Printer, node: t.YieldExpression) {\n  if (node.delegate) {\n    this.word(\"yield\", true);\n    this.token(\"*\");\n    if (node.argument) {\n      this.space();\n      // line terminators are allowed after yield*\n      this.print(node.argument);\n    }\n  } else if (node.argument) {\n    this.word(\"yield\", true);\n    this.space();\n    this.print(node.argument);\n  } else {\n    this.word(\"yield\");\n  }\n}\n\nexport function EmptyStatement(this: Printer) {\n  this.semicolon(true /* force */);\n}\n\nexport function ExpressionStatement(\n  this: Printer,\n  node: t.ExpressionStatement,\n) {\n  this.tokenContext |= TokenContext.expressionStatement;\n  this.print(node.expression);\n  this.semicolon();\n}\n\nexport function AssignmentPattern(this: Printer, node: t.AssignmentPattern) {\n  this.print(node.left);\n  if (node.left.type === \"Identifier\" || isPattern(node.left)) {\n    if (node.left.optional) this.token(\"?\");\n    this.print(node.left.typeAnnotation);\n  }\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right);\n}\n\nexport function AssignmentExpression(\n  this: Printer,\n  node: t.AssignmentExpression | t.BinaryExpression | t.LogicalExpression,\n) {\n  this.print(node.left);\n\n  this.space();\n  if (node.operator === \"in\" || node.operator === \"instanceof\") {\n    this.word(node.operator);\n  } else {\n    this.token(node.operator);\n    this._endsWithDiv = node.operator === \"/\";\n  }\n  this.space();\n\n  this.print(node.right);\n}\n\nexport function BindExpression(this: Printer, node: t.BindExpression) {\n  this.print(node.object);\n  this.token(\"::\");\n  this.print(node.callee);\n}\n\nexport {\n  AssignmentExpression as BinaryExpression,\n  AssignmentExpression as LogicalExpression,\n};\n\nexport function MemberExpression(this: Printer, node: t.MemberExpression) {\n  this.print(node.object);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe use specific literal types\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n\n  if (computed) {\n    const exit = this.enterDelimited();\n    this.token(\"[\");\n    this.print(node.property);\n    this.token(\"]\");\n    exit();\n  } else {\n    this.token(\".\");\n    this.print(node.property);\n  }\n}\n\nexport function MetaProperty(this: Printer, node: t.MetaProperty) {\n  this.print(node.meta);\n  this.token(\".\");\n  this.print(node.property);\n}\n\nexport function PrivateName(this: Printer, node: t.PrivateName) {\n  this.token(\"#\");\n  this.print(node.id);\n}\n\nexport function V8IntrinsicIdentifier(\n  this: Printer,\n  node: t.V8IntrinsicIdentifier,\n) {\n  this.token(\"%\");\n  this.word(node.name);\n}\n\nexport function ModuleExpression(this: Printer, node: t.ModuleExpression) {\n  this.word(\"module\", true);\n  this.space();\n  this.token(\"{\");\n  this.indent();\n  const { body } = node;\n  if (body.body.length || body.directives.length) {\n    this.newline();\n  }\n  this.print(body);\n  this.dedent();\n  this.rightBrace(node);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EAP9CE,gBAAgB;EAChBC,SAAS;EACTC,kBAAkB;EAClBC,eAAe;EACfC;AAAS,IAAAP,EAAA;AAKJ,SAASQ,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,MAAM;IAAEC;EAAS,CAAC,GAAGD,IAAI;EACzB,IACEC,QAAQ,KAAK,MAAM,IACnBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IAErBA,QAAQ,KAAK,OAAO,EACpB;IACA,IAAI,CAACC,IAAI,CAACD,QAAQ,CAAC;IACnB,IAAI,CAACE,KAAK,CAAC,CAAC;EACd,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;EACtB;EAEA,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,CAAC;AAC3B;AAEO,SAASC,YAAYA,CAAgBP,IAAoB,EAAE;EAChE,IAAIA,IAAI,CAACQ,KAAK,EAAE;IACd,IAAI,CAACN,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACS,IAAI,CAAC;AACvB;AAEO,SAASC,uBAAuBA,CAErCV,IAA+B,EAC/B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,MAAMO,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAClC,IAAI,CAACP,KAAK,CAACL,IAAI,CAACa,UAAU,CAAC;EAC3BF,IAAI,CAAC,CAAC;EACN,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;AACxB;AAEO,SAASe,gBAAgBA,CAAgBf,IAAwB,EAAE;EACxE,IAAIA,IAAI,CAACgB,MAAM,EAAE;IACf,IAAI,CAACZ,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,CAAC;EAC3B,CAAC,MAAM;IACL,IAAI,CAACD,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAE,IAAI,CAAC;IAC/B,IAAI,CAACF,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASgB,qBAAqBA,CAEnCjB,IAA6B,EAC7B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACkB,IAAI,CAAC;EACrB,IAAI,CAACf,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACmB,UAAU,CAAC;EAC3B,IAAI,CAAChB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACoB,SAAS,CAAC;AAC5B;AAEO,SAASC,aAAaA,CAE3BrB,IAAqB,EACrBsB,MAAc,EACd;EACA,IAAI,CAACpB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACuB,MAAM,CAAC;EACvB,IACE,IAAI,CAACC,MAAM,CAACC,QAAQ,IACpBzB,IAAI,CAAC0B,SAAS,CAACC,MAAM,KAAK,CAAC,IAE3B,CAAC3B,IAAI,CAAC4B,QAAQ,IACd,CAAClC,gBAAgB,CAAC4B,MAAM,EAAE;IAAEC,MAAM,EAAEvB;EAAK,CAAC,CAAC,IAC3C,CAACJ,kBAAkB,CAAC0B,MAAM,CAAC,IAC3B,CAACzB,eAAe,CAACyB,MAAM,CAAC,EACxB;IACA;EACF;EAEA,IAAI,CAACjB,KAAK,CAACL,IAAI,CAAC6B,aAAa,CAAC;EACK;IAEjC,IAAI,CAACxB,KAAK,CAACL,IAAI,CAAC8B,cAAc,CAAC;EACjC;EAGA,IAAI9B,IAAI,CAAC4B,QAAQ,EAAE;IAEjB,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IACEJ,IAAI,CAAC0B,SAAS,CAACC,MAAM,KAAK,CAAC,IAC3B,IAAI,CAACI,QAAQ,IACb,CAAC,IAAI,CAACA,QAAQ,CAACC,UAAU,CAAChC,IAAI,EAAE,GAAG,CAAC,EACpC;IACA;EACF;EAEA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,MAAMO,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAClC,IAAI,CAACqB,SAAS,CAACjC,IAAI,CAAC0B,SAAS,EAAE,IAAI,CAACQ,wBAAwB,CAAC,GAAG,CAAC,CAAC;EAClEvB,IAAI,CAAC,CAAC;EACN,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;AACxB;AAEO,SAASmC,kBAAkBA,CAAgBnC,IAA0B,EAAE;EAC5E,IAAI,CAACiC,SAAS,CAACjC,IAAI,CAACoC,WAAW,CAAC;AAClC;AAEO,SAASC,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACnC,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASoC,KAAKA,CAAA,EAAgB;EACnC,IAAI,CAACpC,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAASqC,kCAAkCA,CAEhDvC,IAA+D,EAC/D;EACA,IAAI,OAAO,IAAI,CAACwB,MAAM,CAACgB,sBAAsB,KAAK,SAAS,EAAE;IAC3D,OAAO,IAAI,CAAChB,MAAM,CAACgB,sBAAsB;EAC3C;EACA,OACE,OAAOxC,IAAI,CAACyC,KAAK,KAAK,QAAQ,IAAIzC,IAAI,CAACyC,KAAK,KAAKzC,IAAI,CAAC0C,WAAW,CAACD,KAAK;AAE3E;AAEO,SAASE,SAASA,CAAgB3C,IAAiB,EAAE;EAC1D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACa,UAAU,CAAC;EAC3B,IAAI,CAAC+B,OAAO,CAAC,CAAC;AAChB;AAEO,SAASC,wBAAwBA,CAEtC7C,IAAgC,EAChC;EACA,IAAI;IAAE8C;EAAS,CAAC,GAAG9C,IAAI;EACvB,MAAM;IAAE4B,QAAQ;IAAEmB;EAAS,CAAC,GAAG/C,IAAI;EAEnC,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgD,MAAM,CAAC;EAEvB,IAAI,CAACF,QAAQ,IAAIlD,kBAAkB,CAACmD,QAAQ,CAAC,EAAE;IAC7C,MAAM,IAAIE,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAGA,IAAItD,SAAS,CAACoD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,CAACG,KAAK,KAAK,QAAQ,EAAE;IAC7DJ,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIlB,QAAQ,EAAE;IACZ,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI0C,QAAQ,EAAE;IACZ,IAAI,CAAC1C,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC0C,QAAQ,CAAC;IACpB,IAAI,CAAC3C,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACwB,QAAQ,EAAE;MACb,IAAI,CAACxB,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACC,KAAK,CAAC0C,QAAQ,CAAC;EACtB;AACF;AAEO,SAASI,sBAAsBA,CAEpCnD,IAA8B,EAC9B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuB,MAAM,CAAC;EAEY;IAEjC,IAAI,CAAClB,KAAK,CAACL,IAAI,CAAC8B,cAAc,CAAC;EACjC;EAEA,IAAI9B,IAAI,CAAC4B,QAAQ,EAAE;IACjB,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC6B,aAAa,CAAC;EAE9B,IAAI,CAACzB,SAAK,GAAI,CAAC;EACf,MAAMO,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAClC,IAAI,CAACqB,SAAS,CAACjC,IAAI,CAAC0B,SAAS,CAAC;EAC9Bf,IAAI,CAAC,CAAC;EACN,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;AACxB;AAEO,SAASoD,cAAcA,CAAgBpD,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuB,MAAM,CAAC;EAEvB,IAAI,CAAClB,KAAK,CAACL,IAAI,CAAC6B,aAAa,CAAC;EACK;IAEjC,IAAI,CAACxB,KAAK,CAACL,IAAI,CAAC8B,cAAc,CAAC;EACjC;EACA,IAAI,CAAC1B,SAAK,GAAI,CAAC;EACf,MAAMO,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAClC,IAAI,CAACqB,SAAS,CAACjC,IAAI,CAAC0B,SAAS,EAAE,IAAI,CAACQ,wBAAwB,CAAC,GAAG,CAAC,CAAC;EAClEvB,IAAI,CAAC,CAAC;EACN,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;AACxB;AAEO,SAASqD,MAAMA,CAAA,EAAgB;EACpC,IAAI,CAACnD,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASoD,eAAeA,CAAgBtD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,CAAC;AAC3B;AAEO,SAASiD,eAAeA,CAAgBvD,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACwD,QAAQ,EAAE;IACjB,IAAI,CAACtD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACE,SAAK,GAAI,CAAC;IACf,IAAIJ,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,CAAC,CAAC;MAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,CAAC;IAC3B;EACF,CAAC,MAAM,IAAIN,IAAI,CAACM,QAAQ,EAAE;IACxB,IAAI,CAACJ,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,CAAC;EAC3B,CAAC,MAAM;IACL,IAAI,CAACJ,IAAI,CAAC,OAAO,CAAC;EACpB;AACF;AAEO,SAASuD,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACC,SAAS,CAAC,IAAgB,CAAC;AAClC;AAEO,SAASC,mBAAmBA,CAEjC3D,IAA2B,EAC3B;EACA,IAAI,CAAC4D,YAAY,IAAIC,mBAAY,CAACC,mBAAmB;EACrD,IAAI,CAACzD,KAAK,CAACL,IAAI,CAACa,UAAU,CAAC;EAC3B,IAAI,CAAC6C,SAAS,CAAC,CAAC;AAClB;AAEO,SAASK,iBAAiBA,CAAgB/D,IAAyB,EAAE;EAC1E,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgE,IAAI,CAAC;EACrB,IAAIhE,IAAI,CAACgE,IAAI,CAACC,IAAI,KAAK,YAAY,IAAInE,SAAS,CAACE,IAAI,CAACgE,IAAI,CAAC,EAAE;IAC3D,IAAIhE,IAAI,CAACgE,IAAI,CAACpC,QAAQ,EAAE,IAAI,CAACxB,SAAK,GAAI,CAAC;IACvC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACgE,IAAI,CAACE,cAAc,CAAC;EACtC;EACA,IAAI,CAAC/D,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACmE,KAAK,CAAC;AACxB;AAEO,SAASC,oBAAoBA,CAElCpE,IAAuE,EACvE;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgE,IAAI,CAAC;EAErB,IAAI,CAAC7D,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACC,QAAQ,KAAK,IAAI,IAAID,IAAI,CAACC,QAAQ,KAAK,YAAY,EAAE;IAC5D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACoE,YAAY,GAAGrE,IAAI,CAACC,QAAQ,KAAK,GAAG;EAC3C;EACA,IAAI,CAACE,KAAK,CAAC,CAAC;EAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACmE,KAAK,CAAC;AACxB;AAEO,SAASG,cAAcA,CAAgBtE,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgD,MAAM,CAAC;EACvB,IAAI,CAAC5C,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,KAAK,CAACL,IAAI,CAACuB,MAAM,CAAC;AACzB;AAOO,SAASgD,gBAAgBA,CAAgBvE,IAAwB,EAAE;EACxE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgD,MAAM,CAAC;EAEvB,IAAI,CAAChD,IAAI,CAAC8C,QAAQ,IAAIlD,kBAAkB,CAACI,IAAI,CAAC+C,QAAQ,CAAC,EAAE;IACvD,MAAM,IAAIE,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIH,QAAQ,GAAG9C,IAAI,CAAC8C,QAAQ;EAE5B,IAAInD,SAAS,CAACK,IAAI,CAAC+C,QAAQ,CAAC,IAAI,OAAO/C,IAAI,CAAC+C,QAAQ,CAACG,KAAK,KAAK,QAAQ,EAAE;IACvEJ,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIA,QAAQ,EAAE;IACZ,MAAMnC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAClC,IAAI,CAACR,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC+C,QAAQ,CAAC;IACzB,IAAI,CAAC3C,SAAK,GAAI,CAAC;IACfO,IAAI,CAAC,CAAC;EACR,CAAC,MAAM;IACL,IAAI,CAACP,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC+C,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASyB,YAAYA,CAAgBxE,IAAoB,EAAE;EAChE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACyE,IAAI,CAAC;EACrB,IAAI,CAACrE,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC+C,QAAQ,CAAC;AAC3B;AAEO,SAAS2B,WAAWA,CAAgB1E,IAAmB,EAAE;EAC9D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC2E,EAAE,CAAC;AACrB;AAEO,SAASC,qBAAqBA,CAEnC5E,IAA6B,EAC7B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,IAAI,CAACF,IAAI,CAAC6E,IAAI,CAAC;AACtB;AAEO,SAASC,gBAAgBA,CAAgB9E,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,IAAI,CAAC;EACf,IAAI,CAAC2E,MAAM,CAAC,CAAC;EACb,MAAM;IAAEtE;EAAK,CAAC,GAAGT,IAAI;EACrB,IAAIS,IAAI,CAACA,IAAI,CAACkB,MAAM,IAAIlB,IAAI,CAACuE,UAAU,CAACrD,MAAM,EAAE;IAC9C,IAAI,CAACiB,OAAO,CAAC,CAAC;EAChB;EACA,IAAI,CAACvC,KAAK,CAACI,IAAI,CAAC;EAChB,IAAI,CAACwE,MAAM,CAAC,CAAC;EACb,IAAI,CAACC,UAAU,CAAClF,IAAI,CAAC;AACvB", "ignoreList": []}