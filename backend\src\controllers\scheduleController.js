const { Schedule, Reminder, User } = require('../models');
const { success, error } = require('../shared/response');
const { Op } = require('sequelize');

/**
 * 获取日程列表
 */
const getSchedules = async (req, res) => {
  try {
    const { page = 1, size = 20, type, priority, search } = req.query;
    const userId = req.user.id;
    const offset = (parseInt(page) - 1) * parseInt(size);

    const where = {
      user_id: userId // 只获取当前用户的日程
    };

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 类型筛选
    if (type) {
      where.type = type;
    }

    // 优先级筛选
    if (priority) {
      where.priority = priority;
    }

    const { count, rows } = await Schedule.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name', 'email']
        },
        {
          model: Reminder,
          as: 'reminders',
          required: false
        }
      ],
      order: [['start_time', 'ASC']],
      limit: parseInt(size),
      offset
    });

    return success(res, {
      schedules: rows,
      pagination: {
        page: parseInt(page),
        size: parseInt(size),
        total: count,
        totalPages: Math.ceil(count / parseInt(size))
      }
    }, '获取日程列表成功');
  } catch (error) {
    console.error('获取日程列表失败:', error);
    return error(res, '获取日程列表失败', 500);
  }
};

/**
 * 根据ID获取单个日程
 */
const getScheduleById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const schedule = await Schedule.findOne({
      where: { id, user_id: userId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name', 'email']
        },
        {
          model: Reminder,
          as: 'reminders'
        }
      ]
    });

    if (!schedule) {
      return error(res, '日程不存在', 404);
    }

    return success(res, schedule, '获取日程详情成功');
  } catch (error) {
    console.error('获取日程详情失败:', error);
    return error(res, '获取日程详情失败', 500);
  }
};

/**
 * 创建新日程
 */
const createSchedule = async (req, res) => {
  try {
    const {
      title,
      type = 'personal',
      start_time,
      end_time,
      priority = 'medium',
      reminder_minutes = 15,
      is_all_day = false,
      description,
      attendees
    } = req.body;

    const userId = req.user.id;

    // 验证必填字段
    if (!title || !start_time || !end_time) {
      return error(res, '标题、开始时间和结束时间不能为空', 400);
    }

    // 验证时间逻辑
    const startDate = new Date(start_time);
    const endDate = new Date(end_time);
    
    if (startDate >= endDate) {
      return error(res, '结束时间必须晚于开始时间', 400);
    }

    // 创建日程
    const schedule = await Schedule.create({
      title,
      type,
      start_time: startDate,
      end_time: endDate,
      priority,
      reminder_minutes,
      is_all_day,
      description,
      attendees: typeof attendees === 'string' ? attendees : JSON.stringify(attendees || []),
      user_id: userId
    });

    // 获取完整信息
    const fullSchedule = await Schedule.findByPk(schedule.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name', 'email']
        }
      ]
    });

    return success(res, fullSchedule, '创建日程成功', 201);
  } catch (error) {
    console.error('创建日程失败:', error);
    return error(res, '创建日程失败: ' + error.message, 500);
  }
};

/**
 * 更新日程
 */
const updateSchedule = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    // 查找日程
    const schedule = await Schedule.findOne({
      where: { id, user_id: userId }
    });

    if (!schedule) {
      return error(res, '日程不存在或无权限修改', 404);
    }

    // 验证时间逻辑（如果有时间更新）
    if (updateData.start_time || updateData.end_time) {
      const startTime = new Date(updateData.start_time || schedule.start_time);
      const endTime = new Date(updateData.end_time || schedule.end_time);
      
      if (startTime >= endTime) {
        return error(res, '结束时间必须晚于开始时间', 400);
      }
    }

    // 处理attendees数据
    if (updateData.attendees && typeof updateData.attendees !== 'string') {
      updateData.attendees = JSON.stringify(updateData.attendees);
    }

    // 更新日程
    await schedule.update(updateData);

    // 获取更新后的完整信息
    const updatedSchedule = await Schedule.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name', 'email']
        }
      ]
    });

    return success(res, updatedSchedule, '更新日程成功');
  } catch (error) {
    console.error('更新日程失败:', error);
    return error(res, '更新日程失败: ' + error.message, 500);
  }
};

/**
 * 删除日程
 */
const deleteSchedule = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const schedule = await Schedule.findOne({
      where: { id, user_id: userId }
    });

    if (!schedule) {
      return error(res, '日程不存在或无权限删除', 404);
    }

    await schedule.destroy();

    return success(res, null, '删除日程成功');
  } catch (error) {
    console.error('删除日程失败:', error);
    return error(res, '删除日程失败', 500);
  }
};

/**
 * 获取今日日程
 */
const getTodaySchedules = async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const schedules = await Schedule.findAll({
      where: {
        user_id: userId,
        start_time: {
          [Op.gte]: startOfDay,
          [Op.lt]: endOfDay
        }
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'real_name']
        }
      ],
      order: [['start_time', 'ASC']]
    });

    return success(res, { schedules }, '获取今日日程成功');
  } catch (error) {
    console.error('获取今日日程失败:', error);
    return error(res, '获取今日日程失败', 500);
  }
};

module.exports = {
  getSchedules,
  getScheduleById,
  createSchedule,
  updateSchedule,
  deleteSchedule,
  getTodaySchedules
}; 