const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class ApprovalTask extends Model {
    static associate(models) {
      // 关联到工作流实例
      ApprovalTask.belongsTo(models.WorkflowInstance, {
        foreignKey: 'workflow_instance_id',
        as: 'instance'
      });

      // 关联到工作流节点
      ApprovalTask.belongsTo(models.WorkflowNode, {
        foreignKey: 'workflow_node_id',
        as: 'node'
      });

      // 关联到审批人
      ApprovalTask.belongsTo(models.Employee, {
        foreignKey: 'assignee_id',
        as: 'assignee'
      });

      // 关联到实际处理人（委托情况下）
      ApprovalTask.belongsTo(models.Employee, {
        foreignKey: 'processed_by',
        as: 'processor'
      });
    }

    // 静态方法：获取用户的待审批任务列表
    static async getPendingTasks(assigneeId, options = {}) {
      const {
        page = 1,
        pageSize = 20,
        category,
        priority,
        keyword,
        overdue
      } = options;

      const where = {
        assignee_id: assigneeId,
        status: 'pending'
      };

      if (overdue) {
        where.deadline = {
          [sequelize.Sequelize.Op.lt]: new Date()
        };
      }

      let include = [
        {
          model: sequelize.models.WorkflowInstance,
          as: 'instance',
          include: [
            {
              model: sequelize.models.WorkflowDefinition,
              as: 'definition',
              attributes: ['id', 'name', 'category']
            },
            {
              model: sequelize.models.Employee,
              as: 'initiator',
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name']
              }]
            }
          ]
        },
        {
          model: sequelize.models.WorkflowNode,
          as: 'node',
          attributes: ['id', 'name', 'type']
        }
      ];

      // 按分类筛选
      if (category) {
        include[0].include[0].where = { category };
      }

      // 按优先级筛选
      if (priority) {
        include[0].where = { priority };
      }

      // 关键字搜索
      if (keyword) {
        include[0].where = {
          ...include[0].where,
          title: { [sequelize.Sequelize.Op.like]: `%${keyword}%` }
        };
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include,
        limit: pageSize,
        offset,
        order: [
          ['deadline', 'ASC NULLS LAST'],
          ['created_at', 'DESC']
        ]
      });

      return {
        tasks: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：获取用户的已处理任务列表
    static async getProcessedTasks(assigneeId, options = {}) {
      const {
        page = 1,
        pageSize = 20,
        status,
        startDate,
        endDate
      } = options;

      const where = {
        assignee_id: assigneeId,
        status: { [sequelize.Sequelize.Op.in]: ['approved', 'rejected', 'delegated'] }
      };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (startDate && endDate) {
        where.processed_at = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.WorkflowInstance,
            as: 'instance',
            include: [
              {
                model: sequelize.models.WorkflowDefinition,
                as: 'definition',
                attributes: ['id', 'name', 'category']
              },
              {
                model: sequelize.models.Employee,
                as: 'initiator',
                include: [{
                  model: sequelize.models.User,
                  as: 'user',
                  attributes: ['real_name']
                }]
              }
            ]
          },
          {
            model: sequelize.models.WorkflowNode,
            as: 'node',
            attributes: ['id', 'name', 'type']
          },
          {
            model: sequelize.models.Employee,
            as: 'processor',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [['processed_at', 'DESC']]
      });

      return {
        tasks: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 实例方法：审批通过
    async approve(processedBy, comment = '', attachments = []) {
      if (this.status !== 'pending') {
        throw new Error('任务状态不允许审批');
      }

      this.status = 'approved';
      this.processed_by = processedBy;
      this.processed_at = new Date();
      this.comment = comment;
      this.attachments = attachments;

      await this.save();

      // 检查当前节点的所有审批任务是否都已完成
      const instance = await sequelize.models.WorkflowInstance.findByPk(this.workflow_instance_id);
      const node = await sequelize.models.WorkflowNode.findByPk(this.workflow_node_id);
      
      if (await this._checkNodeCompletion(node)) {
        // 移动到下一个节点
        await instance.moveToNext({ approved: true, comment });
      }

      return this;
    }

    // 实例方法：审批拒绝
    async reject(processedBy, comment = '', attachments = []) {
      if (this.status !== 'pending') {
        throw new Error('任务状态不允许审批');
      }

      this.status = 'rejected';
      this.processed_by = processedBy;
      this.processed_at = new Date();
      this.comment = comment;
      this.attachments = attachments;

      await this.save();

      // 拒绝整个工作流实例
      const instance = await sequelize.models.WorkflowInstance.findByPk(this.workflow_instance_id);
      await instance.reject(comment, processedBy);

      return this;
    }

    // 实例方法：委托给其他人
    async delegate(processedBy, delegateToId, comment = '') {
      if (this.status !== 'pending') {
        throw new Error('任务状态不允许委托');
      }

      // 检查节点是否允许委托
      const node = await sequelize.models.WorkflowNode.findByPk(this.workflow_node_id);
      if (node.config && node.config.allow_delegate === false) {
        throw new Error('当前节点不允许委托');
      }

      // 创建新的审批任务
      const newTask = await ApprovalTask.create({
        workflow_instance_id: this.workflow_instance_id,
        workflow_node_id: this.workflow_node_id,
        assignee_id: delegateToId,
        status: 'pending',
        deadline: this.deadline,
        delegated_from: this.id,
        task_data: this.task_data
      });

      // 更新当前任务状态
      this.status = 'delegated';
      this.processed_by = processedBy;
      this.processed_at = new Date();
      this.comment = comment;
      this.delegated_to = delegateToId;

      await this.save();

      return newTask;
    }

    // 实例方法：取消任务
    async cancel() {
      if (this.status === 'cancelled') {
        return this;
      }

      this.status = 'cancelled';
      this.processed_at = new Date();
      await this.save();

      return this;
    }

    // 实例方法：检查是否超时
    isOverdue() {
      if (!this.deadline) {
        return false;
      }
      return new Date() > this.deadline;
    }

    // 实例方法：获取剩余时间（小时）
    getRemainingHours() {
      if (!this.deadline) {
        return null;
      }
      const remaining = this.deadline.getTime() - Date.now();
      return Math.max(0, Math.round(remaining / (1000 * 60 * 60)));
    }

    // 实例方法：检查节点是否已完成
    async _checkNodeCompletion(node) {
      const allTasks = await ApprovalTask.findAll({
        where: {
          workflow_instance_id: this.workflow_instance_id,
          workflow_node_id: this.workflow_node_id,
          status: { [sequelize.Sequelize.Op.in]: ['pending', 'approved'] }
        }
      });

      const approvedTasks = allTasks.filter(task => task.status === 'approved');
      const pendingTasks = allTasks.filter(task => task.status === 'pending');

      // 根据节点配置判断是否完成
      const approvalType = node.config?.approval_type || 'any';
      
      if (approvalType === 'any') {
        // 任意一人审批即可
        return approvedTasks.length > 0;
      } else if (approvalType === 'all') {
        // 所有人都必须审批
        return pendingTasks.length === 0 && approvedTasks.length > 0;
      }

      return false;
    }

    // 静态方法：获取审批统计
    static async getApprovalStatistics(assigneeId, options = {}) {
      const { startDate, endDate } = options;
      
      const where = { assignee_id: assigneeId };
      if (startDate && endDate) {
        where.processed_at = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }

      const stats = await this.findAll({
        where,
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const result = {
        pending: 0,
        approved: 0,
        rejected: 0,
        delegated: 0,
        cancelled: 0,
        total: 0
      };

      stats.forEach(stat => {
        result[stat.status] = parseInt(stat.count);
        result.total += parseInt(stat.count);
      });

      // 获取超时任务数量
      const overdueCount = await this.count({
        where: {
          assignee_id: assigneeId,
          status: 'pending',
          deadline: {
            [sequelize.Sequelize.Op.lt]: new Date()
          }
        }
      });

      result.overdue = overdueCount;

      return result;
    }

    // 静态方法：获取工作量统计
    static async getWorkloadStatistics(assigneeId, days = 30) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const dailyStats = await this.findAll({
        where: {
          assignee_id: assigneeId,
          processed_at: {
            [sequelize.Sequelize.Op.gte]: startDate
          }
        },
        attributes: [
          [sequelize.fn('DATE', sequelize.col('processed_at')), 'date'],
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['date', 'status'],
        order: [['date', 'ASC']],
        raw: true
      });

      return dailyStats;
    }

    // 静态方法：获取即将到期的任务
    static async getUpcomingTasks(assigneeId, hours = 24) {
      const deadline = new Date();
      deadline.setHours(deadline.getHours() + hours);

      return this.findAll({
        where: {
          assignee_id: assigneeId,
          status: 'pending',
          deadline: {
            [sequelize.Sequelize.Op.between]: [new Date(), deadline]
          }
        },
        include: [
          {
            model: sequelize.models.WorkflowInstance,
            as: 'instance',
            include: [
              {
                model: sequelize.models.WorkflowDefinition,
                as: 'definition',
                attributes: ['name', 'category']
              },
              {
                model: sequelize.models.Employee,
                as: 'initiator',
                include: [{
                  model: sequelize.models.User,
                  as: 'user',
                  attributes: ['real_name']
                }]
              }
            ]
          }
        ],
        order: [['deadline', 'ASC']]
      });
    }

    // 实例方法：发送提醒
    async sendReminder() {
      // 这里可以集成邮件或消息推送服务
      console.log(`发送审批提醒: 任务 ${this.id} 给用户 ${this.assignee_id}`);
      
      // 更新最后提醒时间
      this.last_reminded_at = new Date();
      await this.save();
    }
  }

  ApprovalTask.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflow_instance_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_instances',
        key: 'id'
      },
      comment: '工作流实例ID'
    },
    workflow_node_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      },
      comment: '工作流节点ID'
    },
    assignee_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '审批人ID'
    },
    processed_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '实际处理人ID'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'delegated', 'cancelled'),
      defaultValue: 'pending',
      comment: '任务状态'
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '审批意见'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '附件信息'
    },
    task_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '任务数据'
    },
    deadline: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '截止时间'
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '处理时间'
    },
    delegated_from: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '委托来源任务ID'
    },
    delegated_to: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '委托目标用户ID'
    },
    last_reminded_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后提醒时间'
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal',
      comment: '优先级'
    }
  }, {
    sequelize,
    modelName: 'ApprovalTask',
    tableName: 'approval_tasks',
    timestamps: true,
    paranoid: true,
    comment: '审批任务表',
    indexes: [
      {
        fields: ['workflow_instance_id']
      },
      {
        fields: ['workflow_node_id']
      },
      {
        fields: ['assignee_id', 'status']
      },
      {
        fields: ['status']
      },
      {
        fields: ['deadline']
      },
      {
        fields: ['processed_at']
      }
    ]
  });

  return ApprovalTask;
}; 