const express = require('express');
const router = express.Router();
const scheduleController = require('../controllers/scheduleController');
const { authenticateToken } = require('../middleware/auth');

/**
 * @route   GET /api/v1/schedules
 * @desc    获取日程列表
 * @access  Private
 */
router.get('/', authenticateToken, scheduleController.getSchedules);

/**
 * @route   GET /api/v1/schedules/today
 * @desc    获取今日日程
 * @access  Private
 */
router.get('/today', authenticateToken, scheduleController.getTodaySchedules);

/**
 * @route   GET /api/v1/schedules/:id
 * @desc    获取单个日程详情
 * @access  Private
 */
router.get('/:id', authenticateToken, scheduleController.getScheduleById);

/**
 * @route   POST /api/v1/schedules
 * @desc    创建新日程
 * @access  Private
 */
router.post('/', authenticateToken, scheduleController.createSchedule);

/**
 * @route   PUT /api/v1/schedules/:id
 * @desc    更新日程
 * @access  Private
 */
router.put('/:id', authenticateToken, scheduleController.updateSchedule);

/**
 * @route   DELETE /api/v1/schedules/:id
 * @desc    删除日程
 * @access  Private
 */
router.delete('/:id', authenticateToken, scheduleController.deleteSchedule);

module.exports = router; 