const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class WorkflowNode extends Model {
    static associate(models) {
      // 关联到工作流定义
      WorkflowNode.belongsTo(models.WorkflowDefinition, {
        foreignKey: 'workflow_definition_id',
        as: 'definition'
      });

      // 关联到审批任务
      WorkflowNode.hasMany(models.ApprovalTask, {
        foreignKey: 'workflow_node_id',
        as: 'approvalTasks'
      });
    }

    // 静态方法：获取节点的下一个节点
    static async getNextNodes(currentNodeId, instanceData = {}) {
      const currentNode = await this.findByPk(currentNodeId);
      if (!currentNode) {
        throw new Error('当前节点不存在');
      }

      const allNodes = await this.findAll({
        where: {
          workflow_definition_id: currentNode.workflow_definition_id
        },
        order: [['order', 'ASC']]
      });

      // 获取当前节点后的所有节点
      const nextNodes = allNodes.filter(node => node.order > currentNode.order);
      
      if (nextNodes.length === 0) {
        return []; // 没有下一个节点
      }

      // 如果当前节点有条件配置，需要评估条件
      if (currentNode.conditions && currentNode.conditions.length > 0) {
        return this._evaluateConditions(currentNode.conditions, nextNodes, instanceData);
      }

      // 默认返回下一个顺序节点
      return [nextNodes[0]];
    }

    // 静态方法：评估条件
    static _evaluateConditions(conditions, nextNodes, instanceData) {
      for (const condition of conditions) {
        if (this._checkCondition(condition, instanceData)) {
          const targetNode = nextNodes.find(node => node.id === condition.target_node_id);
          if (targetNode) {
            return [targetNode];
          }
        }
      }

      // 如果没有条件匹配，返回默认下一个节点
      return nextNodes.length > 0 ? [nextNodes[0]] : [];
    }

    // 静态方法：检查单个条件
    static _checkCondition(condition, instanceData) {
      const { field, operator, value } = condition;
      const fieldValue = instanceData[field];

      switch (operator) {
        case 'equals':
          return fieldValue === value;
        case 'not_equals':
          return fieldValue !== value;
        case 'greater_than':
          return Number(fieldValue) > Number(value);
        case 'less_than':
          return Number(fieldValue) < Number(value);
        case 'greater_equal':
          return Number(fieldValue) >= Number(value);
        case 'less_equal':
          return Number(fieldValue) <= Number(value);
        case 'contains':
          return String(fieldValue).includes(String(value));
        case 'in':
          return Array.isArray(value) && value.includes(fieldValue);
        default:
          return false;
      }
    }

    // 实例方法：获取审批人列表
    async getApprovers() {
      if (!this.config || !this.config.approvers) {
        return [];
      }

      const approvers = [];
      for (const approverConfig of this.config.approvers) {
        if (approverConfig.type === 'employee') {
          const employee = await sequelize.models.Employee.findByPk(approverConfig.id, {
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['id', 'real_name', 'email']
            }]
          });
          if (employee) {
            approvers.push({
              id: employee.id,
              name: employee.user.real_name,
              email: employee.user.email,
              type: 'employee'
            });
          }
        } else if (approverConfig.type === 'department') {
          // 获取部门主管
          const department = await sequelize.models.Department.findByPk(approverConfig.id);
          if (department && department.manager_id) {
            const manager = await sequelize.models.Employee.findByPk(department.manager_id, {
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['id', 'real_name', 'email']
              }]
            });
            if (manager) {
              approvers.push({
                id: manager.id,
                name: manager.user.real_name,
                email: manager.user.email,
                type: 'department_manager',
                department: department.name
              });
            }
          }
        } else if (approverConfig.type === 'role') {
          // 获取角色下的所有员工
          const employees = await sequelize.models.Employee.findAll({
            include: [{
              model: sequelize.models.User,
              as: 'user',
              where: {
                role: approverConfig.role
              },
              attributes: ['id', 'real_name', 'email', 'role']
            }]
          });
          
          employees.forEach(employee => {
            approvers.push({
              id: employee.id,
              name: employee.user.real_name,
              email: employee.user.email,
              type: 'role',
              role: employee.user.role
            });
          });
        }
      }

      return approvers;
    }

    // 实例方法：检查是否为审批节点
    isApprovalNode() {
      return this.type === 'approval';
    }

    // 实例方法：检查是否为开始节点
    isStartNode() {
      return this.type === 'start';
    }

    // 实例方法：检查是否为结束节点
    isEndNode() {
      return this.type === 'end';
    }

    // 实例方法：检查是否为条件节点
    isConditionNode() {
      return this.type === 'condition';
    }

    // 实例方法：获取节点配置
    getNodeConfig() {
      return {
        id: this.id,
        name: this.name,
        type: this.type,
        order: this.order,
        config: this.config || {},
        conditions: this.conditions || [],
        timeout: this.timeout,
        description: this.description
      };
    }

    // 静态方法：创建默认审批流程节点
    static async createDefaultApprovalFlow(workflowDefinitionId, approvers = []) {
      const nodes = [
        {
          workflow_definition_id: workflowDefinitionId,
          name: '开始',
          type: 'start',
          order: 1,
          description: '流程开始'
        },
        {
          workflow_definition_id: workflowDefinitionId,
          name: '部门主管审批',
          type: 'approval',
          order: 2,
          config: {
            approvers: [{ type: 'department', id: 'current_user_department' }],
            approval_type: 'any', // any, all
            allow_reject: true,
            allow_delegate: true
          },
          timeout: 24 * 60 * 60 * 1000, // 24小时超时
          description: '部门主管审批环节'
        },
        {
          workflow_definition_id: workflowDefinitionId,
          name: 'HR审批',
          type: 'approval',
          order: 3,
          config: {
            approvers: [{ type: 'role', role: 'hr' }],
            approval_type: 'any',
            allow_reject: true,
            allow_delegate: false
          },
          timeout: 48 * 60 * 60 * 1000, // 48小时超时
          description: 'HR部门审批环节'
        },
        {
          workflow_definition_id: workflowDefinitionId,
          name: '结束',
          type: 'end',
          order: 4,
          description: '流程结束'
        }
      ];

      const createdNodes = [];
      for (const nodeData of nodes) {
        const node = await this.create(nodeData);
        createdNodes.push(node);
      }

      return createdNodes;
    }

    // 实例方法：验证节点配置
    validateConfig() {
      if (this.type === 'approval') {
        if (!this.config || !this.config.approvers || this.config.approvers.length === 0) {
          throw new Error(`审批节点 ${this.name} 必须配置审批人`);
        }

        const validApprovalTypes = ['any', 'all'];
        if (this.config.approval_type && !validApprovalTypes.includes(this.config.approval_type)) {
          throw new Error(`审批节点 ${this.name} 的审批类型无效`);
        }
      }

      if (this.type === 'condition') {
        if (!this.conditions || this.conditions.length === 0) {
          throw new Error(`条件节点 ${this.name} 必须配置条件`);
        }
      }

      return true;
    }
  }

  WorkflowNode.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflow_definition_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_definitions',
        key: 'id'
      },
      comment: '工作流定义ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '节点名称'
    },
    type: {
      type: DataTypes.ENUM('start', 'approval', 'condition', 'notification', 'end'),
      allowNull: false,
      comment: '节点类型'
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '节点顺序'
    },
    config: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '节点配置'
    },
    conditions: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '条件配置'
    },
    timeout: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '超时时间(毫秒)'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '节点描述'
    },
    x: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '节点X坐标(用于流程图显示)'
    },
    y: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '节点Y坐标(用于流程图显示)'
    }
  }, {
    sequelize,
    modelName: 'WorkflowNode',
    tableName: 'workflow_nodes',
    timestamps: true,
    paranoid: true,
    comment: '工作流节点表',
    indexes: [
      {
        fields: ['workflow_definition_id', 'order']
      },
      {
        fields: ['workflow_definition_id', 'type']
      }
    ]
  });

  return WorkflowNode;
}; 