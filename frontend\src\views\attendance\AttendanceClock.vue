<template>
  <div class="attendance-clock">
    <!-- 今日考勤状态卡片 -->
    <el-card class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>今日考勤状态</span>
          <el-tag :type="attendanceStore.todayWorkStatus.type" size="large">
            {{ attendanceStore.todayWorkStatus.text }}
          </el-tag>
        </div>
      </template>

      <div class="status-content">
        <div class="date-info">
          <h3>{{ formatDate(new Date()) }}</h3>
          <p>{{ getDayOfWeek(new Date()) }}</p>
        </div>

        <div class="clock-info" v-if="attendanceStore.todayStatus.hasClockIn">
          <div class="clock-item">
            <el-icon><Clock /></el-icon>
            <div>
              <label>上班时间</label>
              <p>{{ formatTime(attendanceStore.todayStatus.clockInTime) }}</p>
            </div>
          </div>

          <div class="clock-item" v-if="attendanceStore.todayStatus.hasClockOut">
            <el-icon><Clock /></el-icon>
            <div>
              <label>下班时间</label>
              <p>{{ formatTime(attendanceStore.todayStatus.clockOutTime) }}</p>
            </div>
          </div>

          <div class="work-duration" v-if="attendanceStore.todayStatus.workDuration">
            <label>工作时长</label>
            <p>{{ attendanceStore.todayStatus.workDuration }} 小时</p>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 打卡操作区域 -->
    <el-card class="clock-card" shadow="hover">
      <template #header>
        <span>考勤打卡</span>
      </template>

      <div class="clock-actions">
        <!-- 上班打卡按钮 -->
        <el-button
          type="primary"
          size="large"
          :disabled="!attendanceStore.canClockIn"
          :loading="attendanceStore.loading"
          @click="handleClockIn"
          class="clock-button"
        >
          <el-icon><UserFilled /></el-icon>
          上班打卡
        </el-button>

        <!-- 下班打卡按钮 -->
        <el-button
          type="success"
          size="large"
          :disabled="!attendanceStore.canClockOut"
          :loading="attendanceStore.loading"
          @click="handleClockOut"
          class="clock-button"
        >
          <el-icon><SwitchButton /></el-icon>
          下班打卡
        </el-button>
      </div>

      <!-- 位置信息 -->
      <div class="location-info" v-if="currentLocation">
        <el-icon><LocationInformation /></el-icon>
        <span>{{ currentLocation }}</span>
      </div>

      <!-- 备注输入 -->
      <el-input
        v-model="remarks"
        type="textarea"
        placeholder="备注信息（可选）"
        :rows="2"
        maxlength="500"
        show-word-limit
        class="remarks-input"
      />
    </el-card>

    <!-- 考勤记录快速查看 -->
    <el-card class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>近期考勤记录</span>
          <el-button type="text" @click="$router.push('/attendance/records')">
            查看更多
          </el-button>
        </div>
      </template>

      <div class="records-list">
        <div
          v-for="record in recentRecords"
          :key="record.id"
          class="record-item"
        >
          <div class="record-info">
            <span class="record-date">{{ formatDate(record.clock_time) }}</span>
            <span class="record-type">
              <el-tag :type="record.clock_type === 'clock_in' ? 'success' : 'warning'" size="small">
                {{ record.clock_type === 'clock_in' ? '上班' : '下班' }}
              </el-tag>
            </span>
          </div>
          <div class="record-time">{{ formatTime(record.clock_time) }}</div>
          <div class="record-status">
            <el-tag :type="getStatusType(record.status)" size="small">
              {{ getStatusText(record.status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAttendanceStore } from '@/stores/attendance'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, UserFilled, SwitchButton, LocationInformation } from '@element-plus/icons-vue'

const attendanceStore = useAttendanceStore()

// 响应式数据
const remarks = ref('')
const currentLocation = ref('')
const locationCoords = ref({ latitude: null, longitude: null })
const recentRecords = ref([])

// 计算属性
const currentTime = computed(() => {
  return new Date().toLocaleTimeString()
})

// 获取地理位置
const getCurrentLocation = () => {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        locationCoords.value = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        }
        
        // 使用逆地理编码获取地址（这里简化处理）
        currentLocation.value = `经度: ${position.coords.longitude.toFixed(6)}, 纬度: ${position.coords.latitude.toFixed(6)}`
      },
      (error) => {
        console.error('获取位置失败:', error)
        ElMessage.warning('无法获取当前位置，请检查浏览器权限设置')
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  } else {
    ElMessage.warning('浏览器不支持地理定位功能')
  }
}

// 上班打卡
const handleClockIn = async () => {
  try {
    await ElMessageBox.confirm('确认要进行上班打卡吗？', '确认操作', {
      type: 'info'
    })

    const data = {
      location: currentLocation.value,
      latitude: locationCoords.value.latitude,
      longitude: locationCoords.value.longitude,
      remarks: remarks.value.trim()
    }

    await attendanceStore.clockIn(data)
    remarks.value = ''
    await loadRecentRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('上班打卡失败:', error)
    }
  }
}

// 下班打卡
const handleClockOut = async () => {
  try {
    await ElMessageBox.confirm('确认要进行下班打卡吗？', '确认操作', {
      type: 'info'
    })

    const data = {
      location: currentLocation.value,
      latitude: locationCoords.value.latitude,
      longitude: locationCoords.value.longitude,
      remarks: remarks.value.trim()
    }

    await attendanceStore.clockOut(data)
    remarks.value = ''
    await loadRecentRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('下班打卡失败:', error)
    }
  }
}

// 加载近期考勤记录
const loadRecentRecords = async () => {
  try {
    const result = await attendanceStore.getAttendanceRecords({
      page: 1,
      pageSize: 5
    })
    recentRecords.value = result.records || []
  } catch (error) {
    console.error('加载考勤记录失败:', error)
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取星期几
const getDayOfWeek = (date) => {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return days[date.getDay()]
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    normal: 'success',
    late: 'warning',
    early: 'danger',
    overtime: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    normal: '正常',
    late: '迟到',
    early: '早退',
    overtime: '加班'
  }
  return textMap[status] || '未知'
}

// 页面加载时执行
onMounted(() => {
  getCurrentLocation()
  attendanceStore.getTodayStatus()
  loadRecentRecords()
})
</script>

<style scoped>
.attendance-clock {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.status-card,
.clock-card,
.records-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.date-info h3 {
  margin: 0;
  font-size: 24px;
  color: #409eff;
}

.date-info p {
  margin: 5px 0 0;
  color: #666;
}

.clock-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.clock-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clock-item .el-icon {
  color: #409eff;
}

.clock-item label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.clock-item p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.work-duration {
  text-align: center;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
}

.work-duration label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.work-duration p {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.clock-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 20px;
}

.clock-button {
  min-width: 150px;
  height: 60px;
  font-size: 16px;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

.remarks-input {
  margin-top: 15px;
}

.records-list {
  max-height: 200px;
  overflow-y: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.record-date {
  font-size: 14px;
  color: #666;
}

.record-time {
  font-weight: 500;
}

@media (max-width: 768px) {
  .status-content {
    flex-direction: column;
    gap: 20px;
  }

  .clock-actions {
    flex-direction: column;
    align-items: center;
  }

  .clock-button {
    width: 100%;
    max-width: 300px;
  }
}
</style> 