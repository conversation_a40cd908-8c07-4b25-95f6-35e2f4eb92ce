#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

const CONFIG = {
  development: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME || 'smart_office_dev',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'password'
  }
};

console.log('🚀 开始初始化智能办公系统数据库...\n');

async function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    console.log(`✅ ${description}完成`);
    return output.toString();
  } catch (error) {
    console.error(`❌ ${description}失败:`, error.message);
    if (error.stdout) {
      console.error('输出:', error.stdout.toString());
    }
    if (error.stderr) {
      console.error('错误:', error.stderr.toString());
    }
    throw error;
  }
}

async function initDatabase() {
  try {
    console.log('📊 检查数据库配置...');
    
    // 检查配置文件
    const configPath = path.join(__dirname, '../src/config/database.js');
    if (!fs.existsSync(configPath)) {
      console.error('❌ 数据库配置文件不存在:', configPath);
      process.exit(1);
    }

    // 运行迁移
    await runCommand(
      'npx sequelize-cli db:migrate --config src/config/database.js --migrations-path database/migrations',
      '执行数据库迁移'
    );

    // 运行种子数据
    await runCommand(
      'npx sequelize-cli db:seed:all --config src/config/database.js --seeders-path database/seeders',
      '导入种子数据'
    );

    console.log('\n🎉 数据库初始化完成！\n');
    console.log('📋 已创建的数据表:');
    console.log('   ├── roles (角色表)');
    console.log('   ├── users (用户表)');
    console.log('   ├── user_roles (用户角色关联表)');
    console.log('   ├── departments (部门表)');
    console.log('   ├── positions (职位表)');
    console.log('   ├── employees (员工表)');
    console.log('   ├── attendance_records (考勤记录表)');
    console.log('   ├── leave_requests (请假申请表)');
    console.log('   ├── meeting_rooms (会议室表)');
    console.log('   ├── meetings (会议表)');
    console.log('   ├── meeting_participants (会议参与者表)');
    console.log('   ├── workflow_definitions (工作流定义表)');
    console.log('   ├── workflow_instances (工作流实例表)');
    console.log('   └── approval_steps (审批步骤表)');
    
    console.log('\n📦 已导入的基础数据:');
    console.log('   ├── 6个系统角色 (超级管理员、管理员、人事经理等)');
    console.log('   └── 9个部门 (总经理办公室、人力资源部、技术部等)');

    console.log('\n🔧 下一步:');
    console.log('   1. 启动后端服务: npm run dev');
    console.log('   2. 访问系统: http://localhost:3000');
    console.log('   3. 创建管理员账号并分配角色');
    
  } catch (error) {
    console.error('\n💥 数据库初始化失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('   1. 检查MySQL服务是否启动');
    console.log('   2. 检查数据库连接配置');
    console.log('   3. 确保数据库用户有足够权限');
    console.log('   4. 查看详细错误日志');
    process.exit(1);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('智能办公系统数据库初始化脚本\n');
    console.log('使用方法:');
    console.log('  npm run db:init          # 初始化数据库');
    console.log('  npm run db:init --reset  # 重置并初始化数据库');
    console.log('\n环境变量:');
    console.log('  DB_HOST     数据库主机 (默认: localhost)');
    console.log('  DB_PORT     数据库端口 (默认: 3306)'); 
    console.log('  DB_NAME     数据库名称 (默认: smart_office_dev)');
    console.log('  DB_USER     数据库用户 (默认: root)');
    console.log('  DB_PASS     数据库密码 (默认: password)');
    return;
  }

  if (args.includes('--reset')) {
    console.log('⚠️  警告: 这将删除所有现有数据！');
    await runCommand(
      'npx sequelize-cli db:migrate:undo:all --config src/config/database.js --migrations-path database/migrations',
      '回滚所有迁移'
    );
  }

  await initDatabase();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { initDatabase }; 