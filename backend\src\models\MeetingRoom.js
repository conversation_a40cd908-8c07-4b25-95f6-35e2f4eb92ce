const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const MeetingRoom = sequelize.define('MeetingRoom', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '会议室名称'
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: '会议室编号'
    },
    capacity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '容纳人数'
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '位置'
    },
    floor: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '楼层'
    },
    equipment: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '设备清单'
    },
    status: {
      type: DataTypes.ENUM('available', 'maintenance', 'disabled'),
      defaultValue: 'available',
      comment: '状态'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '描述'
    },
    booking_rules: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '预订规则'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否启用'
    }
  }, {
    tableName: 'meeting_rooms',
    timestamps: true,
    underscored: true,
    comment: '会议室表',
    indexes: [
      {
        fields: ['code'],
        unique: true
      },
      {
        fields: ['status']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  MeetingRoom.associate = (models) => {
    // 会议关联
    MeetingRoom.hasMany(models.Meeting, {
      foreignKey: 'meeting_room_id',
      as: 'meetings'
    });
  };

  return MeetingRoom;
}; 