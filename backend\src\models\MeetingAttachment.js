const { DataTypes } = require('sequelize')

module.exports = (sequelize) => {
  const MeetingAttachment = sequelize.define('MeetingAttachment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    meeting_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'meetings',
        key: 'id'
      },
      comment: '会议ID'
    },
    file_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'files',
        key: 'id'
      },
      comment: '文件ID'
    },
    uploaded_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '上传者ID'
    },
    attachment_type: {
      type: DataTypes.ENUM('agenda', 'material', 'minutes', 'other'),
      defaultValue: 'material',
      comment: '附件类型'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '附件描述'
    },
    is_shared: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否共享给所有参与者'
    }
  }, {
    tableName: 'meeting_attachments',
    timestamps: true,
    underscored: true,
    comment: '会议附件表',
    indexes: [
      {
        fields: ['meeting_id']
      },
      {
        fields: ['file_id']
      },
      {
        fields: ['uploaded_by']
      },
      {
        fields: ['meeting_id', 'file_id'],
        unique: true
      }
    ]
  })

  MeetingAttachment.associate = (models) => {
    // 会议关联
    MeetingAttachment.belongsTo(models.Meeting, {
      foreignKey: 'meeting_id',
      as: 'meeting'
    })

    // 文件关联
    MeetingAttachment.belongsTo(models.File, {
      foreignKey: 'file_id',
      as: 'file'
    })

    // 上传者关联
    MeetingAttachment.belongsTo(models.User, {
      foreignKey: 'uploaded_by',
      as: 'uploader'
    })
  }

  return MeetingAttachment
} 