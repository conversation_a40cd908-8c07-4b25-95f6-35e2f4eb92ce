const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class LeaveRequest extends Model {
    static associate(models) {
      // 关联到员工表（申请人）
      LeaveRequest.belongsTo(models.Employee, {
        foreignKey: 'employee_id',
        as: 'employee'
      });

      // 关联到员工表（审批人）
      LeaveRequest.belongsTo(models.Employee, {
        foreignKey: 'approved_by',
        as: 'approver'
      });
    }

    // 静态方法：获取请假申请列表
    static async getLeaveRequests(options = {}) {
      const {
        employeeId,
        status,
        startDate,
        endDate,
        page = 1,
        pageSize = 20,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const where = {};
      
      if (employeeId) {
        where.employee_id = employeeId;
      }
      
      if (status) {
        where.status = status;
      }
      
      if (startDate && endDate) {
        where.start_date = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.Employee,
            as: 'employee',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: sequelize.models.Employee,
            as: 'approver',
            required: false,
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [[sortBy, sortOrder]]
      });

      return {
        requests: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：获取待审批的请假申请
    static async getPendingRequests(approverId) {
      return this.findAll({
        where: {
          status: 'pending'
        },
        include: [
          {
            model: sequelize.models.Employee,
            as: 'employee',
            include: [
              {
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name', 'email']
              },
              {
                model: sequelize.models.Department,
                as: 'department',
                attributes: ['name']
              }
            ]
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // 实例方法：审批请假申请
    async approve(approverId, comments = '') {
      this.status = 'approved';
      this.approved_by = approverId;
      this.approved_at = new Date();
      this.approver_comments = comments;
      return this.save();
    }

    // 实例方法：拒绝请假申请
    async reject(approverId, comments = '') {
      this.status = 'rejected';
      this.approved_by = approverId;
      this.approved_at = new Date();
      this.approver_comments = comments;
      return this.save();
    }

    // 实例方法：取消请假申请
    async cancel() {
      if (this.status !== 'pending') {
        throw new Error('只能取消待审批的请假申请');
      }
      this.status = 'cancelled';
      return this.save();
    }

    // 静态方法：统计员工请假情况
    static async getLeaveStats(employeeId, year) {
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year + 1, 0, 1);

      const requests = await this.findAll({
        where: {
          employee_id: employeeId,
          status: 'approved',
          start_date: {
            [sequelize.Sequelize.Op.between]: [startDate, endDate]
          }
        }
      });

      const stats = {
        annual: 0,
        sick: 0,
        personal: 0,
        other: 0,
        total: 0
      };

      requests.forEach(request => {
        const type = request.leave_type;
        const duration = parseFloat(request.duration);
        
        if (stats.hasOwnProperty(type)) {
          stats[type] += duration;
        }
        stats.total += duration;
      });

      return stats;
    }
  }

  LeaveRequest.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    employee_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    leave_type: {
      type: DataTypes.ENUM('annual', 'sick', 'personal', 'maternity', 'bereavement', 'other'),
      allowNull: false,
      comment: '请假类型'
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '请假开始日期'
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '请假结束日期'
    },
    start_time: {
      type: DataTypes.TIME,
      allowNull: true,
      comment: '开始时间（半天假时使用）'
    },
    end_time: {
      type: DataTypes.TIME,
      allowNull: true,
      comment: '结束时间（半天假时使用）'
    },
    duration: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: false,
      comment: '请假天数'
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '请假原因'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '附件文件列表'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'cancelled'),
      defaultValue: 'pending',
      comment: '审批状态'
    },
    approver_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '审批意见'
    },
    approved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: '审批人ID'
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '审批时间'
    }
  }, {
    sequelize,
    modelName: 'LeaveRequest',
    tableName: 'leave_requests',
    timestamps: true,
    paranoid: true,
    comment: '请假申请表'
  });

  return LeaveRequest;
}; 