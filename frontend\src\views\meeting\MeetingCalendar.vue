<template>
  <div class="meeting-calendar">
    <el-card>
      <template #header>
        <div class="calendar-header">
          <div class="header-left">
            <h3>会议日历</h3>
          </div>
          <div class="header-right">
            <el-button 
              type="primary" 
              @click="showCreateDialog = true"
              :icon="Plus"
            >
              新建会议
            </el-button>
          </div>
        </div>
      </template>

      <!-- 日历组件 -->
      <div class="calendar-container">
        <FullCalendar 
          ref="calendar"
          :options="calendarOptions"
        />
      </div>
    </el-card>

    <!-- 会议详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="currentEvent?.title"
      width="600px"
      append-to-body
    >
      <div v-if="currentEvent" class="meeting-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会议主题">
            {{ currentEvent.title }}
          </el-descriptions-item>
          <el-descriptions-item label="会议状态">
            <el-tag :type="getStatusType(currentEvent.extendedProps.status)">
              {{ getStatusText(currentEvent.extendedProps.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(currentEvent.start) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatDateTime(currentEvent.end) }}
          </el-descriptions-item>
          <el-descriptions-item label="会议室">
            {{ currentEvent.extendedProps.meetingRoom?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="组织者">
            {{ currentEvent.extendedProps.organizer?.user?.real_name }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentEvent.extendedProps.agenda" class="agenda-section">
          <h4>会议议程</h4>
          <p>{{ currentEvent.extendedProps.agenda }}</p>
        </div>

        <div class="participants-section">
          <h4>参会人员 ({{ currentEvent.extendedProps.participants?.length || 0 }})</h4>
          <div class="participants-list">
            <el-tag
              v-for="participant in currentEvent.extendedProps.participants"
              :key="participant.id"
              :type="getParticipantType(participant.status)"
              class="participant-tag"
            >
              {{ participant.employee.user.real_name }}
              ({{ getParticipantStatusText(participant.status) }})
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            v-if="canEditMeeting(currentEvent)"
            type="primary" 
            @click="editMeeting(currentEvent)"
          >
            编辑会议
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建/编辑会议对话框 -->
    <meeting-form
      v-model="showCreateDialog"
      :meeting="editingMeeting"
      @success="handleMeetingSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import { useMeetingStore } from '@/stores/meeting'
import { useUserStore } from '@/stores/user'
import MeetingForm from './components/MeetingForm.vue'
import { formatDateTime } from '@/utils/date'

const meetingStore = useMeetingStore()
const userStore = useUserStore()

// 响应式数据
const calendar = ref(null)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const currentEvent = ref(null)
const editingMeeting = ref(null)

// FullCalendar 配置
const calendarOptions = ref({
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
  headerToolbar: {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,timeGridWeek,timeGridDay'
  },
  initialView: 'dayGridMonth',
  locale: 'zh-cn',
  height: 'auto',
  firstDay: 1, // 周一为第一天
  allDaySlot: false,
  slotMinTime: '08:00:00',
  slotMaxTime: '20:00:00',
  businessHours: {
    daysOfWeek: [1, 2, 3, 4, 5], // 周一到周五
    startTime: '09:00',
    endTime: '18:00'
  },
  selectable: true,
  selectMirror: true,
  dayMaxEvents: true,
  weekends: true,
  editable: true,
  droppable: false,
  events: [],
  
  // 事件回调
  select: handleDateSelect,
  eventClick: handleEventClick,
  eventDrop: handleEventDrop,
  eventResize: handleEventResize,
  datesSet: handleDatesSet
})

// 处理日期选择（创建新会议）
function handleDateSelect(selectInfo) {
  const startTime = selectInfo.start
  const endTime = selectInfo.end || new Date(startTime.getTime() + 60 * 60 * 1000) // 默认1小时

  editingMeeting.value = {
    startTime: startTime.toISOString(),
    endTime: endTime.toISOString()
  }
  
  showCreateDialog.value = true
  
  // 清除选择
  calendar.value.getApi().unselect()
}

// 处理事件点击（查看会议详情）
function handleEventClick(clickInfo) {
  currentEvent.value = clickInfo.event
  showDetailDialog.value = true
}

// 处理事件拖拽
async function handleEventDrop(dropInfo) {
  try {
    const event = dropInfo.event
    const meetingId = event.id
    
    const updateData = {
      startTime: event.start.toISOString(),
      endTime: event.end.toISOString()
    }
    
    await meetingStore.updateMeeting(meetingId, updateData)
    ElMessage.success('会议时间已更新')
  } catch (error) {
    // 恢复原始位置
    dropInfo.revert()
    ElMessage.error('更新会议时间失败')
  }
}

// 处理事件大小调整
async function handleEventResize(resizeInfo) {
  try {
    const event = resizeInfo.event
    const meetingId = event.id
    
    const updateData = {
      startTime: event.start.toISOString(),
      endTime: event.end.toISOString()
    }
    
    await meetingStore.updateMeeting(meetingId, updateData)
    ElMessage.success('会议时间已更新')
  } catch (error) {
    // 恢复原始大小
    resizeInfo.revert()
    ElMessage.error('更新会议时间失败')
  }
}

// 处理日期变化（加载会议数据）
async function handleDatesSet(dateInfo) {
  try {
    const startDate = dateInfo.start.toISOString().split('T')[0]
    const endDate = dateInfo.end.toISOString().split('T')[0]
    
    const events = await meetingStore.fetchCalendarEvents(startDate, endDate)
    calendarOptions.value.events = events
  } catch (error) {
    console.error('加载会议数据失败:', error)
  }
}

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    scheduled: 'primary',
    ongoing: 'success',
    completed: 'info',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    scheduled: '已安排',
    ongoing: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知'
}

// 获取参会人状态类型
function getParticipantType(status) {
  const typeMap = {
    invited: 'warning',
    accepted: 'success',
    declined: 'danger',
    tentative: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取参会人状态文本
function getParticipantStatusText(status) {
  const textMap = {
    invited: '已邀请',
    accepted: '已接受',
    declined: '已拒绝',
    tentative: '暂定'
  }
  return textMap[status] || '未知'
}

// 检查是否可以编辑会议
function canEditMeeting(event) {
  if (!event || !userStore.user) return false
  
  const organizer = event.extendedProps.organizer
  return organizer && organizer.user_id === userStore.user.id
}

// 编辑会议
function editMeeting(event) {
  editingMeeting.value = {
    id: event.id,
    title: event.title,
    agenda: event.extendedProps.agenda,
    startTime: event.start.toISOString(),
    endTime: event.end.toISOString(),
    meetingRoomId: event.extendedProps.meetingRoom?.id,
    meetingType: event.extendedProps.meetingType,
    priority: event.extendedProps.priority,
    notes: event.extendedProps.notes,
    participantIds: event.extendedProps.participants?.map(p => p.employee_id) || []
  }
  
  showDetailDialog.value = false
  showCreateDialog.value = true
}

// 处理会议创建/更新成功
function handleMeetingSuccess() {
  showCreateDialog.value = false
  editingMeeting.value = null
  
  // 刷新日历数据
  const calendarApi = calendar.value.getApi()
  const currentDate = calendarApi.getDate()
  const view = calendarApi.view
  
  handleDatesSet({
    start: view.activeStart,
    end: view.activeEnd
  })
}

// 初始加载
onMounted(() => {
  // 获取会议室列表
  meetingStore.fetchMeetingRooms()
})
</script>

<style scoped>
.meeting-calendar {
  padding: 20px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-header h3 {
  margin: 0;
  color: #303133;
}

.calendar-container {
  margin-top: 20px;
}

.meeting-detail .agenda-section,
.meeting-detail .participants-section {
  margin-top: 20px;
}

.meeting-detail h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.participants-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.participant-tag {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* FullCalendar 样式覆盖 */
:deep(.fc-event) {
  border: none;
  padding: 2px 4px;
  font-size: 12px;
  cursor: pointer;
}

:deep(.fc-event-title) {
  font-weight: 500;
}

:deep(.fc-daygrid-event) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.fc-timegrid-event) {
  border-radius: 4px;
}

:deep(.fc-header-toolbar) {
  margin-bottom: 20px;
}

:deep(.fc-button-group) {
  box-shadow: none;
}

:deep(.fc-button-primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.fc-button-primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

:deep(.fc-today-button) {
  background-color: #67c23a;
  border-color: #67c23a;
}

:deep(.fc-today-button:hover) {
  background-color: #85ce61;
  border-color: #85ce61;
}
</style> 