const express = require('express');
const router = express.Router();
const meetingController = require('../controllers/meetingController');
const { authenticateToken } = require('../middleware/auth');

// 应用认证中间件到所有路由
router.use(authenticateToken);

// 会议室相关路由
router.get('/rooms', meetingController.getMeetingRooms);
router.get('/rooms/:roomId/availability', meetingController.checkRoomAvailability);

// 会议基础路由
router.get('/', meetingController.getMeetings);
router.post('/', meetingController.createMeeting);
router.get('/calendar/events', meetingController.getCalendarEvents);
router.get('/stats', meetingController.getMeetingStats);

// 用户个人会议路由
router.get('/my/organized', meetingController.getMyOrganizedMeetings);
router.get('/my/participating', meetingController.getMyParticipatingMeetings);
router.get('/my/invitations', meetingController.getMyInvitations);

// 特定会议操作路由
router.get('/:id', meetingController.getMeetingById);
router.put('/:id', meetingController.updateMeeting);

// 会议状态管理路由
router.post('/:id/cancel', meetingController.cancelMeeting);
router.post('/:id/start', meetingController.startMeeting);
router.post('/:id/end', meetingController.endMeeting);

// 参会人管理路由
router.post('/:id/respond', meetingController.respondToInvitation);

module.exports = router; 