'use strict';

const bcrypt = require('bcryptjs');

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查admin用户是否已存在
    const existingUser = await queryInterface.sequelize.query(
      "SELECT id FROM users WHERE username = 'admin' LIMIT 1",
      { type: Sequelize.QueryTypes.SELECT }
    );

    let adminUserId;

    if (existingUser.length === 0) {
      // 创建admin用户
      const hashedPassword = await bcrypt.hash('123456', 12);
      
      const adminUsers = await queryInterface.bulkInsert('users', [{
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        real_name: '系统管理员',
        phone: '13800138000',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      }], { returning: true });

      adminUserId = adminUsers[0]?.id || adminUsers[0]?.insertId;
      
      // 如果返回的ID不可用，再次查询
      if (!adminUserId) {
        const createdUser = await queryInterface.sequelize.query(
          "SELECT id FROM users WHERE username = 'admin' LIMIT 1",
          { type: Sequelize.QueryTypes.SELECT }
        );
        adminUserId = createdUser[0]?.id;
      }
    } else {
      adminUserId = existingUser[0].id;
    }

    // 检查用户角色关联是否已存在
    const existingUserRole = await queryInterface.sequelize.query(
      "SELECT id FROM user_roles WHERE user_id = ? AND role_id = 1 LIMIT 1",
      { 
        replacements: [adminUserId],
        type: Sequelize.QueryTypes.SELECT 
      }
    );

    // 如果没有角色关联，创建admin用户与SUPER_ADMIN角色的关联
    if (existingUserRole.length === 0 && adminUserId) {
      await queryInterface.bulkInsert('user_roles', [{
        user_id: adminUserId,
        role_id: 1, // SUPER_ADMIN角色ID
        created_at: new Date(),
        updated_at: new Date()
      }]);
    }
  },

  async down(queryInterface, Sequelize) {
    // 删除admin用户的角色关联
    await queryInterface.sequelize.query(
      "DELETE ur FROM user_roles ur INNER JOIN users u ON ur.user_id = u.id WHERE u.username = 'admin'"
    );
    
    // 删除admin用户
    await queryInterface.sequelize.query(
      "DELETE FROM users WHERE username = 'admin'"
    );
  }
}; 