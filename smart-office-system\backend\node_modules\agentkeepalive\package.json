{"name": "agentkeepalive", "version": "4.6.0", "description": "Missing keepalive http.Agent", "main": "index.js", "browser": "browser.js", "files": ["index.js", "index.d.ts", "browser.js", "lib"], "scripts": {"contributor": "git-contributor", "test": "npm run lint && egg-bin test --full-trace", "test-local": "egg-bin test --full-trace", "cov": "cross-env NODE_DEBUG=agentkeepalive egg-bin cov --full-trace", "ci": "npm run lint && npm run cov", "lint": "eslint lib test index.js"}, "repository": {"type": "git", "url": "git://github.com/node-modules/agentkeepalive.git"}, "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "dependencies": {"humanize-ms": "^1.2.1"}, "devDependencies": {"coffee": "^5.3.0", "cross-env": "^6.0.3", "egg-bin": "^4.9.0", "eslint": "^5.7.0", "eslint-config-egg": "^7.1.0", "git-contributor": "^2.0.0", "mm": "^2.4.1", "pedding": "^1.1.0", "typescript": "^3.8.3"}, "engines": {"node": ">= 8.0.0"}, "author": "fengmk2 <<EMAIL>> (https://github.com/fengmk2)", "license": "MIT"}