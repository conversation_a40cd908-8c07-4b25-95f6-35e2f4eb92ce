"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.removeDirAndFiles = removeDirAndFiles;
var fs = _interopRequireWildcard(require("fs"), true);
var path = _interopRequireWildcard(require("path"), true);
var querystring = _interopRequireWildcard(require("query-string"), true);
var errors = _interopRequireWildcard(require("./errors.js"), true);
var _helper = require("./internal/helper.js");
var _type = require("./internal/type.js");
exports.RETENTION_MODES = _type.RETENTION_MODES;
exports.ENCRYPTION_TYPES = _type.ENCRYPTION_TYPES;
exports.LEGAL_HOLD_STATUS = _type.LEGAL_HOLD_STATUS;
exports.RETENTION_VALIDITY_UNITS = _type.RETENTION_VALIDITY_UNITS;
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
const DEFAULT_REGION = 'us-east-1';
exports.DEFAULT_REGION = DEFAULT_REGION;
class CopySourceOptions {
  constructor({
    Bucket,
    Object,
    VersionID = '',
    MatchETag = '',
    NoMatchETag = '',
    MatchModifiedSince = null,
    MatchUnmodifiedSince = null,
    MatchRange = false,
    Start = 0,
    End = 0,
    Encryption = undefined
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.VersionID = VersionID;
    this.MatchETag = MatchETag;
    this.NoMatchETag = NoMatchETag;
    this.MatchModifiedSince = MatchModifiedSince;
    this.MatchUnmodifiedSince = MatchUnmodifiedSince;
    this.MatchRange = MatchRange;
    this.Start = Start;
    this.End = End;
    this.Encryption = Encryption;
  }
  validate() {
    if (!(0, _helper.isValidBucketName)(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Source bucket name: ' + this.Bucket);
    }
    if (!(0, _helper.isValidObjectName)(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Source object name: ${this.Object}`);
    }
    if (this.MatchRange && this.Start !== -1 && this.End !== -1 && this.Start > this.End || this.Start < 0) {
      throw new errors.InvalidObjectNameError('Source start must be non-negative, and start must be at most end.');
    } else if (this.MatchRange && !(0, _helper.isNumber)(this.Start) || !(0, _helper.isNumber)(this.End)) {
      throw new errors.InvalidObjectNameError('MatchRange is specified. But Invalid Start and End values are specified.');
    }
    return true;
  }
  getHeaders() {
    const headerOptions = {};
    headerOptions['x-amz-copy-source'] = encodeURI(this.Bucket + '/' + this.Object);
    if (!(0, _helper.isEmpty)(this.VersionID)) {
      headerOptions['x-amz-copy-source'] = `${encodeURI(this.Bucket + '/' + this.Object)}?versionId=${this.VersionID}`;
    }
    if (!(0, _helper.isEmpty)(this.MatchETag)) {
      headerOptions['x-amz-copy-source-if-match'] = this.MatchETag;
    }
    if (!(0, _helper.isEmpty)(this.NoMatchETag)) {
      headerOptions['x-amz-copy-source-if-none-match'] = this.NoMatchETag;
    }
    if (!(0, _helper.isEmpty)(this.MatchModifiedSince)) {
      headerOptions['x-amz-copy-source-if-modified-since'] = this.MatchModifiedSince;
    }
    if (!(0, _helper.isEmpty)(this.MatchUnmodifiedSince)) {
      headerOptions['x-amz-copy-source-if-unmodified-since'] = this.MatchUnmodifiedSince;
    }
    return headerOptions;
  }
}

/**
 * @deprecated use nodejs fs module
 */
exports.CopySourceOptions = CopySourceOptions;
function removeDirAndFiles(dirPath, removeSelf = true) {
  if (removeSelf) {
    return fs.rmSync(dirPath, {
      recursive: true,
      force: true
    });
  }
  fs.readdirSync(dirPath).forEach(item => {
    fs.rmSync(path.join(dirPath, item), {
      recursive: true,
      force: true
    });
  });
}
class CopyDestinationOptions {
  constructor({
    Bucket,
    Object,
    Encryption,
    UserMetadata,
    UserTags,
    LegalHold,
    RetainUntilDate,
    Mode,
    MetadataDirective
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.Encryption = Encryption ?? undefined; // null input will become undefined, easy for runtime assert
    this.UserMetadata = UserMetadata;
    this.UserTags = UserTags;
    this.LegalHold = LegalHold;
    this.Mode = Mode; // retention mode
    this.RetainUntilDate = RetainUntilDate;
    this.MetadataDirective = MetadataDirective;
  }
  getHeaders() {
    const replaceDirective = 'REPLACE';
    const headerOptions = {};
    const userTags = this.UserTags;
    if (!(0, _helper.isEmpty)(userTags)) {
      headerOptions['X-Amz-Tagging-Directive'] = replaceDirective;
      headerOptions['X-Amz-Tagging'] = (0, _helper.isObject)(userTags) ? querystring.stringify(userTags) : (0, _helper.isString)(userTags) ? userTags : '';
    }
    if (this.Mode) {
      headerOptions['X-Amz-Object-Lock-Mode'] = this.Mode; // GOVERNANCE or COMPLIANCE
    }

    if (this.RetainUntilDate) {
      headerOptions['X-Amz-Object-Lock-Retain-Until-Date'] = this.RetainUntilDate; // needs to be UTC.
    }

    if (this.LegalHold) {
      headerOptions['X-Amz-Object-Lock-Legal-Hold'] = this.LegalHold; // ON or OFF
    }

    if (this.UserMetadata) {
      for (const [key, value] of Object.entries(this.UserMetadata)) {
        headerOptions[`X-Amz-Meta-${key}`] = value.toString();
      }
    }
    if (this.MetadataDirective) {
      headerOptions[`X-Amz-Metadata-Directive`] = this.MetadataDirective;
    }
    if (this.Encryption) {
      const encryptionHeaders = (0, _helper.getEncryptionHeaders)(this.Encryption);
      for (const [key, value] of Object.entries(encryptionHeaders)) {
        headerOptions[key] = value;
      }
    }
    return headerOptions;
  }
  validate() {
    if (!(0, _helper.isValidBucketName)(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Destination bucket name: ' + this.Bucket);
    }
    if (!(0, _helper.isValidObjectName)(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Destination object name: ${this.Object}`);
    }
    if (!(0, _helper.isEmpty)(this.UserMetadata) && !(0, _helper.isObject)(this.UserMetadata)) {
      throw new errors.InvalidObjectNameError(`Destination UserMetadata should be an object with key value pairs`);
    }
    if (!(0, _helper.isEmpty)(this.Mode) && ![_type.RETENTION_MODES.GOVERNANCE, _type.RETENTION_MODES.COMPLIANCE].includes(this.Mode)) {
      throw new errors.InvalidObjectNameError(`Invalid Mode specified for destination object it should be one of [GOVERNANCE,COMPLIANCE]`);
    }
    if (this.Encryption !== undefined && (0, _helper.isEmptyObject)(this.Encryption)) {
      throw new errors.InvalidObjectNameError(`Invalid Encryption configuration for destination object `);
    }
    return true;
  }
}

/**
 * maybe this should be a generic type for Records, leave it for later refactor
 */
exports.CopyDestinationOptions = CopyDestinationOptions;
class SelectResults {
  constructor({
    records,
    // parsed data as stream
    response,
    // original response stream
    stats,
    // stats as xml
    progress // stats as xml
  }) {
    this.records = records;
    this.response = response;
    this.stats = stats;
    this.progress = progress;
  }
  setStats(stats) {
    this.stats = stats;
  }
  getStats() {
    return this.stats;
  }
  setProgress(progress) {
    this.progress = progress;
  }
  getProgress() {
    return this.progress;
  }
  setResponse(response) {
    this.response = response;
  }
  getResponse() {
    return this.response;
  }
  setRecords(records) {
    this.records = records;
  }
  getRecords() {
    return this.records;
  }
}
exports.SelectResults = SelectResults;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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