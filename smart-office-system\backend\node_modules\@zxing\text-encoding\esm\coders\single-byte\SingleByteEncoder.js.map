{"version": 3, "file": "SingleByteEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/single-byte/SingleByteEncoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE7E;;;;;GAKG;AACH;IAIE,2BAAoB,KAAwB,EAAE,OAA4B;QAAtD,UAAK,GAAL,KAAK,CAAmB;QAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,mCAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,8DAA8D;QAC9D,eAAe;QACf,IAAM,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAExD,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,YAAY,CAAC,UAAU,CAAC,CAAC;QAE3B,kDAAkD;QAClD,OAAO,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IACH,wBAAC;AAAD,CAAC,AAlCD,IAkCC"}