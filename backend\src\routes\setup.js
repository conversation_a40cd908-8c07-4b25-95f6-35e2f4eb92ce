const express = require('express');
const router = express.Router();
const { User } = require('../models');
const bcrypt = require('bcryptjs');

// 临时设置路由 - 创建admin用户
router.post('/create-admin', async (req, res) => {
  try {
    console.log('🔧 API: 创建admin用户...');
    
    // 检查现有用户
    const allUsers = await User.findAll({
      attributes: ['id', 'username', 'email', 'status']
    });
    console.log('📋 现有用户:', allUsers.length, '个');
    
    // 删除现有admin用户（如果存在）
    const existingAdmin = await User.findOne({
      where: { username: 'admin' }
    });
    
    if (existingAdmin) {
      console.log('🗑️  删除现有admin用户...');
      await existingAdmin.destroy();
    }
    
    // 手动创建密码哈希
    const hashedPassword = await bcrypt.hash('123456', 12);
    console.log('🔑 密码哈希生成成功');
    
    // 创建admin用户
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,  // 直接使用哈希密码
      real_name: '系统管理员',
      phone: '13800138000',
      status: 'active'
    });
    
    console.log('✅ admin用户创建成功！');
    
    // 验证创建结果
    const verifyUser = await User.findByCredentials('admin');
    const isPasswordValid = verifyUser ? await verifyUser.validatePassword('123456') : false;
    
    // 再次查询所有用户
    const finalUsers = await User.findAll({
      attributes: ['id', 'username', 'email', 'status']
    });
    
    res.json({
      success: true,
      message: 'admin用户创建成功',
      data: {
        adminUser: {
          id: adminUser.id,
          username: adminUser.username,
          email: adminUser.email,
          real_name: adminUser.real_name,
          status: adminUser.status
        },
        verification: {
          canFindByCredentials: !!verifyUser,
          passwordValid: isPasswordValid
        },
        totalUsers: finalUsers.length,
        allUsers: finalUsers.map(u => ({
          id: u.id,
          username: u.username,
          email: u.email,
          status: u.status
        }))
      }
    });
    
  } catch (error) {
    console.error('❌ 创建admin用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建admin用户失败',
      error: error.message
    });
  }
});

// 重置admin密码
router.post('/reset-admin-password', async (req, res) => {
  try {
    console.log('🔧 API: 重置admin密码...');

    // 查找admin用户
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });

    if (!adminUser) {
      return res.status(404).json({
        success: false,
        message: 'admin用户不存在'
      });
    }

    console.log('📋 找到admin用户:', {
      id: adminUser.id,
      username: adminUser.username,
      currentPasswordHash: adminUser.password.substring(0, 20) + '...'
    });

    // 使用updatePassword方法重置密码（会自动哈希）
    await adminUser.updatePassword('123456');
    console.log('✅ 密码重置成功');

    // 重新获取用户验证
    await adminUser.reload();

    // 测试密码验证
    const isPasswordValid = await adminUser.validatePassword('123456');
    console.log('🔍 密码验证测试:', isPasswordValid ? '✅ 正确' : '❌ 错误');

    res.json({
      success: true,
      message: 'admin密码重置成功',
      data: {
        userId: adminUser.id,
        username: adminUser.username,
        passwordValid: isPasswordValid,
        newPasswordHash: adminUser.password.substring(0, 20) + '...'
      }
    });

  } catch (error) {
    console.error('❌ 重置admin密码失败:', error);
    res.status(500).json({
      success: false,
      message: '重置admin密码失败',
      error: error.message
    });
  }
});

// 测试登录
router.post('/test-login', async (req, res) => {
  try {
    console.log('🔧 API: 测试登录...');

    // 查找admin用户
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });

    if (!adminUser) {
      return res.status(404).json({
        success: false,
        message: 'admin用户不存在'
      });
    }

    console.log('📋 找到admin用户:', {
      id: adminUser.id,
      username: adminUser.username,
      status: adminUser.status
    });

    // 测试不同的密码
    const testPasswords = ['123456', 'admin', 'password'];
    const results = {};

    for (const testPassword of testPasswords) {
      try {
        const isValid = await adminUser.validatePassword(testPassword);
        results[testPassword] = isValid;
        console.log(`🔍 密码 "${testPassword}" 验证:`, isValid ? '✅ 正确' : '❌ 错误');
      } catch (error) {
        results[testPassword] = `错误: ${error.message}`;
        console.log(`🔍 密码 "${testPassword}" 验证错误:`, error.message);
      }
    }

    // 显示当前密码哈希
    console.log('🔑 当前密码哈希:', adminUser.password);

    // 手动创建新的密码哈希进行比较
    const bcrypt = require('bcryptjs');
    const newHash = await bcrypt.hash('123456', 12);
    const manualTest = await bcrypt.compare('123456', newHash);
    console.log('🔍 手动哈希测试:', manualTest ? '✅ 正确' : '❌ 错误');

    res.json({
      success: true,
      message: '登录测试完成',
      data: {
        userId: adminUser.id,
        username: adminUser.username,
        status: adminUser.status,
        currentPasswordHash: adminUser.password,
        passwordTests: results,
        manualHashTest: manualTest,
        newHashSample: newHash
      }
    });

  } catch (error) {
    console.error('❌ 测试登录失败:', error);
    res.status(500).json({
      success: false,
      message: '测试登录失败',
      error: error.message
    });
  }
});

// 修复admin密码（绕过双重哈希问题）
router.post('/fix-admin-password', async (req, res) => {
  try {
    console.log('🔧 API: 修复admin密码...');

    // 查找admin用户
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });

    if (!adminUser) {
      return res.status(404).json({
        success: false,
        message: 'admin用户不存在'
      });
    }

    console.log('📋 找到admin用户:', {
      id: adminUser.id,
      username: adminUser.username,
      oldPasswordHash: adminUser.password.substring(0, 20) + '...'
    });

    // 手动创建正确的密码哈希
    const bcrypt = require('bcryptjs');
    const correctHash = await bcrypt.hash('123456', 12);
    console.log('🔑 生成正确的密码哈希');

    // 直接更新数据库，绕过Sequelize的hooks
    await adminUser.update({ password: correctHash }, {
      hooks: false  // 关键：禁用hooks避免再次哈希
    });

    console.log('✅ 密码更新成功');

    // 重新获取用户并测试
    await adminUser.reload();
    const isPasswordValid = await adminUser.validatePassword('123456');
    console.log('🔍 密码验证测试:', isPasswordValid ? '✅ 正确' : '❌ 错误');

    res.json({
      success: true,
      message: 'admin密码修复成功',
      data: {
        userId: adminUser.id,
        username: adminUser.username,
        passwordValid: isPasswordValid,
        newPasswordHash: adminUser.password.substring(0, 20) + '...'
      }
    });

  } catch (error) {
    console.error('❌ 修复admin密码失败:', error);
    res.status(500).json({
      success: false,
      message: '修复admin密码失败',
      error: error.message
    });
  }
});

// 检查数据库数据
router.get('/check-database', async (req, res) => {
  try {
    console.log('🔍 API: 检查数据库数据...');

    const { User, Employee, Department, Position } = require('../models');

    // 检查所有表的数据
    const userCount = await User.count();
    const employeeCount = await Employee.count();
    const departmentCount = await Department.count();
    const positionCount = await Position.count();

    console.log('📊 数据库统计:', {
      users: userCount,
      employees: employeeCount,
      departments: departmentCount,
      positions: positionCount
    });

    // 获取详细数据
    const users = await User.findAll({
      attributes: ['id', 'username', 'email', 'real_name', 'status'],
      limit: 5
    });

    const employees = await Employee.findAll({
      include: [
        { model: User, as: 'user', attributes: ['username', 'real_name'] },
        { model: Department, as: 'department', attributes: ['name'] },
        { model: Position, as: 'position', attributes: ['name', 'level'] }
      ],
      limit: 5
    });

    const departments = await Department.findAll({
      attributes: ['id', 'name', 'code', 'status'],
      limit: 10
    });

    const positions = await Position.findAll({
      attributes: ['id', 'name', 'code', 'level', 'status'],
      limit: 10
    });

    // 原生SQL查询
    const [sqlEmployees] = await require('../models').sequelize.query('SELECT * FROM employees LIMIT 5');
    const [sqlUsers] = await require('../models').sequelize.query('SELECT * FROM users LIMIT 5');

    res.json({
      success: true,
      message: '数据库检查完成',
      data: {
        counts: {
          users: userCount,
          employees: employeeCount,
          departments: departmentCount,
          positions: positionCount
        },
        samples: {
          users: users.map(u => ({
            id: u.id,
            username: u.username,
            real_name: u.real_name,
            status: u.status
          })),
          employees: employees.map(e => ({
            id: e.id,
            employee_no: e.employee_no,
            user: e.user?.real_name || 'N/A',
            department: e.department?.name || 'N/A',
            position: e.position?.name || 'N/A',
            status: e.status
          })),
          departments: departments.map(d => ({
            id: d.id,
            name: d.name,
            code: d.code,
            status: d.status
          })),
          positions: positions.map(p => ({
            id: p.id,
            name: p.name,
            level: p.level,
            status: p.status
          }))
        },
        rawSql: {
          employees: sqlEmployees,
          users: sqlUsers
        }
      }
    });

  } catch (error) {
    console.error('❌ 检查数据库失败:', error);
    res.status(500).json({
      success: false,
      message: '检查数据库失败',
      error: error.message
    });
  }
});

// 创建完整测试数据
router.post('/create-test-data', async (req, res) => {
  try {
    console.log('🔧 API: 创建完整测试数据...');

    const { User, Employee, Department, Position } = require('../models');
    const bcrypt = require('bcryptjs');

    // 创建部门
    const departments = [
      { name: '技术部', code: 'TECH', description: '技术开发部门', status: 'active' },
      { name: '产品部', code: 'PROD', description: '产品管理部门', status: 'active' },
      { name: '市场部', code: 'MKT', description: '市场营销部门', status: 'active' },
      { name: '人事部', code: 'HR', description: '人力资源部门', status: 'active' }
    ];

    console.log('📋 创建部门...');
    const createdDepartments = [];
    for (const dept of departments) {
      const [department] = await Department.findOrCreate({
        where: { code: dept.code },
        defaults: dept
      });
      createdDepartments.push(department);
      console.log(`   - ${department.name} (ID: ${department.id})`);
    }

    // 创建职位
    const positions = [
      { name: 'CEO', code: 'CEO', level: 8, description: '首席执行官', status: 'active' },
      { name: '技术总监', code: 'CTO', level: 7, description: '技术部门负责人', status: 'active' },
      { name: '产品经理', code: 'PM', level: 5, description: '产品管理', status: 'active' },
      { name: '前端工程师', code: 'FE', level: 3, description: '前端开发', status: 'active' },
      { name: '后端工程师', code: 'BE', level: 3, description: '后端开发', status: 'active' },
      { name: 'UI设计师', code: 'UI', level: 3, description: 'UI设计', status: 'active' },
      { name: '市场专员', code: 'MKT_SPEC', level: 2, description: '市场推广', status: 'active' },
      { name: '人事专员', code: 'HR_SPEC', level: 2, description: '人力资源', status: 'active' }
    ];

    console.log('💼 创建职位...');
    const createdPositions = [];
    for (const pos of positions) {
      const [position] = await Position.findOrCreate({
        where: { code: pos.code },
        defaults: pos
      });
      createdPositions.push(position);
      console.log(`   - ${position.name} (L${position.level}, ID: ${position.id})`);
    }

    // 创建测试员工
    const employees = [
      {
        username: 'zhangsan',
        email: '<EMAIL>',
        real_name: '张三',
        phone: '13800138001',
        departmentCode: 'TECH',
        positionCode: 'CTO'
      },
      {
        username: 'lisi',
        email: '<EMAIL>',
        real_name: '李四',
        phone: '13800138002',
        departmentCode: 'TECH',
        positionCode: 'FE'
      },
      {
        username: 'wangwu',
        email: '<EMAIL>',
        real_name: '王五',
        phone: '13800138003',
        departmentCode: 'TECH',
        positionCode: 'BE'
      },
      {
        username: 'zhaoliu',
        email: '<EMAIL>',
        real_name: '赵六',
        phone: '13800138004',
        departmentCode: 'PROD',
        positionCode: 'PM'
      },
      {
        username: 'sunqi',
        email: '<EMAIL>',
        real_name: '孙七',
        phone: '13800138005',
        departmentCode: 'TECH',
        positionCode: 'UI'
      }
    ];

    console.log('👥 创建员工...');
    const createdEmployees = [];
    const hashedPassword = await bcrypt.hash('123456', 12);

    for (let i = 0; i < employees.length; i++) {
      const emp = employees[i];

      // 查找部门和职位
      const department = createdDepartments.find(d => d.code === emp.departmentCode);
      const position = createdPositions.find(p => p.code === emp.positionCode);

      try {
        // 创建用户
        const [user] = await User.findOrCreate({
          where: { username: emp.username },
          defaults: {
            username: emp.username,
            email: emp.email,
            password: hashedPassword,
            real_name: emp.real_name,
            phone: emp.phone,
            status: 'active'
          }
        });

        // 创建员工
        const [employee] = await Employee.findOrCreate({
          where: { user_id: user.id },
          defaults: {
            user_id: user.id,
            employee_no: `EMP${String(i + 1).padStart(3, '0')}`,
            department_id: department.id,
            position_id: position.id,
            hire_date: '2024-01-01',
            status: 'active'
          }
        });

        createdEmployees.push(employee);
        console.log(`   - ${emp.real_name} (${emp.username}) - ${department.name} - ${position.name}`);

      } catch (error) {
        console.log(`   ⚠️  员工 ${emp.real_name} 创建失败: ${error.message}`);
      }
    }

    // 验证创建结果
    const finalCounts = {
      users: await User.count(),
      employees: await Employee.count(),
      departments: await Department.count(),
      positions: await Position.count()
    };

    console.log('📊 最终统计:', finalCounts);

    res.json({
      success: true,
      message: '测试数据创建完成',
      data: {
        counts: finalCounts,
        created: {
          departments: createdDepartments.length,
          positions: createdPositions.length,
          employees: createdEmployees.length
        }
      }
    });

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    res.status(500).json({
      success: false,
      message: '创建测试数据失败',
      error: error.message
    });
  }
});

// 创建测试会议数据
router.post('/create-meeting-data', async (req, res) => {
  try {
    console.log('🔧 API: 创建测试会议数据...');

    const { Meeting, MeetingRoom, User, Employee } = require('../models');

    // 创建会议室
    const meetingRooms = [
      { name: '会议室A', code: 'ROOM_A', location: '1楼', capacity: 10, equipment: '投影仪,白板', status: 'active' },
      { name: '会议室B', code: 'ROOM_B', location: '2楼', capacity: 20, equipment: '投影仪,音响,白板', status: 'active' },
      { name: '会议室C', code: 'ROOM_C', location: '3楼', capacity: 6, equipment: '白板', status: 'active' }
    ];

    console.log('🏢 创建会议室...');
    const createdRooms = [];
    for (const room of meetingRooms) {
      const [meetingRoom] = await MeetingRoom.findOrCreate({
        where: { code: room.code },
        defaults: room
      });
      createdRooms.push(meetingRoom);
      console.log(`   - ${meetingRoom.name} (容量: ${meetingRoom.capacity}人)`);
    }

    // 获取现有用户和员工
    const users = await User.findAll({ limit: 5 });
    const employees = await Employee.findAll({ limit: 5 });

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有找到用户数据，请先创建用户'
      });
    }

    // 创建测试会议
    const meetings = [
      {
        title: '项目启动会议',
        description: '讨论新项目的启动计划和资源分配',
        agenda: '1. 项目背景介绍\n2. 团队分工\n3. 时间安排\n4. 资源需求',
        room_id: createdRooms[0].id,
        organizer_id: users[0].id,
        start_time: new Date(Date.now() + 24 * 60 * 60 * 1000), // 明天
        end_time: new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 明天+2小时
        status: 'pending'
      },
      {
        title: '周例会',
        description: '每周工作总结和下周计划',
        agenda: '1. 上周工作总结\n2. 本周工作计划\n3. 问题讨论',
        room_id: createdRooms[1].id,
        organizer_id: users[1] ? users[1].id : users[0].id,
        start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 下周
        end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000), // 下周+1小时
        status: 'pending'
      },
      {
        title: '技术分享会',
        description: '前端新技术分享',
        agenda: '1. Vue3新特性\n2. TypeScript实践\n3. 性能优化技巧',
        room_id: createdRooms[2].id,
        organizer_id: users[2] ? users[2].id : users[0].id,
        start_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后
        end_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 90 * 60 * 1000), // 3天后+1.5小时
        status: 'pending'
      }
    ];

    console.log('📅 创建会议...');
    const createdMeetings = [];
    for (const meeting of meetings) {
      try {
        const createdMeeting = await Meeting.create(meeting);
        createdMeetings.push(createdMeeting);
        console.log(`   - ${meeting.title} (${meeting.start_time.toLocaleString()})`);
      } catch (error) {
        console.log(`   ⚠️  会议 ${meeting.title} 创建失败: ${error.message}`);
      }
    }

    // 验证创建结果
    const finalCounts = {
      meetingRooms: await MeetingRoom.count(),
      meetings: await Meeting.count()
    };

    console.log('📊 会议数据统计:', finalCounts);

    res.json({
      success: true,
      message: '测试会议数据创建完成',
      data: {
        counts: finalCounts,
        created: {
          meetingRooms: createdRooms.length,
          meetings: createdMeetings.length
        }
      }
    });

  } catch (error) {
    console.error('❌ 创建测试会议数据失败:', error);
    res.status(500).json({
      success: false,
      message: '创建测试会议数据失败',
      error: error.message
    });
  }
});

// 创建完整系统测试数据
router.post('/create-all-test-data', async (req, res) => {
  try {
    console.log('🔧 API: 创建完整系统测试数据...');

    const { User, Employee, Schedule, OfficeSupply, SupplyRequest, WorkflowDefinition, WorkflowNode } = require('../models');

    // 获取现有用户
    const users = await User.findAll({ limit: 10 });
    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有找到用户数据，请先创建用户'
      });
    }

    console.log('📅 创建日程安排测试数据...');

    // 创建日程安排测试数据
    const schedules = [
      {
        user_id: users[0].id,
        title: '项目启动会议',
        description: '讨论新项目的启动计划和资源分配',
        start_time: new Date(Date.now() + 24 * 60 * 60 * 1000), // 明天
        end_time: new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 明天+2小时
        type: 'meeting',
        priority: 'high',
        location: '会议室A',
        reminder_minutes: 30
      },
      {
        user_id: users[1] ? users[1].id : users[0].id,
        title: '客户拜访',
        description: '拜访重要客户，讨论合作事宜',
        start_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 后天
        end_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 后天+3小时
        type: 'work',
        priority: 'medium',
        location: '客户公司',
        reminder_minutes: 60
      },
      {
        user_id: users[0].id,
        title: '团队建设活动',
        description: '部门团队建设活动',
        start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 下周
        end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000), // 下周+4小时
        type: 'personal',
        priority: 'low',
        location: '户外',
        is_all_day: false,
        reminder_minutes: 120
      }
    ];

    const createdSchedules = [];
    for (const schedule of schedules) {
      try {
        const createdSchedule = await Schedule.create(schedule);
        createdSchedules.push(createdSchedule);
        console.log(`   - ${schedule.title} (${schedule.start_time.toLocaleString()})`);
      } catch (error) {
        console.log(`   ⚠️  日程 ${schedule.title} 创建失败: ${error.message}`);
      }
    }

    console.log('📦 创建办公用品测试数据...');

    // 创建办公用品测试数据
    const supplies = [
      {
        name: '中性笔',
        code: 'PEN001',
        category: 'stationery',
        brand: '晨光',
        model: 'GP-1008',
        unit: '支',
        unit_price: 2.50,
        current_stock: 50,
        min_stock: 20,
        max_stock: 200,
        location: '仓库A-01',
        supplier: '晨光文具',
        status: 'active',
        description: '0.5mm黑色中性笔，书写流畅',
        created_by: users[0].id
      },
      {
        name: 'A4复印纸',
        code: 'PAPER001',
        category: 'consumables',
        brand: '得力',
        model: 'A4-80g',
        unit: '包',
        unit_price: 25.00,
        current_stock: 5,
        min_stock: 10,
        max_stock: 100,
        location: '仓库A-02',
        supplier: '得力办公',
        status: 'active',
        description: '80g A4复印纸，500张/包',
        created_by: users[0].id
      },
      {
        name: '订书机',
        code: 'STAPLER001',
        category: 'stationery',
        brand: '得力',
        model: 'DL-0012',
        unit: '个',
        unit_price: 15.00,
        current_stock: 8,
        min_stock: 5,
        max_stock: 50,
        location: '仓库A-03',
        supplier: '得力办公',
        status: 'active',
        description: '标准订书机，可装订20页',
        created_by: users[0].id
      },
      {
        name: '无线鼠标',
        code: 'MOUSE001',
        category: 'electronics',
        brand: '罗技',
        model: 'M220',
        unit: '个',
        unit_price: 89.00,
        current_stock: 3,
        min_stock: 5,
        max_stock: 30,
        location: '仓库B-01',
        supplier: '罗技科技',
        status: 'active',
        description: '2.4G无线鼠标，静音设计',
        created_by: users[0].id
      }
    ];

    const createdSupplies = [];
    for (const supply of supplies) {
      try {
        const createdSupply = await OfficeSupply.create(supply);
        createdSupplies.push(createdSupply);
        console.log(`   - ${supply.name} (库存: ${supply.current_stock}${supply.unit})`);
      } catch (error) {
        console.log(`   ⚠️  用品 ${supply.name} 创建失败: ${error.message}`);
      }
    }

    // 创建申请记录
    if (createdSupplies.length > 0) {
      console.log('📝 创建申请记录...');

      const requests = [
        {
          request_no: `REQ${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}001`,
          requester_id: users[1] ? users[1].id : users[0].id,
          supply_id: createdSupplies[0].id,
          quantity: 10,
          unit_price: createdSupplies[0].unit_price,
          total_amount: createdSupplies[0].unit_price * 10,
          purpose: '日常办公使用',
          urgency: 'medium',
          status: 'pending'
        },
        {
          request_no: `REQ${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}002`,
          requester_id: users[2] ? users[2].id : users[0].id,
          supply_id: createdSupplies[1].id,
          quantity: 2,
          unit_price: createdSupplies[1].unit_price,
          total_amount: createdSupplies[1].unit_price * 2,
          purpose: '打印重要文件',
          urgency: 'high',
          status: 'pending'
        }
      ];

      for (const request of requests) {
        try {
          await SupplyRequest.create(request);
          console.log(`   - 申请单 ${request.request_no}`);
        } catch (error) {
          console.log(`   ⚠️  申请单创建失败: ${error.message}`);
        }
      }
    }

    // 验证创建结果
    const finalCounts = {
      schedules: await Schedule.count(),
      supplies: await OfficeSupply.count(),
      requests: await SupplyRequest.count()
    };

    console.log('📊 最终统计:', finalCounts);

    res.json({
      success: true,
      message: '完整系统测试数据创建完成',
      data: {
        counts: finalCounts,
        created: {
          schedules: createdSchedules.length,
          supplies: createdSupplies.length
        }
      }
    });

  } catch (error) {
    console.error('❌ 创建完整系统测试数据失败:', error);
    res.status(500).json({
      success: false,
      message: '创建完整系统测试数据失败',
      error: error.message
    });
  }
});

// 检查用户状态
router.get('/check-users', async (req, res) => {
  try {
    const allUsers = await User.findAll({
      attributes: ['id', 'username', 'email', 'status', 'created_at']
    });
    
    const adminUser = await User.findByCredentials('admin');
    
    res.json({
      success: true,
      data: {
        totalUsers: allUsers.length,
        hasAdmin: !!adminUser,
        allUsers: allUsers.map(u => ({
          id: u.id,
          username: u.username,
          email: u.email,
          status: u.status,
          created_at: u.created_at
        }))
      }
    });
  } catch (error) {
    console.error('❌ 检查用户失败:', error);
    res.status(500).json({
      success: false,
      message: '检查用户失败',
      error: error.message
    });
  }
});

module.exports = router;
