const attendanceService = require('../services/attendanceService');
const leaveService = require('../services/leaveService');
const { success, error } = require('../shared/response');
const { ValidationError, BusinessError } = require('../shared/errors');

class AttendanceController {
  // 上班打卡
  async clockIn(req, res) {
    try {
      const employeeId = req.user.employeeId; // 从JWT token中获取员工ID
      const { location, latitude, longitude, remarks } = req.body;

      const result = await attendanceService.clockIn(employeeId, {
        location,
        latitude,
        longitude,
        remarks
      });

      return success(res, result, '上班打卡成功');
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        return error(res, error.message, 400);
      }
      return error(res, '打卡失败', 500);
    }
  }

  // 下班打卡
  async clockOut(req, res) {
    try {
      const employeeId = req.user.employeeId;
      const { location, latitude, longitude, remarks } = req.body;

      const result = await attendanceService.clockOut(employeeId, {
        location,
        latitude,
        longitude,
        remarks
      });

      return success(res, result, '下班打卡成功');
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        return error(res, error.message, 400);
      }
      return error(res, '打卡失败', 500);
    }
  }

  // 获取考勤记录
  async getAttendanceRecords(req, res) {
    try {
      const {
        employeeId,
        startDate,
        endDate,
        page = 1,
        pageSize = 20,
        clockType
      } = req.query;

      // 如果不是管理员，只能查看自己的考勤记录
      const finalEmployeeId = req.user.role === 'admin' ? employeeId : req.user.employeeId;

      const result = await attendanceService.getAttendanceRecords({
        employeeId: finalEmployeeId,
        startDate,
        endDate,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        clockType
      });

      return success(res, result, '获取考勤记录成功');
    } catch (error) {
      return error(res, '获取考勤记录失败', 500);
    }
  }

  // 获取今日考勤状态
  async getTodayStatus(req, res) {
    try {
      const employeeId = req.user.employeeId;
      const result = await attendanceService.getTodayAttendanceStatus(employeeId);

      return success(res, result, '获取今日考勤状态成功');
    } catch (error) {
      return error(res, '获取今日考勤状态失败', 500);
    }
  }

  // 获取考勤统计
  async getAttendanceStats(req, res) {
    try {
      const { employeeId, startDate, endDate } = req.query;

      // 如果不是管理员，只能查看自己的统计
      const finalEmployeeId = req.user.role === 'admin' ? employeeId : req.user.employeeId;

      if (!startDate || !endDate) {
        return error(res, '请提供开始日期和结束日期', 400);
      }

      const result = await attendanceService.getAttendanceStats(finalEmployeeId, startDate, endDate);

      return success(res, result, '获取考勤统计成功');
    } catch (error) {
      return error(res, '获取考勤统计失败', 500);
    }
  }

  // 获取部门考勤汇总（管理员功能）
  async getDepartmentStats(req, res) {
    try {
      const { departmentId, date } = req.query;

      if (req.user.role !== 'admin') {
        return error(res, '权限不足', 403);
      }

      if (!departmentId || !date) {
        return error(res, '请提供部门ID和日期', 400);
      }

      const result = await attendanceService.getDepartmentAttendanceStats(departmentId, date);

      return success(res, result, '获取部门考勤汇总成功');
    } catch (error) {
      return error(res, '获取部门考勤汇总失败', 500);
    }
  }

  // 提交请假申请
  async submitLeaveRequest(req, res) {
    try {
      const employeeId = req.user.employeeId;
      const {
        leaveType,
        startDate,
        endDate,
        startTime,
        endTime,
        reason,
        attachments
      } = req.body;

      const result = await leaveService.submitLeaveRequest(employeeId, {
        leaveType,
        startDate,
        endDate,
        startTime,
        endTime,
        reason,
        attachments
      });

      return success(res, result, '请假申请提交成功');
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        return error(res, error.message, 400);
      }
      return error(res, '提交请假申请失败', 500);
    }
  }

  // 获取请假申请列表
  async getLeaveRequests(req, res) {
    try {
      const {
        employeeId,
        status,
        startDate,
        endDate,
        page = 1,
        pageSize = 20
      } = req.query;

      // 如果不是管理员，只能查看自己的请假申请
      const finalEmployeeId = req.user.role === 'admin' ? employeeId : req.user.employeeId;

      const result = await leaveService.getLeaveRequests({
        employeeId: finalEmployeeId,
        status,
        startDate,
        endDate,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      return success(res, result, '获取请假申请列表成功');
    } catch (error) {
      return error(res, '获取请假申请列表失败', 500);
    }
  }

  // 获取待审批的请假申请
  async getPendingLeaveRequests(req, res) {
    try {
      const approverId = req.user.employeeId;
      const result = await leaveService.getPendingRequests(approverId);

      return success(res, result, '获取待审批请假申请成功');
    } catch (error) {
      return error(res, '获取待审批请假申请失败', 500);
    }
  }

  // 审批请假申请
  async approveLeaveRequest(req, res) {
    try {
      const { id } = req.params;
      const { action, comments } = req.body;
      const approverId = req.user.employeeId;

      const result = await leaveService.approveLeaveRequest(id, approverId, {
        action,
        comments
      });

      return success(res, result, '请假审批操作成功');
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        return error(res, error.message, 400);
      }
      return error(res, '请假审批操作失败', 500);
    }
  }

  // 取消请假申请
  async cancelLeaveRequest(req, res) {
    try {
      const { id } = req.params;
      const employeeId = req.user.employeeId;

      const result = await leaveService.cancelLeaveRequest(id, employeeId);

      return success(res, result, '请假申请取消成功');
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BusinessError) {
        return error(res, error.message, 400);
      }
      return error(res, '取消请假申请失败', 500);
    }
  }

  // 获取请假申请详情
  async getLeaveRequestDetail(req, res) {
    try {
      const { id } = req.params;
      const result = await leaveService.getLeaveRequestDetail(id);

      return success(res, result, '获取请假申请详情成功');
    } catch (error) {
      if (error instanceof ValidationError) {
        return error(res, error.message, 404);
      }
      return error(res, '获取请假申请详情失败', 500);
    }
  }

  // 获取请假统计
  async getLeaveStats(req, res) {
    try {
      const { employeeId, year } = req.query;

      // 如果不是管理员，只能查看自己的统计
      const finalEmployeeId = req.user.role === 'admin' ? employeeId : req.user.employeeId;
      const finalYear = year ? parseInt(year) : new Date().getFullYear();

      const result = await leaveService.getLeaveStats(finalEmployeeId, finalYear);

      return success(res, result, '获取请假统计成功');
    } catch (error) {
      return error(res, '获取请假统计失败', 500);
    }
  }

  // 获取假期余额
  async getLeaveBalance(req, res) {
    try {
      const { employeeId, year } = req.query;

      // 如果不是管理员，只能查看自己的余额
      const finalEmployeeId = req.user.role === 'admin' ? employeeId : req.user.employeeId;
      const finalYear = year ? parseInt(year) : new Date().getFullYear();

      const result = await leaveService.getLeaveBalance(finalEmployeeId, finalYear);

      return success(res, result, '获取假期余额成功');
    } catch (error) {
      return error(res, '获取假期余额失败', 500);
    }
  }

  // 获取部门请假汇总（管理员功能）
  async getDepartmentLeaveStats(req, res) {
    try {
      const { departmentId, startDate, endDate } = req.query;

      if (req.user.role !== 'admin') {
        return error(res, '权限不足', 403);
      }

      if (!departmentId || !startDate || !endDate) {
        return error(res, '请提供部门ID、开始日期和结束日期', 400);
      }

      const result = await leaveService.getDepartmentLeaveStats(departmentId, startDate, endDate);

      return success(res, result, '获取部门请假汇总成功');
    } catch (error) {
      return error(res, '获取部门请假汇总失败', 500);
    }
  }
}

module.exports = new AttendanceController(); 