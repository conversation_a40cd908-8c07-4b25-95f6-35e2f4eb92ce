{"version": 3, "names": ["tsRewriteRelativeImportExtensions", "path", "preserveJsx", "test", "replace", "m", "tsx", "d", "ext", "cm", "toLowerCase"], "sources": ["../../src/helpers/tsRewriteRelativeImportExtensions.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n\n// https://github.com/microsoft/TypeScript/blob/71716a2868c87248af5020e33a84a2178d41a2d6/src/compiler/factory/emitHelpers.ts#L1451\nexport default function tsRewriteRelativeImportExtensions(\n  path: unknown,\n  preserveJsx?: boolean,\n) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n    return path.replace(\n      /\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+)?)\\.([cm]?)ts$/i,\n      function (m, tsx, d, ext, cm) {\n        return tsx\n          ? preserveJsx\n            ? \".jsx\"\n            : \".js\"\n          : d && (!ext || !cm)\n            ? m\n            : d + ext + \".\" + cm.toLowerCase() + \"js\";\n      },\n    );\n  }\n  return path;\n}\n"], "mappings": ";;;;;;AAGe,SAASA,iCAAiCA,CACvDC,IAAa,EACbC,WAAqB,EACrB;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,UAAU,CAACE,IAAI,CAACF,IAAI,CAAC,EAAE;IACrD,OAAOA,IAAI,CAACG,OAAO,CACjB,iDAAiD,EACjD,UAAUC,CAAC,EAAEC,GAAG,EAAEC,CAAC,EAAEC,GAAG,EAAEC,EAAE,EAAE;MAC5B,OAAOH,GAAG,GACNJ,WAAW,GACT,MAAM,GACN,KAAK,GACPK,CAAC,KAAK,CAACC,GAAG,IAAI,CAACC,EAAE,CAAC,GAChBJ,CAAC,GACDE,CAAC,GAAGC,GAAG,GAAG,GAAG,GAAGC,EAAE,CAACC,WAAW,CAAC,CAAC,GAAG,IAAI;IAC/C,CACF,CAAC;EACH;EACA,OAAOT,IAAI;AACb", "ignoreList": []}