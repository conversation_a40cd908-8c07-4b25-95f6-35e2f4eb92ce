<template>
  <div class="server-error-container">
    <div class="error-content">
      <div class="error-icon">
        <el-icon size="80" color="#f56c6c">
          <Warning />
        </el-icon>
      </div>
      
      <h1 class="error-title">服务器连接失败</h1>
      
      <p class="error-message">
        无法连接到后端服务器，请检查：
      </p>
      
      <ul class="error-list">
        <li>后端服务器是否正在运行 (端口 3001)</li>
        <li>网络连接是否正常</li>
        <li>防火墙设置是否正确</li>
      </ul>
      
      <div class="error-actions">
        <el-button type="primary" @click="retryConnection">
          <el-icon><Refresh /></el-icon>
          重试连接
        </el-button>
        
        <el-button @click="goToLogin">
          返回登录页
        </el-button>
      </div>
      
      <div class="error-details">
        <el-collapse>
          <el-collapse-item title="技术详情" name="details">
            <p><strong>错误时间:</strong> {{ errorTime }}</p>
            <p><strong>请求地址:</strong> {{ apiBaseUrl }}</p>
            <p><strong>错误信息:</strong> {{ errorMessage }}</p>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Warning, Refresh } from '@element-plus/icons-vue'
import axios from '@/utils/axios'

const router = useRouter()

const errorTime = ref(new Date().toLocaleString())
const apiBaseUrl = ref(import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api')
const errorMessage = ref('服务器连接超时')

const retryConnection = async () => {
  try {
    ElMessage.info('正在重试连接...')
    
    // 尝试连接健康检查接口
    const response = await axios.get('/health', { timeout: 5000 })
    
    if (response.data.success) {
      ElMessage.success('连接成功！')
      router.push('/login')
    }
  } catch (error) {
    ElMessage.error('连接仍然失败，请检查服务器状态')
    errorTime.value = new Date().toLocaleString()
    errorMessage.value = error.message || '连接失败'
  }
}

const goToLogin = () => {
  router.push('/login')
}

onMounted(() => {
  // 页面加载时自动尝试一次连接
  setTimeout(retryConnection, 1000)
})
</script>

<style scoped>
.server-error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-content {
  background: white;
  border-radius: 12px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-bottom: 20px;
}

.error-title {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.error-message {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.error-list {
  text-align: left;
  color: #7f8c8d;
  margin-bottom: 30px;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 8px;
}

.error-actions {
  margin-bottom: 30px;
}

.error-actions .el-button {
  margin: 0 10px;
}

.error-details {
  text-align: left;
}

.error-details p {
  margin-bottom: 8px;
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .error-content {
    padding: 30px 20px;
  }
  
  .error-title {
    font-size: 1.5rem;
  }
  
  .error-actions .el-button {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}
</style>
