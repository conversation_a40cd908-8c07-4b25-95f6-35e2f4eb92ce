{"version": 3, "names": ["_t", "require", "blockStatement", "cloneNode", "emptyStatement", "expressionStatement", "identifier", "isStatement", "isStringLiteral", "stringLiteral", "validate", "populatePlaceholders", "metadata", "replacements", "ast", "placeholders", "for<PERSON>ach", "placeholder", "hasOwnProperty", "call", "name", "placeholder<PERSON><PERSON>", "Error", "Object", "keys", "key", "placeholder<PERSON><PERSON><PERSON>", "has", "slice", "reverse", "_ref", "applyReplacement", "e", "message", "replacement", "isDuplicate", "Array", "isArray", "map", "node", "parent", "index", "resolve", "type", "undefined", "set", "value", "typeAnnotation", "optional", "decorators", "items", "splice"], "sources": ["../src/populate.ts"], "sourcesContent": ["import {\n  blockStatement,\n  cloneNode,\n  emptyStatement,\n  expressionStatement,\n  identifier,\n  isStatement,\n  isStringLiteral,\n  stringLiteral,\n  validate,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { TemplateReplacements } from \"./options.ts\";\nimport type { Metadata, Placeholder } from \"./parse.ts\";\n\nexport default function populatePlaceholders(\n  metadata: Metadata,\n  replacements: TemplateReplacements,\n): t.File {\n  const ast = cloneNode(metadata.ast);\n\n  if (replacements) {\n    metadata.placeholders.forEach(placeholder => {\n      if (!Object.hasOwn(replacements, placeholder.name)) {\n        const placeholderName = placeholder.name;\n\n        throw new Error(\n          `Error: No substitution given for \"${placeholderName}\". If this is not meant to be a\n            placeholder you may want to consider passing one of the following options to @babel/template:\n            - { placeholderPattern: false, placeholderWhitelist: new Set(['${placeholderName}'])}\n            - { placeholderPattern: /^${placeholderName}$/ }`,\n        );\n      }\n    });\n    Object.keys(replacements).forEach(key => {\n      if (!metadata.placeholderNames.has(key)) {\n        throw new Error(`Unknown substitution \"${key}\" given`);\n      }\n    });\n  }\n\n  // Process in reverse order so AST mutation doesn't change indices that\n  // will be needed for later calls to `placeholder.resolve()`.\n  metadata.placeholders\n    .slice()\n    .reverse()\n    .forEach(placeholder => {\n      try {\n        applyReplacement(\n          placeholder,\n          ast,\n          (replacements && replacements[placeholder.name]) ?? null,\n        );\n      } catch (e) {\n        e.message = `@babel/template placeholder \"${placeholder.name}\": ${e.message}`;\n        throw e;\n      }\n    });\n\n  return ast;\n}\n\nfunction applyReplacement(\n  placeholder: Placeholder,\n  ast: t.File,\n  replacement: any,\n) {\n  // Track inserted nodes and clone them if they are inserted more than\n  // once to avoid injecting the same node multiple times.\n  if (placeholder.isDuplicate) {\n    if (Array.isArray(replacement)) {\n      replacement = replacement.map(node => cloneNode(node));\n    } else if (typeof replacement === \"object\") {\n      replacement = cloneNode(replacement);\n    }\n  }\n\n  const { parent, key, index } = placeholder.resolve(ast);\n\n  if (placeholder.type === \"string\") {\n    if (typeof replacement === \"string\") {\n      replacement = stringLiteral(replacement);\n    }\n    if (!replacement || !isStringLiteral(replacement)) {\n      throw new Error(\"Expected string substitution\");\n    }\n  } else if (placeholder.type === \"statement\") {\n    if (index === undefined) {\n      if (!replacement) {\n        replacement = emptyStatement();\n      } else if (Array.isArray(replacement)) {\n        replacement = blockStatement(replacement);\n      } else if (typeof replacement === \"string\") {\n        replacement = expressionStatement(identifier(replacement));\n      } else if (!isStatement(replacement)) {\n        replacement = expressionStatement(replacement);\n      }\n    } else {\n      if (replacement && !Array.isArray(replacement)) {\n        if (typeof replacement === \"string\") {\n          replacement = identifier(replacement);\n        }\n        if (!isStatement(replacement)) {\n          replacement = expressionStatement(replacement);\n        }\n      }\n    }\n  } else if (placeholder.type === \"param\") {\n    if (typeof replacement === \"string\") {\n      replacement = identifier(replacement);\n    }\n\n    if (index === undefined) throw new Error(\"Assertion failure.\");\n  } else {\n    if (typeof replacement === \"string\") {\n      replacement = identifier(replacement);\n    }\n    if (Array.isArray(replacement)) {\n      throw new Error(\"Cannot replace single expression with an array.\");\n    }\n  }\n\n  function set(parent: any, key: any, value: any) {\n    const node = parent[key] as t.Node;\n    parent[key] = value;\n    if (node.type === \"Identifier\" || node.type === \"Placeholder\") {\n      if (node.typeAnnotation) {\n        value.typeAnnotation = node.typeAnnotation;\n      }\n      if (node.optional) {\n        value.optional = node.optional;\n      }\n      if (node.decorators) {\n        value.decorators = node.decorators;\n      }\n    }\n  }\n\n  if (index === undefined) {\n    validate(parent, key, replacement);\n\n    set(parent, key, replacement);\n  } else {\n    const items: Array<t.Node> = (parent as any)[key].slice();\n\n    if (placeholder.type === \"statement\" || placeholder.type === \"param\") {\n      if (replacement == null) {\n        items.splice(index, 1);\n      } else if (Array.isArray(replacement)) {\n        items.splice(index, 1, ...replacement);\n      } else {\n        set(items, index, replacement);\n      }\n    } else {\n      set(items, index, replacement);\n    }\n\n    validate(parent, key, items);\n    (parent as any)[key] = items;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAUsB;EATpBC,cAAc;EACdC,SAAS;EACTC,cAAc;EACdC,mBAAmB;EACnBC,UAAU;EACVC,WAAW;EACXC,eAAe;EACfC,aAAa;EACbC;AAAQ,IAAAV,EAAA;AAOK,SAASW,oBAAoBA,CAC1CC,QAAkB,EAClBC,YAAkC,EAC1B;EACR,MAAMC,GAAG,GAAGX,SAAS,CAACS,QAAQ,CAACE,GAAG,CAAC;EAEnC,IAAID,YAAY,EAAE;IAChBD,QAAQ,CAACG,YAAY,CAACC,OAAO,CAACC,WAAW,IAAI;MAC3C,IAAI,CAACC,cAAA,CAAAC,IAAA,CAAcN,YAAY,EAAEI,WAAW,CAACG,IAAI,CAAC,EAAE;QAClD,MAAMC,eAAe,GAAGJ,WAAW,CAACG,IAAI;QAExC,MAAM,IAAIE,KAAK,CACb,qCAAqCD,eAAe;AAC9D;AACA,6EAA6EA,eAAe;AAC5F,wCAAwCA,eAAe,MAC/C,CAAC;MACH;IACF,CAAC,CAAC;IACFE,MAAM,CAACC,IAAI,CAACX,YAAY,CAAC,CAACG,OAAO,CAACS,GAAG,IAAI;MACvC,IAAI,CAACb,QAAQ,CAACc,gBAAgB,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;QACvC,MAAM,IAAIH,KAAK,CAAC,yBAAyBG,GAAG,SAAS,CAAC;MACxD;IACF,CAAC,CAAC;EACJ;EAIAb,QAAQ,CAACG,YAAY,CAClBa,KAAK,CAAC,CAAC,CACPC,OAAO,CAAC,CAAC,CACTb,OAAO,CAACC,WAAW,IAAI;IACtB,IAAI;MAAA,IAAAa,IAAA;MACFC,gBAAgB,CACdd,WAAW,EACXH,GAAG,GAAAgB,IAAA,GACFjB,YAAY,IAAIA,YAAY,CAACI,WAAW,CAACG,IAAI,CAAC,YAAAU,IAAA,GAAK,IACtD,CAAC;IACH,CAAC,CAAC,OAAOE,CAAC,EAAE;MACVA,CAAC,CAACC,OAAO,GAAG,gCAAgChB,WAAW,CAACG,IAAI,MAAMY,CAAC,CAACC,OAAO,EAAE;MAC7E,MAAMD,CAAC;IACT;EACF,CAAC,CAAC;EAEJ,OAAOlB,GAAG;AACZ;AAEA,SAASiB,gBAAgBA,CACvBd,WAAwB,EACxBH,GAAW,EACXoB,WAAgB,EAChB;EAGA,IAAIjB,WAAW,CAACkB,WAAW,EAAE;IAC3B,IAAIC,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;MAC9BA,WAAW,GAAGA,WAAW,CAACI,GAAG,CAACC,IAAI,IAAIpC,SAAS,CAACoC,IAAI,CAAC,CAAC;IACxD,CAAC,MAAM,IAAI,OAAOL,WAAW,KAAK,QAAQ,EAAE;MAC1CA,WAAW,GAAG/B,SAAS,CAAC+B,WAAW,CAAC;IACtC;EACF;EAEA,MAAM;IAAEM,MAAM;IAAEf,GAAG;IAAEgB;EAAM,CAAC,GAAGxB,WAAW,CAACyB,OAAO,CAAC5B,GAAG,CAAC;EAEvD,IAAIG,WAAW,CAAC0B,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAI,OAAOT,WAAW,KAAK,QAAQ,EAAE;MACnCA,WAAW,GAAGzB,aAAa,CAACyB,WAAW,CAAC;IAC1C;IACA,IAAI,CAACA,WAAW,IAAI,CAAC1B,eAAe,CAAC0B,WAAW,CAAC,EAAE;MACjD,MAAM,IAAIZ,KAAK,CAAC,8BAA8B,CAAC;IACjD;EACF,CAAC,MAAM,IAAIL,WAAW,CAAC0B,IAAI,KAAK,WAAW,EAAE;IAC3C,IAAIF,KAAK,KAAKG,SAAS,EAAE;MACvB,IAAI,CAACV,WAAW,EAAE;QAChBA,WAAW,GAAG9B,cAAc,CAAC,CAAC;MAChC,CAAC,MAAM,IAAIgC,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QACrCA,WAAW,GAAGhC,cAAc,CAACgC,WAAW,CAAC;MAC3C,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QAC1CA,WAAW,GAAG7B,mBAAmB,CAACC,UAAU,CAAC4B,WAAW,CAAC,CAAC;MAC5D,CAAC,MAAM,IAAI,CAAC3B,WAAW,CAAC2B,WAAW,CAAC,EAAE;QACpCA,WAAW,GAAG7B,mBAAmB,CAAC6B,WAAW,CAAC;MAChD;IACF,CAAC,MAAM;MACL,IAAIA,WAAW,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QAC9C,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;UACnCA,WAAW,GAAG5B,UAAU,CAAC4B,WAAW,CAAC;QACvC;QACA,IAAI,CAAC3B,WAAW,CAAC2B,WAAW,CAAC,EAAE;UAC7BA,WAAW,GAAG7B,mBAAmB,CAAC6B,WAAW,CAAC;QAChD;MACF;IACF;EACF,CAAC,MAAM,IAAIjB,WAAW,CAAC0B,IAAI,KAAK,OAAO,EAAE;IACvC,IAAI,OAAOT,WAAW,KAAK,QAAQ,EAAE;MACnCA,WAAW,GAAG5B,UAAU,CAAC4B,WAAW,CAAC;IACvC;IAEA,IAAIO,KAAK,KAAKG,SAAS,EAAE,MAAM,IAAItB,KAAK,CAAC,oBAAoB,CAAC;EAChE,CAAC,MAAM;IACL,IAAI,OAAOY,WAAW,KAAK,QAAQ,EAAE;MACnCA,WAAW,GAAG5B,UAAU,CAAC4B,WAAW,CAAC;IACvC;IACA,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;MAC9B,MAAM,IAAIZ,KAAK,CAAC,iDAAiD,CAAC;IACpE;EACF;EAEA,SAASuB,GAAGA,CAACL,MAAW,EAAEf,GAAQ,EAAEqB,KAAU,EAAE;IAC9C,MAAMP,IAAI,GAAGC,MAAM,CAACf,GAAG,CAAW;IAClCe,MAAM,CAACf,GAAG,CAAC,GAAGqB,KAAK;IACnB,IAAIP,IAAI,CAACI,IAAI,KAAK,YAAY,IAAIJ,IAAI,CAACI,IAAI,KAAK,aAAa,EAAE;MAC7D,IAAIJ,IAAI,CAACQ,cAAc,EAAE;QACvBD,KAAK,CAACC,cAAc,GAAGR,IAAI,CAACQ,cAAc;MAC5C;MACA,IAAIR,IAAI,CAACS,QAAQ,EAAE;QACjBF,KAAK,CAACE,QAAQ,GAAGT,IAAI,CAACS,QAAQ;MAChC;MACA,IAAIT,IAAI,CAACU,UAAU,EAAE;QACnBH,KAAK,CAACG,UAAU,GAAGV,IAAI,CAACU,UAAU;MACpC;IACF;EACF;EAEA,IAAIR,KAAK,KAAKG,SAAS,EAAE;IACvBlC,QAAQ,CAAC8B,MAAM,EAAEf,GAAG,EAAES,WAAW,CAAC;IAElCW,GAAG,CAACL,MAAM,EAAEf,GAAG,EAAES,WAAW,CAAC;EAC/B,CAAC,MAAM;IACL,MAAMgB,KAAoB,GAAIV,MAAM,CAASf,GAAG,CAAC,CAACG,KAAK,CAAC,CAAC;IAEzD,IAAIX,WAAW,CAAC0B,IAAI,KAAK,WAAW,IAAI1B,WAAW,CAAC0B,IAAI,KAAK,OAAO,EAAE;MACpE,IAAIT,WAAW,IAAI,IAAI,EAAE;QACvBgB,KAAK,CAACC,MAAM,CAACV,KAAK,EAAE,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIL,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QACrCgB,KAAK,CAACC,MAAM,CAACV,KAAK,EAAE,CAAC,EAAE,GAAGP,WAAW,CAAC;MACxC,CAAC,MAAM;QACLW,GAAG,CAACK,KAAK,EAAET,KAAK,EAAEP,WAAW,CAAC;MAChC;IACF,CAAC,MAAM;MACLW,GAAG,CAACK,KAAK,EAAET,KAAK,EAAEP,WAAW,CAAC;IAChC;IAEAxB,QAAQ,CAAC8B,MAAM,EAAEf,GAAG,EAAEyB,KAAK,CAAC;IAC3BV,MAAM,CAASf,GAAG,CAAC,GAAGyB,KAAK;EAC9B;AACF", "ignoreList": []}