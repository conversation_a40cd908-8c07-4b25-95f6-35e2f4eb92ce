const { body, param, query } = require('express-validator');

const employeeValidators = {
  // 创建员工验证
  create: [
    body('userData.user_id')
      .notEmpty()
      .withMessage('关联用户ID不能为空')
      .isInt({ min: 1 })
      .withMessage('用户ID必须是正整数'),

    body('department_id')
      .notEmpty()
      .withMessage('部门ID不能为空')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是正整数'),

    body('position_id')
      .notEmpty()
      .withMessage('职位ID不能为空')
      .isInt({ min: 1 })
      .withMessage('职位ID必须是正整数'),

    body('hire_date')
      .notEmpty()
      .withMessage('入职日期不能为空')
      .isDate()
      .withMessage('入职日期格式不正确'),

    body('manager_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('直属上级ID必须是正整数'),

    body('salary')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('薪资必须是非负数'),

    body('work_location')
      .optional()
      .isLength({ max: 100 })
      .withMessage('工作地点不能超过100个字符'),

    body('emergency_contact')
      .optional()
      .isLength({ max: 50 })
      .withMessage('紧急联系人姓名不能超过50个字符'),

    body('emergency_phone')
      .optional()
      .isMobilePhone('zh-CN')
      .withMessage('紧急联系电话格式不正确'),

    body('notes')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('备注信息不能超过1000个字符')
  ],

  // 更新员工验证
  update: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('员工ID必须是正整数'),

    body('department_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('部门ID必须是正整数'),

    body('position_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('职位ID必须是正整数'),

    body('manager_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('直属上级ID必须是正整数'),

    body('hire_date')
      .optional()
      .isDate()
      .withMessage('入职日期格式不正确'),

    body('status')
      .optional()
      .isIn(['active', 'inactive', 'resigned'])
      .withMessage('员工状态必须是: active, inactive, resigned 之一'),

    body('salary')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('薪资必须是非负数'),

    body('work_location')
      .optional()
      .isLength({ max: 100 })
      .withMessage('工作地点不能超过100个字符'),

    body('emergency_contact')
      .optional()
      .isLength({ max: 50 })
      .withMessage('紧急联系人姓名不能超过50个字符'),

    body('emergency_phone')
      .optional()
      .isMobilePhone('zh-CN')
      .withMessage('紧急联系电话格式不正确'),

    body('notes')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('备注信息不能超过1000个字符'),

    // 用户信息更新验证
    body('userData.real_name')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('真实姓名长度必须在2-50个字符之间'),

    body('userData.phone')
      .optional()
      .isMobilePhone('zh-CN')
      .withMessage('手机号格式不正确'),

    body('userData.email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确')
  ],

  // 删除员工验证
  delete: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('员工ID必须是正整数')
  ],

  // 根据ID获取员工验证
  getById: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('员工ID必须是正整数')
  ],

  // 获取部门员工验证
  getDepartmentEmployees: [
    param('departmentId')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是正整数')
  ],

  // 转移员工验证
  transfer: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('员工ID必须是正整数'),

    body('newDepartmentId')
      .notEmpty()
      .withMessage('新部门ID不能为空')
      .isInt({ min: 1 })
      .withMessage('新部门ID必须是正整数'),

    body('newPositionId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('新职位ID必须是正整数')
  ],

  // 批量更新状态验证
  batchUpdateStatus: [
    body('employeeIds')
      .isArray({ min: 1 })
      .withMessage('员工ID列表不能为空'),

    body('employeeIds.*')
      .isInt({ min: 1 })
      .withMessage('员工ID必须是正整数'),

    body('status')
      .notEmpty()
      .withMessage('状态不能为空')
      .isIn(['active', 'inactive', 'resigned'])
      .withMessage('状态必须是: active, inactive, resigned 之一')
  ],

  // 查询参数验证
  list: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),

    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),

    query('departmentId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('部门ID必须是正整数'),

    query('positionId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('职位ID必须是正整数'),

    query('status')
      .optional()
      .isIn(['active', 'inactive', 'resigned'])
      .withMessage('状态必须是: active, inactive, resigned 之一'),

    query('keyword')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('关键词长度必须在1-50个字符之间'),

    query('sortBy')
      .optional()
      .isIn(['created_at', 'updated_at', 'hire_date', 'employee_no'])
      .withMessage('排序字段不正确'),

    query('sortOrder')
      .optional()
      .isIn(['ASC', 'DESC'])
      .withMessage('排序方向必须是ASC或DESC')
  ]
};

module.exports = { employeeValidators }; 