const Minio = require('minio');
const logger = require('../shared/logger');

// MinIO配置
const minioConfig = {
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT) || 9000,
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin'
};

// 创建MinIO客户端
const minioClient = new Minio.Client(minioConfig);

// 默认存储桶名称
const DEFAULT_BUCKET = process.env.MINIO_BUCKET || 'smart-office';

// 初始化MinIO服务
const initializeMinio = async () => {
  try {
    // 检查存储桶是否存在
    const bucketExists = await minioClient.bucketExists(DEFAULT_BUCKET);
    
    if (!bucketExists) {
      // 创建存储桶
      await minioClient.makeBucket(DEFAULT_BUCKET, 'us-east-1');
      logger.info(`MinIO存储桶 ${DEFAULT_BUCKET} 已创建`);
      
      // 设置存储桶策略（公共读取）
      const policy = {
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetBucketLocation', 's3:ListBucket'],
            Resource: [`arn:aws:s3:::${DEFAULT_BUCKET}`]
          },
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${DEFAULT_BUCKET}/public/*`]
          }
        ]
      };
      
      await minioClient.setBucketPolicy(DEFAULT_BUCKET, JSON.stringify(policy));
      logger.info(`MinIO存储桶策略已设置`);
    } else {
      logger.info(`MinIO存储桶 ${DEFAULT_BUCKET} 已存在`);
    }
    
    logger.info('MinIO服务初始化成功');
    return true;
  } catch (error) {
    logger.error('MinIO服务初始化失败:', error);
    throw error;
  }
};

// 生成对象名称（包含路径）
const generateObjectName = (userId, originalName, folder = 'files') => {
  const timestamp = Date.now();
  const extension = originalName.split('.').pop();
  const baseName = originalName.split('.').slice(0, -1).join('.');
  const sanitizedName = baseName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_');
  
  return `${folder}/${userId}/${timestamp}_${sanitizedName}.${extension}`;
};

// 获取文件预签名URL
const getPresignedUrl = async (objectName, expiry = 7 * 24 * 60 * 60) => {
  try {
    return await minioClient.presignedGetObject(DEFAULT_BUCKET, objectName, expiry);
  } catch (error) {
    logger.error('生成预签名URL失败:', error);
    throw error;
  }
};

// 获取文件上传预签名URL
const getPresignedUploadUrl = async (objectName, expiry = 15 * 60) => {
  try {
    return await minioClient.presignedPutObject(DEFAULT_BUCKET, objectName, expiry);
  } catch (error) {
    logger.error('生成上传预签名URL失败:', error);
    throw error;
  }
};

// 检查文件是否存在
const fileExists = async (objectName) => {
  try {
    await minioClient.statObject(DEFAULT_BUCKET, objectName);
    return true;
  } catch (error) {
    return false;
  }
};

// 删除文件
const deleteFile = async (objectName) => {
  try {
    await minioClient.removeObject(DEFAULT_BUCKET, objectName);
    return true;
  } catch (error) {
    logger.error('删除文件失败:', error);
    throw error;
  }
};

// 复制文件（用于版本管理）
const copyFile = async (sourceObjectName, destObjectName) => {
  try {
    await minioClient.copyObject(
      DEFAULT_BUCKET,
      destObjectName,
      `/${DEFAULT_BUCKET}/${sourceObjectName}`
    );
    return true;
  } catch (error) {
    logger.error('复制文件失败:', error);
    throw error;
  }
};

// 获取文件信息
const getFileInfo = async (objectName) => {
  try {
    return await minioClient.statObject(DEFAULT_BUCKET, objectName);
  } catch (error) {
    logger.error('获取文件信息失败:', error);
    throw error;
  }
};

// 列出文件
const listFiles = async (prefix = '', recursive = false) => {
  try {
    const objectsList = [];
    const objectsStream = minioClient.listObjects(DEFAULT_BUCKET, prefix, recursive);
    
    return new Promise((resolve, reject) => {
      objectsStream.on('data', (obj) => {
        objectsList.push(obj);
      });
      
      objectsStream.on('error', (err) => {
        reject(err);
      });
      
      objectsStream.on('end', () => {
        resolve(objectsList);
      });
    });
  } catch (error) {
    logger.error('列出文件失败:', error);
    throw error;
  }
};

module.exports = {
  minioClient,
  DEFAULT_BUCKET,
  initializeMinio,
  generateObjectName,
  getPresignedUrl,
  getPresignedUploadUrl,
  fileExists,
  deleteFile,
  copyFile,
  getFileInfo,
  listFiles
}; 