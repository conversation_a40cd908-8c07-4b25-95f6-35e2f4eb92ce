import { defineStore } from 'pinia';
import { fileAPI } from '@/api/file';

export const useFileStore = defineStore('file', {
  state: () => ({
    // 文件列表相关
    files: [],
    sharedFiles: [],
    currentFile: null,
    fileStats: {
      storage: {
        fileCount: 0,
        totalSize: 0,
        avgFileSize: 0
      },
      fileTypes: {},
      shares: {
        totalShares: 0,
        activeShares: 0,
        publicShares: 0,
        totalAccessCount: 0
      }
    },

    // 分页信息
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0
    },

    // 筛选和搜索
    filters: {
      folder: '',
      fileType: '',
      keyword: '',
      sortBy: 'created_at',
      sortOrder: 'DESC'
    },

    // 分享相关
    shares: [],
    currentShare: null,
    shareStats: {
      totalShares: 0,
      activeShares: 0,
      publicShares: 0,
      totalAccessCount: 0
    },

    // 上传相关
    uploadProgress: {},
    uploadQueue: [],

    // 选中的文件
    selectedFiles: [],

    // 当前文件夹路径
    currentFolder: '/',
    folderHistory: ['/'],

    // 加载状态
    loading: {
      files: false,
      upload: false,
      download: false,
      share: false,
      delete: false
    },

    // 预览相关
    previewFile: null,
    previewVisible: false
  }),

  getters: {
    // 获取当前文件夹的文件
    currentFolderFiles: (state) => {
      return state.files.filter(file => 
        file.folder_path === state.currentFolder
      );
    },

    // 获取文件类型统计
    fileTypeStats: (state) => {
      const stats = {};
      state.files.forEach(file => {
        if (!stats[file.file_type]) {
          stats[file.file_type] = { count: 0, size: 0 };
        }
        stats[file.file_type].count++;
        stats[file.file_type].size += file.file_size;
      });
      return stats;
    },

    // 获取选中文件的总大小
    selectedFilesSize: (state) => {
      return state.selectedFiles.reduce((total, fileId) => {
        const file = state.files.find(f => f.id === fileId);
        return total + (file ? file.file_size : 0);
      }, 0);
    },

    // 检查是否有上传中的文件
    hasUploading: (state) => {
      return Object.values(state.uploadProgress).some(progress => 
        progress.status === 'uploading'
      );
    },

    // 格式化文件大小
    formatFileSize: () => (size) => {
      if (size === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(size) / Math.log(k));
      return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  },

  actions: {
    // 获取文件列表
    async fetchFiles(options = {}) {
      this.loading.files = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.filters,
          ...options
        };

        const response = await fileAPI.getUserFiles(params);
        
        this.files = response.data.files;
        this.pagination = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('获取文件列表失败:', error);
        throw error;
      } finally {
        this.loading.files = false;
      }
    },

    // 获取共享文件列表
    async fetchSharedFiles(options = {}) {
      this.loading.files = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...options
        };

        const response = await fileAPI.getSharedFiles(params);
        
        this.sharedFiles = response.data.files;
        this.pagination = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('获取共享文件列表失败:', error);
        throw error;
      } finally {
        this.loading.files = false;
      }
    },

    // 上传文件
    async uploadFile(file, options = {}) {
      const fileId = Date.now() + '_' + file.name;
      
      // 初始化上传进度
      this.uploadProgress[fileId] = {
        file: file,
        progress: 0,
        status: 'uploading',
        error: null
      };

      try {
        const formData = new FormData();
        formData.append('file', file);
        
        if (options.folderPath) {
          formData.append('folderPath', options.folderPath);
        }
        if (options.description) {
          formData.append('description', options.description);
        }
        if (options.isPublic !== undefined) {
          formData.append('isPublic', options.isPublic);
        }
        if (options.tags) {
          formData.append('tags', JSON.stringify(options.tags));
        }

        const response = await fileAPI.uploadFile(formData, {
          onUploadProgress: (progressEvent) => {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            this.uploadProgress[fileId].progress = progress;
          }
        });

        // 上传成功
        this.uploadProgress[fileId].status = 'success';
        
        // 添加到文件列表
        this.files.unshift(response.data);
        
        // 清理上传进度（延迟清理以显示成功状态）
        setTimeout(() => {
          delete this.uploadProgress[fileId];
        }, 2000);

        return response.data;
      } catch (error) {
        this.uploadProgress[fileId].status = 'error';
        this.uploadProgress[fileId].error = error.message;
        
        console.error('文件上传失败:', error);
        throw error;
      }
    },

    // 批量上传文件
    async uploadFiles(files, options = {}) {
      const uploadPromises = Array.from(files).map(file => 
        this.uploadFile(file, options)
      );

      try {
        const results = await Promise.allSettled(uploadPromises);
        
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        return {
          successful,
          failed,
          total: files.length,
          results
        };
      } catch (error) {
        console.error('批量上传失败:', error);
        throw error;
      }
    },

    // 获取文件详情
    async fetchFileById(fileId) {
      try {
        const response = await fileAPI.getFileById(fileId);
        this.currentFile = response.data;
        return response.data;
      } catch (error) {
        console.error('获取文件详情失败:', error);
        throw error;
      }
    },

    // 获取文件下载URL
    async getDownloadUrl(fileId, expiresIn = 3600) {
      this.loading.download = true;
      try {
        const response = await fileAPI.getDownloadUrl(fileId, { expiresIn });
        return response.data;
      } catch (error) {
        console.error('获取下载链接失败:', error);
        throw error;
      } finally {
        this.loading.download = false;
      }
    },

    // 下载文件
    async downloadFile(fileId) {
      try {
        const downloadData = await this.getDownloadUrl(fileId);
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = downloadData.downloadUrl;
        link.download = downloadData.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return true;
      } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
      }
    },

    // 删除文件
    async deleteFile(fileId) {
      this.loading.delete = true;
      try {
        await fileAPI.deleteFile(fileId);
        
        // 从列表中移除
        this.files = this.files.filter(file => file.id !== fileId);
        
        // 如果是当前文件，清空
        if (this.currentFile && this.currentFile.id === fileId) {
          this.currentFile = null;
        }

        return true;
      } catch (error) {
        console.error('删除文件失败:', error);
        throw error;
      } finally {
        this.loading.delete = false;
      }
    },

    // 批量删除文件
    async batchDeleteFiles(fileIds) {
      this.loading.delete = true;
      try {
        const response = await fileAPI.batchDeleteFiles({ fileIds });
        
        // 从列表中移除成功删除的文件
        const successfulIds = response.data.results
          .filter(r => r.success)
          .map(r => r.fileId);
        
        this.files = this.files.filter(file => 
          !successfulIds.includes(file.id)
        );

        // 清空选中状态
        this.selectedFiles = [];

        return response.data;
      } catch (error) {
        console.error('批量删除文件失败:', error);
        throw error;
      } finally {
        this.loading.delete = false;
      }
    },

    // 创建文件分享
    async createFileShare(fileId, shareData) {
      this.loading.share = true;
      try {
        const response = await fileAPI.createFileShare(fileId, shareData);
        
        // 添加到分享列表
        this.shares.unshift(response.data);

        return response.data;
      } catch (error) {
        console.error('创建文件分享失败:', error);
        throw error;
      } finally {
        this.loading.share = false;
      }
    },

    // 获取用户分享记录
    async fetchUserShares(options = {}) {
      try {
        const response = await fileAPI.getUserShares(options);
        this.shares = response.data.shares;
        return response.data;
      } catch (error) {
        console.error('获取分享记录失败:', error);
        throw error;
      }
    },

    // 停用分享
    async deactivateShare(shareId) {
      try {
        await fileAPI.deactivateShare(shareId);
        
        // 从列表中移除或更新状态
        this.shares = this.shares.filter(share => share.id !== shareId);

        return true;
      } catch (error) {
        console.error('停用分享失败:', error);
        throw error;
      }
    },

    // 更新文件信息
    async updateFile(fileId, updateData) {
      try {
        const response = await fileAPI.updateFile(fileId, updateData);
        
        // 更新列表中的文件
        const index = this.files.findIndex(file => file.id === fileId);
        if (index !== -1) {
          this.files[index] = response.data;
        }

        // 更新当前文件
        if (this.currentFile && this.currentFile.id === fileId) {
          this.currentFile = response.data;
        }

        return response.data;
      } catch (error) {
        console.error('更新文件信息失败:', error);
        throw error;
      }
    },

    // 移动文件
    async moveFiles(fileIds, targetFolder) {
      try {
        const response = await fileAPI.moveFiles({ fileIds, targetFolder });
        
        // 更新文件的文件夹路径
        const successfulIds = response.data.results
          .filter(r => r.success)
          .map(r => r.fileId);
        
        this.files.forEach(file => {
          if (successfulIds.includes(file.id)) {
            file.folder_path = targetFolder;
          }
        });

        return response.data;
      } catch (error) {
        console.error('移动文件失败:', error);
        throw error;
      }
    },

    // 搜索文件
    async searchFiles(keyword, options = {}) {
      this.loading.files = true;
      try {
        const params = {
          keyword,
          page: 1,
          pageSize: this.pagination.pageSize,
          ...options
        };

        const response = await fileAPI.searchFiles(params);
        
        this.files = response.data.files;
        this.pagination = {
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
          totalPages: response.data.totalPages
        };

        return response.data;
      } catch (error) {
        console.error('搜索文件失败:', error);
        throw error;
      } finally {
        this.loading.files = false;
      }
    },

    // 获取文件统计信息
    async fetchFileStats() {
      try {
        const response = await fileAPI.getFileStats();
        this.fileStats = response.data;
        return response.data;
      } catch (error) {
        console.error('获取文件统计信息失败:', error);
        throw error;
      }
    },

    // 获取文件预览信息
    async getFilePreview(fileId) {
      try {
        const response = await fileAPI.getFilePreview(fileId);
        this.previewFile = response.data;
        return response.data;
      } catch (error) {
        console.error('获取文件预览信息失败:', error);
        throw error;
      }
    },

    // 设置筛选条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters };
    },

    // 设置当前文件夹
    setCurrentFolder(folder) {
      this.currentFolder = folder;
      
      // 更新文件夹历史
      if (!this.folderHistory.includes(folder)) {
        this.folderHistory.push(folder);
      }
    },

    // 选择/取消选择文件
    toggleFileSelection(fileId) {
      const index = this.selectedFiles.indexOf(fileId);
      if (index === -1) {
        this.selectedFiles.push(fileId);
      } else {
        this.selectedFiles.splice(index, 1);
      }
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (this.selectedFiles.length === this.currentFolderFiles.length) {
        this.selectedFiles = [];
      } else {
        this.selectedFiles = this.currentFolderFiles.map(file => file.id);
      }
    },

    // 清空选择
    clearSelection() {
      this.selectedFiles = [];
    },

    // 显示预览
    showPreview(file) {
      this.previewFile = file;
      this.previewVisible = true;
    },

    // 隐藏预览
    hidePreview() {
      this.previewVisible = false;
      this.previewFile = null;
    },

    // 重置状态
    resetState() {
      this.files = [];
      this.sharedFiles = [];
      this.currentFile = null;
      this.shares = [];
      this.selectedFiles = [];
      this.currentFolder = '/';
      this.folderHistory = ['/'];
      this.uploadProgress = {};
      this.previewFile = null;
      this.previewVisible = false;
    }
  }
}); 