'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('attendance_records', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      employee_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      attendance_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '考勤日期'
      },
      check_in_time: {
        type: Sequelize.TIME,
        allowNull: true,
        comment: '签到时间'
      },
      check_out_time: {
        type: Sequelize.TIME,
        allowNull: true,
        comment: '签退时间'
      },
      work_hours: {
        type: Sequelize.DECIMAL(4, 2),
        allowNull: true,
        comment: '实际工作小时数'
      },
      overtime_hours: {
        type: Sequelize.DECIMAL(4, 2),
        allowNull: true,
        defaultValue: 0,
        comment: '加班小时数'
      },
      status: {
        type: Sequelize.ENUM('normal', 'late', 'early_leave', 'absent', 'leave', 'business_trip', 'overtime'),
        allowNull: false,
        defaultValue: 'normal'
      },
      location: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '考勤地点'
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true,
        comment: '考勤时的IP地址'
      },
      device_info: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '设备信息JSON'
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '备注'
      },
      approved_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '审批人ID'
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '审批时间'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加复合唯一索引，确保员工每天只有一条考勤记录
    await queryInterface.addIndex('attendance_records', ['employee_id', 'attendance_date'], {
      unique: true,
      name: 'unique_employee_date'
    });
    
    // 添加其他索引
    await queryInterface.addIndex('attendance_records', ['employee_id']);
    await queryInterface.addIndex('attendance_records', ['attendance_date']);
    await queryInterface.addIndex('attendance_records', ['status']);
    await queryInterface.addIndex('attendance_records', ['approved_by']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('attendance_records');
  }
}; 