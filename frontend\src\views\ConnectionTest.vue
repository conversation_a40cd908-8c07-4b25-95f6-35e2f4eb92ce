<template>
  <div class="connection-test">
    <el-card>
      <template #header>
        <h2>前后端连接测试</h2>
      </template>
      
      <div class="test-section">
        <h3>服务器状态</h3>
        <div class="status-grid">
          <div class="status-item">
            <el-tag :type="frontendStatus ? 'success' : 'danger'">
              前端服务器: {{ frontendStatus ? '运行中' : '离线' }}
            </el-tag>
          </div>
          <div class="status-item">
            <el-tag :type="backendStatus ? 'success' : 'danger'">
              后端服务器: {{ backendStatus ? '运行中' : '离线' }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>API连接测试</h3>
        <div class="test-buttons">
          <el-button 
            type="primary" 
            @click="testHealthAPI" 
            :loading="testing.health"
          >
            测试健康检查
          </el-button>
          <el-button 
            type="success" 
            @click="testLoginAPI" 
            :loading="testing.login"
          >
            测试登录API
          </el-button>
          <el-button 
            type="warning" 
            @click="testEmployeeAPI" 
            :loading="testing.employee"
          >
            测试员工API
          </el-button>
        </div>
      </div>

      <div class="test-section" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <div class="results-list">
          <div 
            v-for="result in testResults" 
            :key="result.id" 
            class="result-item"
          >
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.api }}
            </el-tag>
            <span class="result-message">{{ result.message }}</span>
            <span class="result-time">{{ result.time }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>环境信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端地址">
            {{ window.location.origin }}
          </el-descriptions-item>
          <el-descriptions-item label="API地址">
            {{ import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api' }}
          </el-descriptions-item>
          <el-descriptions-item label="环境">
            {{ import.meta.env.MODE }}
          </el-descriptions-item>
          <el-descriptions-item label="时间">
            {{ new Date().toLocaleString() }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const frontendStatus = ref(true) // 前端肯定是运行的，否则看不到这个页面
const backendStatus = ref(false)
const testing = ref({
  health: false,
  login: false,
  employee: false
})
const testResults = ref([])

// 添加测试结果
const addResult = (api, success, message) => {
  testResults.value.unshift({
    id: Date.now(),
    api,
    success,
    message,
    time: new Date().toLocaleTimeString()
  })
  
  // 只保留最近10条结果
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 测试健康检查API
const testHealthAPI = async () => {
  testing.value.health = true
  try {
    const response = await fetch('http://localhost:3001/health')
    const data = await response.json()
    
    if (response.ok) {
      backendStatus.value = true
      addResult('健康检查', true, `状态: ${data.status}`)
      ElMessage.success('健康检查API连接成功')
    } else {
      addResult('健康检查', false, '请求失败')
      ElMessage.error('健康检查API连接失败')
    }
  } catch (error) {
    backendStatus.value = false
    addResult('健康检查', false, `错误: ${error.message}`)
    ElMessage.error('健康检查API连接失败: ' + error.message)
  } finally {
    testing.value.health = false
  }
}

// 测试登录API
const testLoginAPI = async () => {
  testing.value.login = true
  try {
    const response = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'test',
        password: 'test123'
      })
    })
    const data = await response.json()
    
    if (response.ok && data.success) {
      addResult('登录API', true, `用户: ${data.data.user.username}`)
      ElMessage.success('登录API连接成功')
    } else {
      addResult('登录API', false, data.error?.message || '登录失败')
      ElMessage.error('登录API测试失败')
    }
  } catch (error) {
    addResult('登录API', false, `错误: ${error.message}`)
    ElMessage.error('登录API连接失败: ' + error.message)
  } finally {
    testing.value.login = false
  }
}

// 测试员工API
const testEmployeeAPI = async () => {
  testing.value.employee = true
  try {
    const response = await fetch('http://localhost:3001/api/v1/employees')
    const data = await response.json()
    
    if (response.ok && data.success) {
      addResult('员工API', true, `获取到 ${data.employees?.length || 0} 条员工数据`)
      ElMessage.success('员工API连接成功')
    } else {
      addResult('员工API', false, data.error?.message || '请求失败')
      ElMessage.error('员工API测试失败')
    }
  } catch (error) {
    addResult('员工API', false, `错误: ${error.message}`)
    ElMessage.error('员工API连接失败: ' + error.message)
  } finally {
    testing.value.employee = false
  }
}

// 页面加载时自动测试健康检查
onMounted(() => {
  testHealthAPI()
})
</script>

<style scoped>
.connection-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin-bottom: 16px;
  color: #303133;
}

.status-grid {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.status-item {
  flex: 1;
  min-width: 200px;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-message {
  flex: 1;
  color: #606266;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
  }
  
  .status-grid {
    flex-direction: column;
  }
}
</style>
