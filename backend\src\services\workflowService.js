const { WorkflowDefinition, WorkflowNode, WorkflowInstance, ApprovalTask, Employee, User } = require('../models');
const { ValidationError } = require('../shared/errors');
const logger = require('../shared/logger');

class WorkflowService {
  // 获取工作流定义列表
  async getWorkflowDefinitions(options = {}) {
    try {
      return await WorkflowDefinition.getDefinitionList(options);
    } catch (error) {
      logger.error('获取工作流定义列表失败:', error);
      throw error;
    }
  }

  // 获取工作流定义详情
  async getWorkflowDefinitionById(definitionId) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId, {
        include: [
          {
            model: WorkflowNode,
            as: 'nodes',
            order: [['order', 'ASC']]
          },
          {
            model: Employee,
            as: 'creator',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ]
      });

      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      return definition;
    } catch (error) {
      logger.error('获取工作流定义详情失败:', error);
      throw error;
    }
  }

  // 创建工作流定义
  async createWorkflowDefinition(data, createdBy) {
    try {
      const { name, description, category, form_config, nodes } = data;

      // 创建工作流定义
      const definition = await WorkflowDefinition.create({
        name,
        description,
        category,
        form_config,
        created_by: createdBy,
        status: 'draft'
      });

      // 创建节点
      if (nodes && nodes.length > 0) {
        for (const nodeData of nodes) {
          await WorkflowNode.create({
            workflow_definition_id: definition.id,
            ...nodeData
          });
        }
      } else {
        // 创建默认节点
        await WorkflowNode.createDefaultApprovalFlow(definition.id);
      }

      return await this.getWorkflowDefinitionById(definition.id);
    } catch (error) {
      logger.error('创建工作流定义失败:', error);
      throw error;
    }
  }

  // 更新工作流定义
  async updateWorkflowDefinition(definitionId, data, updatedBy) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId);
      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      if (definition.status === 'active') {
        throw new ValidationError('活跃状态的工作流定义不能修改');
      }

      const { name, description, category, form_config, nodes } = data;

      // 更新基本信息
      await definition.update({
        name: name || definition.name,
        description: description || definition.description,
        category: category || definition.category,
        form_config: form_config || definition.form_config
      });

      // 更新节点（如果提供）
      if (nodes && nodes.length > 0) {
        // 删除现有节点
        await WorkflowNode.destroy({
          where: { workflow_definition_id: definitionId }
        });

        // 创建新节点
        for (const nodeData of nodes) {
          await WorkflowNode.create({
            workflow_definition_id: definitionId,
            ...nodeData
          });
        }
      }

      return await this.getWorkflowDefinitionById(definitionId);
    } catch (error) {
      logger.error('更新工作流定义失败:', error);
      throw error;
    }
  }

  // 激活工作流定义
  async activateWorkflowDefinition(definitionId, activatedBy) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId);
      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      await definition.activate();
      
      logger.info(`工作流定义 ${definitionId} 已被用户 ${activatedBy} 激活`);
      return definition;
    } catch (error) {
      logger.error('激活工作流定义失败:', error);
      throw error;
    }
  }

  // 停用工作流定义
  async deactivateWorkflowDefinition(definitionId, deactivatedBy) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId);
      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      await definition.deactivate();
      
      logger.info(`工作流定义 ${definitionId} 已被用户 ${deactivatedBy} 停用`);
      return definition;
    } catch (error) {
      logger.error('停用工作流定义失败:', error);
      throw error;
    }
  }

  // 启动工作流实例
  async startWorkflowInstance(workflowDefinitionId, initiatorId, data) {
    try {
      const instance = await WorkflowInstance.startInstance(
        workflowDefinitionId,
        initiatorId,
        data
      );

      logger.info(`工作流实例 ${instance.id} 已由用户 ${initiatorId} 启动`);
      return instance;
    } catch (error) {
      logger.error('启动工作流实例失败:', error);
      throw error;
    }
  }

  // 获取工作流实例列表
  async getWorkflowInstances(options = {}) {
    try {
      return await WorkflowInstance.getInstanceList(options);
    } catch (error) {
      logger.error('获取工作流实例列表失败:', error);
      throw error;
    }
  }

  // 获取工作流实例详情
  async getWorkflowInstanceById(instanceId) {
    try {
      const instance = await WorkflowInstance.findByPk(instanceId, {
        include: [
          {
            model: WorkflowDefinition,
            as: 'definition'
          },
          {
            model: Employee,
            as: 'initiator',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: WorkflowNode,
            as: 'currentNode'
          }
        ]
      });

      if (!instance) {
        throw new ValidationError('工作流实例不存在');
      }

      // 获取审批历史
      const approvalHistory = await instance.getApprovalHistory();
      
      // 获取当前待审批任务
      const pendingTasks = await instance.getPendingTasks();

      // 获取流程进度
      const progress = await instance.getProgress();

      return {
        ...instance.toJSON(),
        approvalHistory,
        pendingTasks,
        progress
      };
    } catch (error) {
      logger.error('获取工作流实例详情失败:', error);
      throw error;
    }
  }

  // 取消工作流实例
  async cancelWorkflowInstance(instanceId, reason, cancelledBy) {
    try {
      const instance = await WorkflowInstance.findByPk(instanceId);
      if (!instance) {
        throw new ValidationError('工作流实例不存在');
      }

      if (!instance.canBeOperatedBy(cancelledBy).includes('cancel')) {
        throw new ValidationError('无权限取消此工作流实例');
      }

      await instance.cancel(reason, cancelledBy);
      
      logger.info(`工作流实例 ${instanceId} 已被用户 ${cancelledBy} 取消`);
      return instance;
    } catch (error) {
      logger.error('取消工作流实例失败:', error);
      throw error;
    }
  }

  // 获取用户的待审批任务
  async getPendingApprovalTasks(assigneeId, options = {}) {
    try {
      return await ApprovalTask.getPendingTasks(assigneeId, options);
    } catch (error) {
      logger.error('获取待审批任务失败:', error);
      throw error;
    }
  }

  // 获取审批任务详情
  async getApprovalTaskById(taskId) {
    try {
      const task = await ApprovalTask.findByPk(taskId, {
        include: [
          {
            model: WorkflowInstance,
            as: 'instance',
            include: [
              {
                model: WorkflowDefinition,
                as: 'definition'
              },
              {
                model: Employee,
                as: 'initiator',
                include: [{
                  model: User,
                  as: 'user',
                  attributes: ['real_name']
                }]
              }
            ]
          },
          {
            model: WorkflowNode,
            as: 'node'
          },
          {
            model: Employee,
            as: 'assignee',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ]
      });

      if (!task) {
        throw new ValidationError('审批任务不存在');
      }

      return task;
    } catch (error) {
      logger.error('获取审批任务详情失败:', error);
      throw error;
    }
  }

  // 审批任务
  async approveTask(taskId, processedBy, action, comment = '', attachments = []) {
    try {
      const task = await this.getApprovalTaskById(taskId);

      if (task.assignee_id !== processedBy) {
        throw new ValidationError('无权限处理此审批任务');
      }

      if (task.status !== 'pending') {
        throw new ValidationError('任务状态不允许处理');
      }

      let result;
      if (action === 'approve') {
        result = await task.approve(processedBy, comment, attachments);
      } else if (action === 'reject') {
        result = await task.reject(processedBy, comment, attachments);
      } else {
        throw new ValidationError('无效的审批操作');
      }

      logger.info(`审批任务 ${taskId} 已被用户 ${processedBy} ${action === 'approve' ? '通过' : '拒绝'}`);
      return result;
    } catch (error) {
      logger.error('处理审批任务失败:', error);
      throw error;
    }
  }

  // 获取工作流统计数据
  async getWorkflowStatistics(options = {}) {
    try {
      const { userId, startDate, endDate } = options;

      // 获取工作流实例统计
      const instanceStats = await WorkflowInstance.getStatistics({
        userId,
        startDate,
        endDate
      });

      // 获取审批任务统计
      let approvalStats = {};
      if (userId) {
        approvalStats = await ApprovalTask.getApprovalStatistics(userId, {
          startDate,
          endDate
        });
      }

      return {
        instances: instanceStats,
        approvals: approvalStats
      };
    } catch (error) {
      logger.error('获取工作流统计数据失败:', error);
      throw error;
    }
  }

  // 获取活跃的工作流定义（用于发起流程）
  async getActiveWorkflowDefinitions(category = null) {
    try {
      return await WorkflowDefinition.getActiveDefinitions(category);
    } catch (error) {
      logger.error('获取活跃工作流定义失败:', error);
      throw error;
    }
  }

  // 复制工作流定义
  async cloneWorkflowDefinition(definitionId, newName, createdBy) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId);
      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      const newDefinition = await definition.clone(newName, createdBy);
      
      logger.info(`工作流定义 ${definitionId} 已被用户 ${createdBy} 复制为 ${newDefinition.id}`);
      return await this.getWorkflowDefinitionById(newDefinition.id);
    } catch (error) {
      logger.error('复制工作流定义失败:', error);
      throw error;
    }
  }

  // 获取工作流使用统计
  async getWorkflowUsageStats(definitionId, startDate, endDate) {
    try {
      return await WorkflowDefinition.getUsageStats(definitionId, startDate, endDate);
    } catch (error) {
      logger.error('获取工作流使用统计失败:', error);
      throw error;
    }
  }

  // 验证工作流定义
  async validateWorkflowDefinition(definitionId) {
    try {
      const definition = await WorkflowDefinition.findByPk(definitionId);
      if (!definition) {
        throw new ValidationError('工作流定义不存在');
      }

      await definition.validate();
      return { valid: true, message: '工作流定义验证通过' };
    } catch (error) {
      logger.error('验证工作流定义失败:', error);
      return { valid: false, message: error.message };
    }
  }

  // 与请假申请集成
  async integrateWithLeaveRequest(leaveRequestId, employeeId) {
    try {
      // 获取请假工作流定义
      const definitions = await this.getActiveWorkflowDefinitions('leave');
      if (definitions.length === 0) {
        throw new ValidationError('请假审批工作流未配置');
      }

      const workflowDefinition = definitions[0];

      // 启动工作流实例
      const instance = await this.startWorkflowInstance(
        workflowDefinition.id,
        employeeId,
        {
          title: `请假申请审批 - ${leaveRequestId}`,
          business_key: `leave_request_${leaveRequestId}`,
          form_data: {
            leave_request_id: leaveRequestId,
            type: 'leave_request'
          }
        }
      );

      return instance;
    } catch (error) {
      logger.error('集成请假申请工作流失败:', error);
      throw error;
    }
  }
}

module.exports = new WorkflowService(); 