const express = require('express');
const router = express.Router();

// API版本信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '智能办公系统API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      departments: '/api/departments',
      attendance: '/api/attendance',
      meetings: '/api/meetings',
      files: '/api/files',
      workflows: '/api/workflows'
    }
  });
});

// 模块路由（后续添加）
// router.use('/auth', require('../modules/auth/routes'));
// router.use('/users', require('../modules/user/routes'));
// router.use('/departments', require('../modules/department/routes'));
// router.use('/attendance', require('../modules/attendance/routes'));
// router.use('/meetings', require('../modules/meeting/routes'));
// router.use('/files', require('../modules/file/routes'));
// router.use('/workflows', require('../modules/workflow/routes'));

module.exports = router; 