const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const JWT_CONFIG = require('../config/jwt');

class TokenService {
  /**
   * 生成访问令牌
   * @param {Object} payload - 用户信息载荷
   * @returns {string} JWT访问令牌
   */
  generateAccessToken(payload) {
    const { id, username, email, roles = [] } = payload;
    
    return jwt.sign(
      {
        id,
        username,
        email,
        roles: roles.map(role => role.code || role),
        type: JWT_CONFIG.TOKEN_TYPES.ACCESS
      },
      JWT_CONFIG.ACCESS_TOKEN.secret,
      {
        expiresIn: JWT_CONFIG.ACCESS_TOKEN.expiresIn,
        algorithm: JWT_CONFIG.ACCESS_TOKEN.algorithm,
        issuer: JWT_CONFIG.ACCESS_TOKEN.issuer,
        audience: JWT_CONFIG.ACCESS_TOKEN.audience
      }
    );
  }

  /**
   * 生成刷新令牌
   * @param {Object} payload - 用户信息载荷
   * @returns {string} JWT刷新令牌
   */
  generateRefreshToken(payload) {
    const { id, username } = payload;
    
    return jwt.sign(
      {
        id,
        username,
        type: JWT_CONFIG.TOKEN_TYPES.REFRESH,
        jti: crypto.randomUUID() // JWT ID，用于撤销令牌
      },
      JWT_CONFIG.REFRESH_TOKEN.secret,
      {
        expiresIn: JWT_CONFIG.REFRESH_TOKEN.expiresIn,
        algorithm: JWT_CONFIG.REFRESH_TOKEN.algorithm,
        issuer: JWT_CONFIG.REFRESH_TOKEN.issuer,
        audience: JWT_CONFIG.REFRESH_TOKEN.audience
      }
    );
  }

  /**
   * 生成重置密码令牌
   * @param {Object} payload - 用户信息载荷
   * @returns {string} 重置密码令牌
   */
  generateResetPasswordToken(payload) {
    const { id, email } = payload;
    
    return jwt.sign(
      {
        id,
        email,
        type: JWT_CONFIG.TOKEN_TYPES.RESET_PASSWORD
      },
      JWT_CONFIG.ACCESS_TOKEN.secret,
      {
        expiresIn: '1h', // 1小时过期
        algorithm: JWT_CONFIG.ACCESS_TOKEN.algorithm,
        issuer: JWT_CONFIG.ACCESS_TOKEN.issuer,
        audience: JWT_CONFIG.ACCESS_TOKEN.audience
      }
    );
  }

  /**
   * 验证访问令牌
   * @param {string} token - JWT令牌
   * @returns {Object} 解码的载荷
   */
  verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_CONFIG.ACCESS_TOKEN.secret, {
        algorithms: [JWT_CONFIG.ACCESS_TOKEN.algorithm],
        issuer: JWT_CONFIG.ACCESS_TOKEN.issuer,
        audience: JWT_CONFIG.ACCESS_TOKEN.audience
      });

      if (decoded.type !== JWT_CONFIG.TOKEN_TYPES.ACCESS) {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      throw new Error(`Token verification failed: ${error.message}`);
    }
  }

  /**
   * 验证刷新令牌
   * @param {string} token - JWT刷新令牌
   * @returns {Object} 解码的载荷
   */
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_CONFIG.REFRESH_TOKEN.secret, {
        algorithms: [JWT_CONFIG.REFRESH_TOKEN.algorithm],
        issuer: JWT_CONFIG.REFRESH_TOKEN.issuer,
        audience: JWT_CONFIG.REFRESH_TOKEN.audience
      });

      if (decoded.type !== JWT_CONFIG.TOKEN_TYPES.REFRESH) {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      throw new Error(`Refresh token verification failed: ${error.message}`);
    }
  }

  /**
   * 验证重置密码令牌
   * @param {string} token - 重置密码令牌
   * @returns {Object} 解码的载荷
   */
  verifyResetPasswordToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_CONFIG.ACCESS_TOKEN.secret, {
        algorithms: [JWT_CONFIG.ACCESS_TOKEN.algorithm],
        issuer: JWT_CONFIG.ACCESS_TOKEN.issuer,
        audience: JWT_CONFIG.ACCESS_TOKEN.audience
      });

      if (decoded.type !== JWT_CONFIG.TOKEN_TYPES.RESET_PASSWORD) {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      throw new Error(`Reset password token verification failed: ${error.message}`);
    }
  }

  /**
   * 生成令牌对（访问令牌+刷新令牌）
   * @param {Object} user - 用户信息
   * @returns {Object} 包含访问令牌和刷新令牌的对象
   */
  generateTokenPair(user) {
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: JWT_CONFIG.ACCESS_TOKEN.expiresIn
    };
  }

  /**
   * 从请求头中提取令牌
   * @param {string} authHeader - Authorization头
   * @returns {string|null} 提取的令牌
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader) return null;
    
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }
    
    return parts[1];
  }

  /**
   * 解码令牌（不验证签名，用于调试）
   * @param {string} token - JWT令牌
   * @returns {Object} 解码的载荷
   */
  decodeToken(token) {
    return jwt.decode(token, { complete: true });
  }
}

module.exports = new TokenService(); 