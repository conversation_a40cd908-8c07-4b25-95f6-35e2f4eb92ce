const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Meeting extends Model {
    static associate(models) {
      // 关联到会议室表
      Meeting.belongsTo(models.MeetingRoom, {
        foreignKey: 'meeting_room_id',
        as: 'meetingRoom'
      });

      // 关联到员工表（组织者）
      Meeting.belongsTo(models.Employee, {
        foreignKey: 'organizer_id',
        as: 'organizer'
      });

      // 关联到参会人表
      Meeting.hasMany(models.MeetingParticipant, {
        foreignKey: 'meeting_id',
        as: 'participants'
      });
    }

    // 静态方法：获取会议列表
    static async getMeetingList(options = {}) {
      const {
        page = 1,
        pageSize = 20,
        startDate,
        endDate,
        organizerId,
        meetingRoomId,
        status,
        keyword
      } = options;

      const where = {};
      
      if (startDate && endDate) {
        where.start_time = {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        };
      }
      
      if (organizerId) {
        where.organizer_id = organizerId;
      }
      
      if (meetingRoomId) {
        where.meeting_room_id = meetingRoomId;
      }
      
      if (status) {
        where.status = status;
      }
      
      if (keyword) {
        where[sequelize.Sequelize.Op.or] = [
          { title: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { agenda: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
        ];
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include: [
          {
            model: sequelize.models.MeetingRoom,
            as: 'meetingRoom',
            attributes: ['id', 'name', 'location', 'capacity']
          },
          {
            model: sequelize.models.Employee,
            as: 'organizer',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name', 'email']
            }]
          },
          {
            model: sequelize.models.MeetingParticipant,
            as: 'participants',
            include: [{
              model: sequelize.models.Employee,
              as: 'employee',
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name', 'email']
              }]
            }]
          }
        ],
        limit: pageSize,
        offset,
        order: [['start_time', 'ASC']]
      });

      return {
        meetings: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：获取日历事件格式的会议数据
    static async getCalendarEvents(startDate, endDate, userId = null) {
      const where = {
        start_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        },
        status: ['scheduled', 'ongoing', 'completed']
      };

      // 如果指定了用户ID，只返回该用户相关的会议
      if (userId) {
        where[sequelize.Sequelize.Op.or] = [
          { organizer_id: userId },
          {
            '$participants.employee_id$': userId,
            '$participants.status$': ['accepted', 'tentative']
          }
        ];
      }

      const meetings = await this.findAll({
        where,
        include: [
          {
            model: sequelize.models.MeetingRoom,
            as: 'meetingRoom',
            attributes: ['name', 'location']
          },
          {
            model: sequelize.models.Employee,
            as: 'organizer',
            include: [{
              model: sequelize.models.User,
              as: 'user',
              attributes: ['real_name']
            }]
          },
          {
            model: sequelize.models.MeetingParticipant,
            as: 'participants',
            include: [{
              model: sequelize.models.Employee,
              as: 'employee',
              include: [{
                model: sequelize.models.User,
                as: 'user',
                attributes: ['real_name']
              }]
            }]
          }
        ]
      });

      // 转换为FullCalendar事件格式
      return meetings.map(meeting => ({
        id: meeting.id,
        title: meeting.title,
        start: meeting.start_time,
        end: meeting.end_time,
        backgroundColor: this._getStatusColor(meeting.status),
        borderColor: this._getStatusColor(meeting.status),
        extendedProps: {
          meetingRoom: meeting.meetingRoom,
          organizer: meeting.organizer,
          participants: meeting.participants,
          agenda: meeting.agenda,
          status: meeting.status,
          isRecurring: meeting.is_recurring
        }
      }));
    }

    // 静态方法：检查会议时间冲突
    static async checkConflict(meetingRoomId, startTime, endTime, excludeMeetingId = null) {
      const where = {
        meeting_room_id: meetingRoomId,
        status: ['scheduled', 'ongoing'],
        [sequelize.Sequelize.Op.or]: [
          {
            start_time: {
              [sequelize.Sequelize.Op.between]: [startTime, endTime]
            }
          },
          {
            end_time: {
              [sequelize.Sequelize.Op.between]: [startTime, endTime]
            }
          },
          {
            [sequelize.Sequelize.Op.and]: [
              {
                start_time: {
                  [sequelize.Sequelize.Op.lte]: startTime
                }
              },
              {
                end_time: {
                  [sequelize.Sequelize.Op.gte]: endTime
                }
              }
            ]
          }
        ]
      };

      if (excludeMeetingId) {
        where.id = { [sequelize.Sequelize.Op.ne]: excludeMeetingId };
      }

      const conflictingMeeting = await this.findOne({ where });
      return conflictingMeeting;
    }

    // 实例方法：取消会议
    async cancel(reason = '') {
      this.status = 'cancelled';
      this.cancellation_reason = reason;
      this.cancelled_at = new Date();
      return this.save();
    }

    // 实例方法：开始会议
    async start() {
      if (this.status !== 'scheduled') {
        throw new Error('只能开始已安排的会议');
      }
      this.status = 'ongoing';
      this.actual_start_time = new Date();
      return this.save();
    }

    // 实例方法：结束会议
    async end() {
      if (this.status !== 'ongoing') {
        throw new Error('只能结束进行中的会议');
      }
      this.status = 'completed';
      this.actual_end_time = new Date();
      return this.save();
    }

    // 静态方法：获取状态颜色
    static _getStatusColor(status) {
      const colorMap = {
        scheduled: '#409EFF',
        ongoing: '#67C23A',
        completed: '#909399',
        cancelled: '#F56C6C'
      };
      return colorMap[status] || '#409EFF';
    }

    // 静态方法：获取会议统计
    static async getMeetingStats(startDate, endDate, organizerId = null) {
      const where = {
        start_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      };

      if (organizerId) {
        where.organizer_id = organizerId;
      }

      const meetings = await this.findAll({ where });

      const stats = {
        total: meetings.length,
        scheduled: 0,
        completed: 0,
        cancelled: 0,
        totalDuration: 0,
        averageDuration: 0
      };

      meetings.forEach(meeting => {
        stats[meeting.status]++;
        
        const duration = new Date(meeting.end_time) - new Date(meeting.start_time);
        stats.totalDuration += duration / (1000 * 60 * 60); // 转换为小时
      });

      stats.averageDuration = stats.total > 0 ? 
        Math.round((stats.totalDuration / stats.total) * 100) / 100 : 0;
      stats.totalDuration = Math.round(stats.totalDuration * 100) / 100;

      return stats;
    }
  }

  Meeting.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '会议标题'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '会议描述'
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '开始时间'
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '结束时间'
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '会议地点'
    },
    meeting_room_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '会议室ID'
    },
    organizer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '组织者ID',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM('scheduled', 'in_progress', 'completed', 'cancelled'),
      defaultValue: 'scheduled',
      comment: '会议状态'
    },
    meeting_type: {
      type: DataTypes.ENUM('online', 'offline', 'hybrid'),
      defaultValue: 'offline',
      comment: '会议类型'
    },
    meeting_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '线上会议链接'
    },
    max_participants: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '最大参与人数'
    },
    is_recurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否重复会议'
    },
    recurring_pattern: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '重复模式'
    },
    reminder_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 15,
      comment: '提醒时间(分钟)'
    },
    is_private: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否私密会议'
    },
    agenda: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '会议议程'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '会议记录'
    },
    cancellation_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '取消原因'
    },
    actual_start_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '实际开始时间'
    },
    actual_end_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '实际结束时间'
    }
  }, {
    sequelize,
    modelName: 'Meeting',
    tableName: 'meetings',
    timestamps: true,
    paranoid: true,
    comment: '会议表',
    indexes: [
      {
        fields: ['organizer_id']
      },
      {
        fields: ['start_time']
      },
      {
        fields: ['status']
      },
      {
        fields: ['meeting_room_id']
      }
    ]
  });

  return Meeting;
}; 