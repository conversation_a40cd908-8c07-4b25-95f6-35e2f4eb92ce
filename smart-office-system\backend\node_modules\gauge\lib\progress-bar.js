'use strict'
var validate = require('aproba')
var renderTemplate = require('./render-template.js')
var wideTruncate = require('./wide-truncate')
var stringWidth = require('string-width')

module.exports = function (theme, width, completed) {
  validate('ONN', [theme, width, completed])
  if (completed < 0) {
    completed = 0
  }
  if (completed > 1) {
    completed = 1
  }
  if (width <= 0) {
    return ''
  }
  var sofar = Math.round(width * completed)
  var rest = width - sofar
  var template = [
    { type: 'complete', value: repeat(theme.complete, sofar), length: sofar },
    { type: 'remaining', value: repeat(theme.remaining, rest), length: rest },
  ]
  return renderTemplate(width, template, theme)
}

// lodash's way of repeating
function repeat (string, width) {
  var result = ''
  var n = width
  do {
    if (n % 2) {
      result += string
    }
    n = Math.floor(n / 2)
    /* eslint no-self-assign: 0 */
    string += string
  } while (n && stringWidth(result) < width)

  return wideTruncate(result, width)
}
