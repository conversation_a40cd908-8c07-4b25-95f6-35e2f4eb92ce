<template>
  <div class="meeting-list">
    <div class="page-header">
      <h1>🎯 会议管理 (MeetingList.vue)</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建会议
        </el-button>
        <el-button @click="$router.push('/meetings/calendar')">
          <el-icon><Calendar /></el-icon>
          日历视图
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" :inline="true" class="filter-form">
        <el-form-item label="会议标题">
          <el-input
            v-model="filters.title"
            placeholder="请输入会议标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="请选择状态" clearable>
            <el-option label="待开始" value="pending" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已结束" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="会议室">
          <el-select v-model="filters.roomId" placeholder="请选择会议室" clearable>
            <el-option
              v-for="room in meetingRooms"
              :key="room.id"
              :label="room.name"
              :value="room.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchMeetings">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 会议表格 -->
    <el-card class="table-card">
      <el-table
        :data="meetingList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="title" label="会议标题" min-width="200" />
        <el-table-column prop="organizerName" label="组织者" width="120" />
        <el-table-column prop="roomName" label="会议室" width="120" />
        <el-table-column label="会议时间" width="300">
          <template #default="{ row }">
            <div>
              <div>{{ formatDateTime(row.startTime) }}</div>
              <div class="text-gray">{{ formatDateTime(row.endTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="参会人数" width="100">
          <template #default="{ row }">
            {{ row.attendees?.length || 0 }}人
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="会议描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewMeeting(row)">
              查看
            </el-button>
            <el-button
              v-if="canEdit(row)"
              type="warning"
              size="small"
              @click="editMeeting(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="canCancel(row)"
              type="danger"
              size="small"
              @click="cancelMeeting(row.id)"
            >
              取消
            </el-button>
            <el-button
              v-if="canDelete(row)"
              type="danger"
              size="small"
              @click="deleteMeeting(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑会议对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editMode ? '编辑会议' : '创建会议'"
      width="800px"
    >
      <el-form
        :model="meetingForm"
        :rules="meetingRules"
        ref="meetingFormRef"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="会议标题" prop="title">
              <el-input v-model="meetingForm.title" placeholder="请输入会议标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议室" prop="roomId">
              <el-select v-model="meetingForm.roomId" placeholder="请选择会议室">
                <el-option
                  v-for="room in meetingRooms"
                  :key="room.id"
                  :label="room.name"
                  :value="room.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="meetingForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="meetingForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="参会人员" prop="attendees">
          <el-select
            v-model="meetingForm.attendees"
            multiple
            placeholder="请选择参会人员"
            style="width: 100%"
          >
            <el-option
              v-for="employee in employees"
              :key="employee.id"
              :label="`${employee.name} (${employee.departmentName} - ${employee.positionName})`"
              :value="employee.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会议描述">
          <el-input
            v-model="meetingForm.description"
            type="textarea"
            placeholder="请输入会议描述"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="会议议程">
          <el-input
            v-model="meetingForm.agenda"
            type="textarea"
            placeholder="请输入会议议程"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMeeting">
          {{ editMode ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 会议详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="会议详情" width="700px">
      <div v-if="currentMeeting" class="meeting-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会议标题">
            {{ currentMeeting.title }}
          </el-descriptions-item>
          <el-descriptions-item label="组织者">
            {{ currentMeeting.organizerName }}
          </el-descriptions-item>
          <el-descriptions-item label="会议室">
            {{ currentMeeting.roomName }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentMeeting.status)">
              {{ getStatusText(currentMeeting.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(currentMeeting.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatDateTime(currentMeeting.endTime) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h4>参会人员</h4>
          <el-tag
            v-for="attendee in currentMeeting.attendees"
            :key="attendee.id"
            class="attendee-tag"
          >
            {{ attendee.name }}
          </el-tag>
        </div>

        <div v-if="currentMeeting.description" class="detail-section">
          <h4>会议描述</h4>
          <p>{{ currentMeeting.description }}</p>
        </div>

        <div v-if="currentMeeting.agenda" class="detail-section">
          <h4>会议议程</h4>
          <p>{{ currentMeeting.agenda }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Calendar, Search, Refresh } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useMeetingStore } from '@/stores/meeting'
import { useEmployeeStore } from '@/stores/employee'

// Store
const userStore = useUserStore()
const meetingStore = useMeetingStore()
const employeeStore = useEmployeeStore()

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editMode = ref(false)
const meetingFormRef = ref(null)
const currentMeeting = ref(null)

// 筛选条件
const filters = reactive({
  title: '',
  status: '',
  roomId: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 使用store中的会议数据
const meetingList = computed(() => {
  const storeMeetings = meetingStore.meetings;
  // 确保始终返回数组，避免 data.includes 错误
  return Array.isArray(storeMeetings) ? storeMeetings : [];
})

// 使用store中的会议室数据
const meetingRooms = computed(() => {
  const storeRooms = meetingStore.meetingRooms;
  return Array.isArray(storeRooms) ? storeRooms : [];
})

// 从员工store获取真实员工数据
const employees = computed(() => {
  return employeeStore.employees.map(emp => ({
    id: emp.id,
    name: emp.user?.real_name || emp.user?.username || '未知员工',
    departmentName: emp.department?.name || '未知部门',
    positionName: emp.position?.name || '未知职位'
  }))
})

// 会议表单
const meetingForm = reactive({
  id: '',
  title: '',
  roomId: '',
  startTime: '',
  endTime: '',
  attendees: [],
  description: '',
  agenda: ''
})

// 表单验证规则
const meetingRules = {
  title: [{ required: true, message: '请输入会议标题', trigger: 'blur' }],
  roomId: [{ required: true, message: '请选择会议室', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  attendees: [{ required: true, message: '请选择参会人员', trigger: 'change' }]
}

// 方法
const searchMeetings = () => {
  pagination.page = 1
  loadMeetingList()
}

const resetFilters = () => {
  Object.assign(filters, {
    title: '',
    status: '',
    roomId: '',
    dateRange: []
  })
  searchMeetings()
}

const loadMeetingList = async () => {
  loading.value = true
  try {
    // 调用store方法获取会议数据
    const result = await meetingStore.fetchMeetings({
      page: pagination.page,
      pageSize: pagination.size,
      ...filters
    })

    if (result) {
      pagination.total = meetingStore.totalMeetings
    }
  } catch (error) {
    console.error('加载会议数据失败:', error)
    ElMessage.error('加载会议数据失败')
  } finally {
    loading.value = false
  }
}

const viewMeeting = (meeting) => {
  currentMeeting.value = meeting
  showDetailDialog.value = true
}

const editMeeting = (meeting) => {
  editMode.value = true
  Object.assign(meetingForm, {
    id: meeting.id,
    title: meeting.title,
    roomId: meeting.roomId,
    startTime: meeting.startTime,
    endTime: meeting.endTime,
    attendees: meeting.attendees?.map(a => a.id) || [],
    description: meeting.description,
    agenda: meeting.agenda
  })
  showCreateDialog.value = true
}

const saveMeeting = async () => {
  try {
    await meetingFormRef.value.validate()

    // 验证时间逻辑
    if (new Date(meetingForm.startTime) >= new Date(meetingForm.endTime)) {
      ElMessage.error('结束时间必须晚于开始时间')
      return
    }

    // 准备会议数据
    const meetingData = {
      title: meetingForm.title,
      room_id: meetingForm.roomId,
      start_time: meetingForm.startTime,
      end_time: meetingForm.endTime,
      description: meetingForm.description,
      agenda: meetingForm.agenda,
      participants: meetingForm.attendees,
      organizer_id: userStore.userInfo?.id
    }

    if (editMode.value) {
      // 更新会议
      await meetingStore.updateMeeting(meetingForm.id, meetingData)
    } else {
      // 创建会议
      await meetingStore.createMeeting(meetingData)
    }

    showCreateDialog.value = false
    resetMeetingForm()
    await loadMeetingList()
  } catch (error) {
    console.error('保存会议失败:', error)

    // 处理表单验证错误
    if (typeof error === 'object' && error !== null) {
      const errorMessages = []
      for (const field in error) {
        if (Array.isArray(error[field])) {
          errorMessages.push(error[field][0].message)
        }
      }
      if (errorMessages.length > 0) {
        ElMessage.error(errorMessages.join(', '))
        return
      }
    }

    ElMessage.error('保存会议失败')
  }
}

const cancelMeeting = async (id) => {
  try {
    await ElMessageBox.confirm('确认取消这个会议吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用store方法取消会议
    await meetingStore.cancelMeeting(id, '用户主动取消')
    await loadMeetingList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消会议失败:', error)
      ElMessage.error('取消会议失败')
    }
  }
}

const deleteMeeting = async (id) => {
  try {
    await ElMessageBox.confirm('确认删除这个会议吗？删除后无法恢复！', '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 调用store方法删除会议
    await meetingStore.deleteMeeting(id)
    await loadMeetingList()
    ElMessage.success('会议删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会议失败:', error)
      ElMessage.error('删除会议失败')
    }
  }
}

const resetMeetingForm = () => {
  editMode.value = false
  Object.assign(meetingForm, {
    id: '',
    title: '',
    roomId: '',
    startTime: '',
    endTime: '',
    attendees: [],
    description: '',
    agenda: ''
  })
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadMeetingList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadMeetingList()
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    ongoing: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待开始',
    ongoing: '进行中',
    completed: '已结束',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const canEdit = (meeting) => {
  return meeting.status === 'pending' && meeting.organizerId === userStore.userInfo?.id
}

const canCancel = (meeting) => {
  return meeting.status === 'pending' && meeting.organizerId === userStore.userInfo?.id
}

const canDelete = (meeting) => {
  // 只有组织者可以删除会议，且会议状态为已取消或已结束
  return (meeting.status === 'cancelled' || meeting.status === 'completed') &&
         meeting.organizerId === userStore.userInfo?.id
}

// 初始化数据
const initializeData = async () => {
  try {
    console.log('🔄 会议管理页面初始化...')

    // 并行加载员工、会议室和会议数据
    await Promise.all([
      employeeStore.fetchEmployees(),
      meetingStore.fetchMeetingRooms(),
      loadMeetingList()
    ])

    console.log('✅ 员工数据加载完成:', employees.value.length, '个员工')
    console.log('✅ 会议室数据加载完成:', meetingRooms.value.length, '个会议室')
    console.log('✅ 会议数据加载完成:', meetingList.value.length, '个会议')
    console.log('✅ 会议管理页面初始化完成')
  } catch (error) {
    console.error('❌ 会议管理页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
}

onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.meeting-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.text-gray {
  color: #909399;
  font-size: 12px;
}

.meeting-detail {
  padding: 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
}

.attendee-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .meeting-list {
    padding: 12px;
  }
}
</style> 