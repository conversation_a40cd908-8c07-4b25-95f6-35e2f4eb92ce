import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from '@/utils/axios'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const permissions = ref([])

  // 登录
  async function login(credentials) {
    try {
      const response = await axios.post('/v1/auth/login', credentials)
      const { accessToken, user } = response.data.data

      token.value = accessToken
      userInfo.value = user

      localStorage.setItem('token', accessToken)

      return response.data
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  async function getUserInfo() {
    try {
      const response = await axios.get('/v1/auth/profile')
      userInfo.value = response.data.data
      permissions.value = response.data.data.permissions || []

      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error.message)
      // 如果是网络错误或服务器不可用，清除token
      if (!error.response || error.response.status >= 500) {
        logout()
      }
      throw error
    }
  }

  // 登出
  function logout() {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    localStorage.removeItem('token')
  }

  // 检查权限
  function hasPermission(permission) {
    return permissions.value.includes(permission)
  }

  // 检查角色
  function hasRole(role) {
    return userInfo.value?.roles?.includes(role) || false
  }

  return {
    token,
    userInfo,
    permissions,
    login,
    getUserInfo,
    logout,
    hasPermission,
    hasRole
  }
}) 