const logger = require('../shared/logger');
const { createErrorResponse, handleSequelizeError } = require('../shared/errors');

/**
 * API错误处理工具 - 专门为schedules和office-supplies API设计
 */

// 增强的API错误处理包装器
const enhancedApiHandler = (apiFunction, operationName) => {
  return async (req, res, next) => {
    const startTime = Date.now();
    const requestId = generateRequestId();
    
    try {
      // 记录API调用开始
      logger.info(`🚀 API Call Started [${requestId}]`, {
        operation: operationName,
        method: req.method,
        url: req.originalUrl,
        user: req.user ? { id: req.user.id, username: req.user.username } : null,
        requestId
      });

      // 执行API函数
      await apiFunction(req, res, next);
      
      // 记录API调用成功
      const duration = Date.now() - startTime;
      logger.info(`✅ API Call Success [${requestId}]`, {
        operation: operationName,
        duration: `${duration}ms`,
        statusCode: res.statusCode,
        requestId
      });
      
    } catch (error) {
      // 记录API调用失败
      const duration = Date.now() - startTime;
      const errorDetails = analyzeApiError(error, req, operationName);
      
      logger.error(`❌ API Call Failed [${requestId}]`, {
        operation: operationName,
        duration: `${duration}ms`,
        error: errorDetails,
        requestId
      });

      // 发送详细错误响应
      sendDetailedErrorResponse(res, error, errorDetails, operationName);
    }
  };
};

// 生成请求ID
const generateRequestId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 分析API错误
const analyzeApiError = (error, req, operationName) => {
  const errorDetails = {
    operation: operationName,
    errorType: 'UNKNOWN_ERROR',
    errorName: error.name,
    message: error.message,
    isDatabase: false,
    isValidation: false,
    isAuthentication: false,
    timestamp: new Date().toISOString(),
    request: {
      method: req.method,
      path: req.path,
      params: req.params,
      query: req.query,
      body: sanitizeBody(req.body),
      user: req.user ? { id: req.user.id, username: req.user.username } : null
    }
  };

  // Sequelize数据库错误分析
  if (error.name && error.name.startsWith('Sequelize')) {
    errorDetails.isDatabase = true;
    
    switch (error.name) {
      case 'SequelizeConnectionError':
        errorDetails.errorType = 'DATABASE_CONNECTION_FAILED';
        errorDetails.severity = 'CRITICAL';
        errorDetails.suggestedAction = 'Check database connection and ensure MySQL is running';
        break;
        
      case 'SequelizeDatabaseError':
        errorDetails.errorType = 'DATABASE_QUERY_FAILED';
        errorDetails.severity = 'HIGH';
        
        if (error.message.includes('Unknown column')) {
          const columnMatch = error.message.match(/'([^']+)'/);
          const tableMatch = error.message.match(/in '([^']+)'/);
          errorDetails.missingColumn = columnMatch ? columnMatch[1] : 'unknown';
          errorDetails.affectedTable = tableMatch ? tableMatch[1] : 'unknown';
          errorDetails.suggestedAction = `Add missing column '${errorDetails.missingColumn}' to table '${errorDetails.affectedTable}'`;
        } else if (error.message.includes("Table") && error.message.includes("doesn't exist")) {
          const tableMatch = error.message.match(/Table '([^']+)'/);
          errorDetails.missingTable = tableMatch ? tableMatch[1] : 'unknown';
          errorDetails.suggestedAction = `Create missing table '${errorDetails.missingTable}'`;
        }
        break;
        
      case 'SequelizeValidationError':
        errorDetails.errorType = 'DATABASE_VALIDATION_FAILED';
        errorDetails.isValidation = true;
        errorDetails.severity = 'MEDIUM';
        errorDetails.validationErrors = error.errors?.map(err => ({
          field: err.path,
          message: err.message,
          value: err.value,
          type: err.type
        }));
        errorDetails.suggestedAction = 'Fix validation errors in request data';
        break;
        
      case 'SequelizeUniqueConstraintError':
        errorDetails.errorType = 'DATABASE_UNIQUE_CONSTRAINT_VIOLATION';
        errorDetails.severity = 'MEDIUM';
        errorDetails.constraintViolations = error.errors?.map(err => ({
          field: err.path,
          value: err.value
        }));
        errorDetails.suggestedAction = 'Use unique values for constrained fields';
        break;
    }
  }

  // 认证错误分析
  if (error.name === 'UnauthorizedError' || error.message.includes('token')) {
    errorDetails.isAuthentication = true;
    errorDetails.errorType = 'AUTHENTICATION_FAILED';
    errorDetails.severity = 'MEDIUM';
    errorDetails.suggestedAction = 'Check authentication token validity';
  }

  return errorDetails;
};

// 清理请求体敏感信息
const sanitizeBody = (body) => {
  if (!body || typeof body !== 'object') return body;
  
  const sanitized = { ...body };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

// 发送详细错误响应
const sendDetailedErrorResponse = (res, error, errorDetails, operationName) => {
  let statusCode = 500;
  let errorCode = 'INTERNAL_SERVER_ERROR';
  let userMessage = '服务器内部错误';

  // 根据错误类型设置状态码和消息
  switch (errorDetails.errorType) {
    case 'DATABASE_VALIDATION_FAILED':
      statusCode = 400;
      errorCode = 'VALIDATION_ERROR';
      userMessage = '数据验证失败';
      break;
      
    case 'DATABASE_UNIQUE_CONSTRAINT_VIOLATION':
      statusCode = 409;
      errorCode = 'DUPLICATE_DATA';
      userMessage = '数据重复，请检查唯一性字段';
      break;
      
    case 'AUTHENTICATION_FAILED':
      statusCode = 401;
      errorCode = 'UNAUTHORIZED';
      userMessage = '认证失败，请重新登录';
      break;
      
    case 'DATABASE_CONNECTION_FAILED':
      statusCode = 503;
      errorCode = 'SERVICE_UNAVAILABLE';
      userMessage = '数据库服务暂时不可用';
      break;
      
    case 'DATABASE_QUERY_FAILED':
      statusCode = 500;
      errorCode = 'DATABASE_ERROR';
      
      if (errorDetails.missingColumn) {
        userMessage = `系统配置错误：缺少必要的数据字段 '${errorDetails.missingColumn}'`;
      } else if (errorDetails.missingTable) {
        userMessage = `系统配置错误：缺少必要的数据表 '${errorDetails.missingTable}'`;
      } else {
        userMessage = '数据库查询失败';
      }
      break;
      
    default:
      userMessage = `${operationName}操作失败`;
  }

  // 构建响应
  const response = {
    success: false,
    error: {
      code: errorCode,
      message: userMessage,
      operation: operationName,
      timestamp: new Date().toISOString()
    }
  };

  // 开发环境返回详细信息
  if (process.env.NODE_ENV !== 'production') {
    response.error.details = {
      originalMessage: error.message,
      errorType: errorDetails.errorType,
      stack: error.stack,
      analysis: errorDetails,
      suggestedAction: errorDetails.suggestedAction
    };
  }

  // 特殊处理验证错误
  if (errorDetails.validationErrors) {
    response.error.validationErrors = errorDetails.validationErrors;
  }

  res.status(statusCode).json(response);
};

// 操作特定的错误处理器
const scheduleErrorHandler = (error, operation, req) => {
  const baseMessage = {
    'get_schedules': '获取日程列表',
    'create_schedule': '创建日程',
    'update_schedule': '更新日程',
    'delete_schedule': '删除日程'
  };
  
  return `${baseMessage[operation] || operation}失败`;
};

const supplyErrorHandler = (error, operation, req) => {
  const baseMessage = {
    'get_supplies': '获取办公用品列表',
    'create_supply': '创建办公用品',
    'update_supply': '更新办公用品',
    'delete_supply': '删除办公用品'
  };
  
  return `${baseMessage[operation] || operation}失败`;
};

// 创建错误上下文信息
const createErrorContext = (req, operation) => {
  return {
    operation,
    timestamp: new Date().toISOString(),
    request: {
      method: req.method,
      path: req.path,
      query: req.query,
      params: req.params,
      user: req.user ? {
        id: req.user.id,
        username: req.user.username
      } : null
    }
  };
};

module.exports = {
  enhancedApiHandler,
  analyzeApiError,
  sendDetailedErrorResponse,
  scheduleErrorHandler,
  supplyErrorHandler,
  createErrorContext
}; 