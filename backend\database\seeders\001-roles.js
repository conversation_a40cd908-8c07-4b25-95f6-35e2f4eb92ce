'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = [
      {
        id: 1,
        name: '超级管理员',
        code: 'SUPER_ADMIN',
        description: '系统超级管理员，拥有所有权限',
        permissions: JSON.stringify([
          'system:*',
          'user:*',
          'role:*',
          'department:*',
          'employee:*',
          'attendance:*',
          'leave:*',
          'meeting:*',
          'workflow:*',
          'report:*'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 2,
        name: '系统管理员',
        code: 'ADMIN',
        description: '系统管理员，负责用户和基础数据管理',
        permissions: JSON.stringify([
          'user:read',
          'user:create',
          'user:update',
          'role:read',
          'department:*',
          'employee:*',
          'attendance:read',
          'leave:read',
          'meeting:*',
          'report:read'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 3,
        name: '人事经理',
        code: 'HR_MANAGER',
        description: '人事经理，负责员工管理和考勤管理',
        permissions: JSON.stringify([
          'employee:*',
          'attendance:*',
          'leave:*',
          'department:read',
          'user:read',
          'report:hr'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 4,
        name: '部门经理',
        code: 'DEPT_MANAGER',
        description: '部门经理，管理本部门员工和审批',
        permissions: JSON.stringify([
          'employee:read',
          'employee:update',
          'attendance:read',
          'leave:approve',
          'meeting:*',
          'workflow:approve',
          'report:dept'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 5,
        name: '普通员工',
        code: 'EMPLOYEE',
        description: '普通员工，基础功能权限',
        permissions: JSON.stringify([
          'user:profile',
          'attendance:self',
          'leave:self',
          'meeting:participant',
          'workflow:initiate'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 6,
        name: '访客',
        code: 'GUEST',
        description: '访客账号，只读权限',
        permissions: JSON.stringify([
          'user:profile',
          'department:read',
          'meeting:read'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('roles', roles);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', null, {});
  }
}; 