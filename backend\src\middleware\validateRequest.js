const { validationResult } = require('express-validator');

/**
 * 请求验证中间件
 * 检查express-validator的验证结果，如果有错误则返回400错误
 */
const validateRequest = (validations) => {
  return async (req, res, next) => {
    // 如果传入的是验证器数组，则先执行所有验证器
    if (Array.isArray(validations)) {
      for (const validation of validations) {
        const result = await validation.run(req);
        if (!result.isEmpty()) break;
      }
    }

    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: errors.array()
        }
      });
    }

    next();
  };
};

module.exports = {
  validateRequest
}; 