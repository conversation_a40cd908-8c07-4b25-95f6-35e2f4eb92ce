{"development": {"dialect": "sqlite", "storage": "./database/smart_office_development.db", "logging": false, "define": {"timestamps": true, "underscored": true, "createdAt": "created_at", "updatedAt": "updated_at", "deletedAt": "deleted_at"}}, "test": {"username": "root", "password": "123456", "database": "smart_office_test", "host": "127.0.0.1", "port": 3306, "dialect": "mysql", "timezone": "+08:00"}, "production": {"username": "root", "password": "123456", "database": "smart_office_prod", "host": "127.0.0.1", "port": 3306, "dialect": "mysql", "timezone": "+08:00", "define": {"timestamps": true, "underscored": true, "createdAt": "created_at", "updatedAt": "updated_at", "deletedAt": "deleted_at"}}}