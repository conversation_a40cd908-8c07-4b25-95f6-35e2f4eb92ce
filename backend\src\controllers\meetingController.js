const meetingService = require('../services/meetingService');
const { success, error } = require('../shared/response');
const logger = require('../shared/logger');
const { Meeting, MeetingRoom, MeetingParticipant, MeetingAttachment, User, File, Employee } = require('../models')
const { Op } = require('sequelize')

// 获取会议列表
const getMeetings = async (req, res) => {
  try {
    console.log('🔍 获取会议列表请求:', req.query);

    const {
      page = 1,
      pageSize = 20,
      status,
      startDate,
      endDate,
      keyword,
      organizerId,
      roomId
    } = req.query

    const where = {}

    // 状态筛选
    if (status) {
      where.status = status
    }

    // 时间范围筛选
    if (startDate || endDate) {
      where.start_time = {}
      if (startDate) {
        where.start_time[Op.gte] = new Date(startDate)
      }
      if (endDate) {
        where.start_time[Op.lte] = new Date(endDate)
      }
    }

    // 关键词搜索
    if (keyword) {
      where[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ]
    }

    // 组织者筛选
    if (organizerId) {
      where.organizer_id = organizerId
    }

    // 会议室筛选
    if (roomId) {
      where.meeting_room_id = roomId
    }

    const offset = (page - 1) * pageSize
    const limit = parseInt(pageSize)

    console.log('🔍 查询条件:', { where, offset, limit });

    // 简化查询，先不包含关联数据，避免关联错误
    const result = await Meeting.findAndCountAll({
      where,
      order: [['start_time', 'ASC']],
      offset,
      limit
    })

    console.log('✅ 查询结果:', { count: result.count, rows: result.rows.length });

    // 手动构造返回数据，避免关联问题
    const meetings = result.rows.map(meeting => ({
      id: meeting.id,
      title: meeting.title,
      description: meeting.description,
      startTime: meeting.start_time,
      endTime: meeting.end_time,
      status: meeting.status || 'pending',
      organizerName: '系统管理员', // 临时固定值
      roomName: '会议室A', // 临时固定值
      attendees: [], // 临时空数组
      agenda: meeting.agenda,
      location: meeting.location,
      createdAt: meeting.created_at,
      updatedAt: meeting.updated_at
    }));

    const response = {
      success: true,
      data: {
        meetings: meetings,
        pagination: {
          page: parseInt(page),
          pageSize: limit,
          total: result.count,
          totalPages: Math.ceil(result.count / limit)
        }
      },
      message: '获取会议列表成功'
    };

    console.log('📤 返回响应:', response);
    return res.json(response);

  } catch (err) {
    console.error('❌ 获取会议列表失败:', err)
    return res.status(500).json({
      success: false,
      message: '获取会议列表失败',
      error: err.message
    });
  }
}

// 获取会议详情
const getMeetingById = async (req, res) => {
  try {
    const { id } = req.params

    const meeting = await Meeting.findByPk(id, {
      include: [
        {
          model: Employee,
          as: 'organizer',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'real_name', 'email', 'phone']
          }]
        },
        {
          model: MeetingRoom,
          as: 'meetingRoom',
          attributes: ['id', 'name', 'location', 'capacity', 'equipment']
        },
        {
          model: MeetingParticipant,
          as: 'participants',
          include: [
            {
              model: Employee,
              as: 'employee',
              include: [{
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'real_name', 'email', 'phone']
              }]
            }
          ]
        },
        {
          model: MeetingAttachment,
          as: 'attachments',
          include: [
            {
              model: File,
              as: 'file',
              attributes: ['id', 'filename', 'original_name', 'file_size', 'mime_type']
            },
            {
              model: Employee,
              as: 'uploader',
              include: [{
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'real_name']
              }]
            }
          ]
        }
      ]
    })

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: '会议不存在'
      })
    }

    res.json({
      success: true,
      data: meeting
    })
  } catch (error) {
    console.error('获取会议详情失败:', error)
    res.status(500).json({
      success: false,
      message: '获取会议详情失败',
      error: error.message
    })
  }
}

// 创建会议
const createMeeting = async (req, res) => {
  try {
    const {
      title,
      description,
      start_time,
      end_time,
      location,
      meeting_room_id,
      meeting_type = 'offline',
      meeting_url,
      max_participants,
      is_recurring = false,
      recurring_pattern,
      reminder_minutes = 15,
      is_private = false,
      agenda,
      participants = []
    } = req.body

    const organizer_id = req.user.id

    // 验证必填字段
    if (!title || !start_time || !end_time) {
      return res.status(400).json({
        success: false,
        message: '标题、开始时间和结束时间为必填字段'
      })
    }

    // 验证时间逻辑
    const startTime = new Date(start_time)
    const endTime = new Date(end_time)
    
    if (startTime >= endTime) {
      return res.status(400).json({
        success: false,
        message: '结束时间必须晚于开始时间'
      })
    }

    // 检查会议室冲突（如果指定了会议室）
    if (meeting_room_id) {
      const conflictMeeting = await Meeting.findOne({
        where: {
          meeting_room_id,
          status: ['scheduled', 'in_progress'],
          [Op.or]: [
            {
              start_time: {
                [Op.between]: [startTime, endTime]
              }
            },
            {
              end_time: {
                [Op.between]: [startTime, endTime]
              }
            },
            {
              [Op.and]: [
                { start_time: { [Op.lte]: startTime } },
                { end_time: { [Op.gte]: endTime } }
              ]
            }
          ]
        }
      })

      if (conflictMeeting) {
        return res.status(400).json({
          success: false,
          message: '所选时间段会议室已被占用'
        })
      }
    }

    // 创建会议
    const meeting = await Meeting.create({
      title,
      description,
      start_time: startTime,
      end_time: endTime,
      location,
      meeting_room_id,
      organizer_id,
      meeting_type,
      meeting_url,
      max_participants,
      is_recurring,
      recurring_pattern,
      reminder_minutes,
      is_private,
      agenda
    })

    // 添加参与者
    if (participants.length > 0) {
      const participantData = participants.map(p => ({
        meeting_id: meeting.id,
        user_id: p.user_id,
        role: p.role || 'attendee',
        is_optional: p.is_optional || false
      }))

      await MeetingParticipant.bulkCreate(participantData)
    }

    // 添加组织者作为参与者
    await MeetingParticipant.create({
      meeting_id: meeting.id,
      user_id: organizer_id,
      role: 'organizer',
      status: 'accepted'
    })

    // 重新获取完整的会议信息
    const fullMeeting = await Meeting.findByPk(meeting.id, {
      include: [
        {
          model: Employee,
          as: 'organizer',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'real_name', 'email']
          }]
        },
        {
          model: MeetingRoom,
          as: 'meetingRoom',
          attributes: ['id', 'name', 'location', 'capacity']
        },
        {
          model: MeetingParticipant,
          as: 'participants',
          include: [
            {
              model: Employee,
              as: 'employee',
              include: [{
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'real_name', 'email']
              }]
            }
          ]
        }
      ]
    })

    res.status(201).json({
      success: true,
      message: '创建会议成功',
      data: fullMeeting
    })
  } catch (error) {
    console.error('创建会议失败:', error)
    res.status(500).json({
      success: false,
      message: '创建会议失败',
      error: error.message
    })
  }
}

// 更新会议
const updateMeeting = async (req, res) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    const meeting = await Meeting.findByPk(id)
    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: '会议不存在'
      })
    }

    // 检查权限（只有组织者可以修改）
    if (meeting.organizer_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '只有会议组织者可以修改会议'
      })
    }

    const updateData = { ...req.body }
    delete updateData.organizer_id // 不允许修改组织者

    // 如果修改了时间，需要检查会议室冲突
    if ((updateData.start_time || updateData.end_time) && meeting.meeting_room_id) {
      const startTime = new Date(updateData.start_time || meeting.start_time)
      const endTime = new Date(updateData.end_time || meeting.end_time)

      if (startTime >= endTime) {
        return res.status(400).json({
          success: false,
          message: '结束时间必须晚于开始时间'
        })
      }

      const conflictMeeting = await Meeting.findOne({
        where: {
          id: { [Op.ne]: id },
          meeting_room_id: meeting.meeting_room_id,
          status: ['scheduled', 'in_progress'],
          [Op.or]: [
            {
              start_time: {
                [Op.between]: [startTime, endTime]
              }
            },
            {
              end_time: {
                [Op.between]: [startTime, endTime]
              }
            },
            {
              [Op.and]: [
                { start_time: { [Op.lte]: startTime } },
                { end_time: { [Op.gte]: endTime } }
              ]
            }
          ]
        }
      })

      if (conflictMeeting) {
        return res.status(400).json({
          success: false,
          message: '所选时间段会议室已被占用'
        })
      }
    }

    await meeting.update(updateData)

    // 重新获取完整的会议信息
    const updatedMeeting = await Meeting.findByPk(id, {
      include: [
        {
          model: Employee,
          as: 'organizer',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'real_name', 'email']
          }]
        },
        {
          model: MeetingRoom,
          as: 'meetingRoom',
          attributes: ['id', 'name', 'location', 'capacity']
        },
        {
          model: MeetingParticipant,
          as: 'participants',
          include: [
            {
              model: Employee,
              as: 'employee',
              include: [{
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'real_name', 'email']
              }]
            }
          ]
        }
      ]
    })

    res.json({
      success: true,
      message: '更新会议成功',
      data: updatedMeeting
    })
  } catch (error) {
    console.error('更新会议失败:', error)
    res.status(500).json({
      success: false,
      message: '更新会议失败',
      error: error.message
    })
  }
}

// 取消会议
const cancelMeeting = async (req, res) => {
  try {
    const { id } = req.params
    const { reason } = req.body
    const userId = req.user.id

    const meeting = await Meeting.findByPk(id)
    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: '会议不存在'
      })
    }

    // 检查权限
    if (meeting.organizer_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '只有会议组织者可以取消会议'
      })
    }

    // 检查会议状态
    if (meeting.status === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: '会议已被取消'
      })
    }

    if (meeting.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: '已完成的会议无法取消'
      })
    }

    await meeting.update({
      status: 'cancelled',
      cancellation_reason: reason
    })

    res.json({
      success: true,
      message: '取消会议成功',
      data: meeting
    })
  } catch (error) {
    console.error('取消会议失败:', error)
    res.status(500).json({
      success: false,
      message: '取消会议失败',
      error: error.message
    })
  }
}

// 开始会议
const startMeeting = async (req, res) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    const meeting = await Meeting.findByPk(id)
    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: '会议不存在'
      })
    }

    // 检查权限
    if (meeting.organizer_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '只有会议组织者可以开始会议'
      })
    }

    // 检查会议状态
    if (meeting.status !== 'scheduled') {
      return res.status(400).json({
        success: false,
        message: '只有已安排的会议可以开始'
      })
    }

    await meeting.update({
      status: 'in_progress',
      actual_start_time: new Date()
    })

    res.json({
      success: true,
      message: '开始会议成功',
      data: meeting
    })
  } catch (error) {
    console.error('开始会议失败:', error)
    res.status(500).json({
      success: false,
      message: '开始会议失败',
      error: error.message
    })
  }
}

// 结束会议
const endMeeting = async (req, res) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    const meeting = await Meeting.findByPk(id)
    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: '会议不存在'
      })
    }

    // 检查权限
    if (meeting.organizer_id !== userId) {
      return res.status(403).json({
        success: false,
        message: '只有会议组织者可以结束会议'
      })
    }

    // 检查会议状态
    if (meeting.status !== 'in_progress') {
      return res.status(400).json({
        success: false,
        message: '只有进行中的会议可以结束'
      })
    }

    await meeting.update({
      status: 'completed',
      actual_end_time: new Date()
    })

    res.json({
      success: true,
      message: '结束会议成功',
      data: meeting
    })
  } catch (error) {
    console.error('结束会议失败:', error)
    res.status(500).json({
      success: false,
      message: '结束会议失败',
      error: error.message
    })
  }
}

// 获取会议室列表
const getMeetingRooms = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      keyword,
      capacity,
      status,
      floor
    } = req.query

    const where = { is_active: true }

    if (keyword) {
      where[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { code: { [Op.like]: `%${keyword}%` } },
        { location: { [Op.like]: `%${keyword}%` } }
      ]
    }

    if (capacity) {
      where.capacity = { [Op.gte]: parseInt(capacity) }
    }

    if (status) {
      where.status = status
    }

    if (floor) {
      where.floor = floor
    }

    const offset = (page - 1) * pageSize
    const limit = parseInt(pageSize)

    const result = await MeetingRoom.findAndCountAll({
      where,
      order: [['name', 'ASC']],
      offset,
      limit
    })

    res.json({
      success: true,
      data: result.rows,
      total: result.count,
      page: parseInt(page),
      pageSize: limit
    })
  } catch (error) {
    console.error('获取会议室列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取会议室列表失败',
      error: error.message
    })
  }
}

// 检查会议室可用性
const checkRoomAvailability = async (req, res) => {
  try {
    const { roomId } = req.params
    const { startTime, endTime, excludeMeetingId } = req.query

    if (!startTime || !endTime) {
      return res.status(400).json({
        success: false,
        message: '开始时间和结束时间为必填参数'
      })
    }

    const where = {
      meeting_room_id: roomId,
      status: ['scheduled', 'in_progress'],
      [Op.or]: [
        {
          start_time: {
            [Op.between]: [new Date(startTime), new Date(endTime)]
          }
        },
        {
          end_time: {
            [Op.between]: [new Date(startTime), new Date(endTime)]
          }
        },
        {
          [Op.and]: [
            { start_time: { [Op.lte]: new Date(startTime) } },
            { end_time: { [Op.gte]: new Date(endTime) } }
          ]
        }
      ]
    }

    if (excludeMeetingId) {
      where.id = { [Op.ne]: excludeMeetingId }
    }

    const conflictMeeting = await Meeting.findOne({ where })

    res.json({
      success: true,
      data: {
        available: !conflictMeeting,
        conflictMeeting: conflictMeeting ? {
          id: conflictMeeting.id,
          title: conflictMeeting.title,
          start_time: conflictMeeting.start_time,
          end_time: conflictMeeting.end_time
        } : null
      }
    })
  } catch (error) {
    console.error('检查会议室可用性失败:', error)
    res.status(500).json({
      success: false,
      message: '检查会议室可用性失败',
      error: error.message
    })
  }
}

// 回应会议邀请
const respondToInvitation = async (req, res) => {
  try {
    const { id } = req.params
    const { status, reason } = req.body
    const userId = req.user.id

    if (!['accepted', 'declined', 'tentative'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的回应状态'
      })
    }

    const participant = await MeetingParticipant.findOne({
      where: {
        meeting_id: id,
        user_id: userId
      }
    })

    if (!participant) {
      return res.status(404).json({
        success: false,
        message: '您未被邀请参加此会议'
      })
    }

    await participant.update({
      status,
      response_reason: reason,
      response_time: new Date()
    })

    res.json({
      success: true,
      message: '回应会议邀请成功',
      data: participant
    })
  } catch (error) {
    console.error('回应会议邀请失败:', error)
    res.status(500).json({
      success: false,
      message: '回应会议邀请失败',
      error: error.message
    })
  }
}

// 获取我的会议邀请
const getMyInvitations = async (req, res) => {
  try {
    const { status } = req.query
    const userId = req.user.id

    const where = { user_id: userId }
    if (status) {
      where.status = status
    }

    const invitations = await MeetingParticipant.findAll({
      where,
      include: [
        {
          model: Meeting,
          as: 'meeting',
          include: [
            {
              model: User,
              as: 'organizer',
              attributes: ['id', 'username', 'real_name', 'email']
            },
            {
              model: MeetingRoom,
              as: 'meetingRoom',
              attributes: ['id', 'name', 'location']
            }
          ]
        }
      ],
      order: [
        [{ model: Meeting, as: 'meeting' }, 'start_time', 'ASC']
      ]
    })

    res.json({
      success: true,
      data: invitations
    })
  } catch (error) {
    console.error('获取会议邀请失败:', error)
    res.status(500).json({
      success: false,
      message: '获取会议邀请失败',
      error: error.message
    })
  }
}

// 获取我组织的会议
const getMyOrganizedMeetings = async (req, res) => {
  try {
    const { page = 1, pageSize = 20, status } = req.query
    const userId = req.user.id

    const where = { organizer_id: userId }
    if (status) {
      where.status = status
    }

    const offset = (page - 1) * pageSize
    const limit = parseInt(pageSize)

    const result = await Meeting.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'organizer',
          attributes: ['id', 'username', 'real_name', 'email']
        },
        {
          model: MeetingRoom,
          as: 'meetingRoom',
          attributes: ['id', 'name', 'location', 'capacity']
        },
        {
          model: MeetingParticipant,
          as: 'participants',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'username', 'real_name', 'email']
            }
          ]
        }
      ],
      order: [['start_time', 'DESC']],
      offset,
      limit
    })

    res.json({
      success: true,
      data: result.rows,
      total: result.count,
      page: parseInt(page),
      pageSize: limit
    })
  } catch (error) {
    console.error('获取我组织的会议失败:', error)
    res.status(500).json({
      success: false,
      message: '获取我组织的会议失败',
      error: error.message
    })
  }
}

// 获取我参与的会议
const getMyParticipatingMeetings = async (req, res) => {
  try {
    const { page = 1, pageSize = 20, status } = req.query
    const userId = req.user.id

    const participantWhere = { user_id: userId }
    if (status) {
      participantWhere.status = status
    }

    const offset = (page - 1) * pageSize
    const limit = parseInt(pageSize)

    const result = await MeetingParticipant.findAndCountAll({
      where: participantWhere,
      include: [
        {
          model: Meeting,
          as: 'meeting',
          include: [
            {
              model: User,
              as: 'organizer',
              attributes: ['id', 'username', 'real_name', 'email']
            },
            {
              model: MeetingRoom,
              as: 'meetingRoom',
              attributes: ['id', 'name', 'location', 'capacity']
            }
          ]
        }
      ],
      order: [
        [{ model: Meeting, as: 'meeting' }, 'start_time', 'DESC']
      ],
      offset,
      limit
    })

    res.json({
      success: true,
      data: result.rows.map(p => p.meeting),
      total: result.count,
      page: parseInt(page),
      pageSize: limit
    })
  } catch (error) {
    console.error('获取我参与的会议失败:', error)
    res.status(500).json({
      success: false,
      message: '获取我参与的会议失败',
      error: error.message
    })
  }
}

// 获取会议统计
const getMeetingStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query
    const userId = req.user.id

    const dateWhere = {}
    if (startDate) {
      dateWhere.start_time = { [Op.gte]: new Date(startDate) }
    }
    if (endDate) {
      dateWhere.start_time = { ...dateWhere.start_time, [Op.lte]: new Date(endDate) }
    }

    // 总会议数
    const totalMeetings = await Meeting.count({
      where: dateWhere
    })

    // 我组织的会议数
    const myOrganizedMeetings = await Meeting.count({
      where: {
        ...dateWhere,
        organizer_id: userId
      }
    })

    // 我参与的会议数
    const myParticipatingMeetings = await MeetingParticipant.count({
      include: [
        {
          model: Meeting,
          as: 'meeting',
          where: dateWhere
        }
      ],
      where: {
        user_id: userId
      }
    })

    // 按状态统计
    const statusStats = await Meeting.findAll({
      where: dateWhere,
      attributes: [
        'status',
        [Meeting.sequelize.fn('COUNT', Meeting.sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    })

    res.json({
      success: true,
      data: {
        totalMeetings,
        myOrganizedMeetings,
        myParticipatingMeetings,
        statusStats: statusStats.reduce((acc, stat) => {
          acc[stat.status] = parseInt(stat.count)
          return acc
        }, {})
      }
    })
  } catch (error) {
    console.error('获取会议统计失败:', error)
    res.status(500).json({
      success: false,
      message: '获取会议统计失败',
      error: error.message
    })
  }
}

// 获取日历事件
const getCalendarEvents = async (req, res) => {
  try {
    const { startDate, endDate } = req.query
    const userId = req.user.id

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: '开始日期和结束日期为必填参数'
      })
    }

    // 获取用户参与的所有会议
    const meetings = await Meeting.findAll({
      where: {
        start_time: {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        },
        status: { [Op.ne]: 'cancelled' }
      },
      include: [
        {
          model: MeetingParticipant,
          as: 'participants',
          where: {
            user_id: userId
          },
          required: true
        },
        {
          model: User,
          as: 'organizer',
          attributes: ['id', 'username', 'real_name']
        },
        {
          model: MeetingRoom,
          as: 'meetingRoom',
          attributes: ['id', 'name', 'location']
        }
      ]
    })

    // 转换为日历事件格式
    const events = meetings.map(meeting => ({
      id: meeting.id,
      title: meeting.title,
      start: meeting.start_time,
      end: meeting.end_time,
      description: meeting.description,
      location: meeting.location || meeting.meetingRoom?.location,
      status: meeting.status,
      organizer: meeting.organizer,
      meetingRoom: meeting.meetingRoom,
      type: 'meeting'
    }))

    res.json({
      success: true,
      data: events
    })
  } catch (error) {
    console.error('获取日历事件失败:', error)
    res.status(500).json({
      success: false,
      message: '获取日历事件失败',
      error: error.message
    })
  }
}

module.exports = {
  getMeetings,
  getMeetingById,
  createMeeting,
  updateMeeting,
  cancelMeeting,
  startMeeting,
  endMeeting,
  getMeetingRooms,
  checkRoomAvailability,
  respondToInvitation,
  getMyInvitations,
  getMyOrganizedMeetings,
  getMyParticipatingMeetings,
  getMeetingStats,
  getCalendarEvents
} 