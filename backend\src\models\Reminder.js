const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Reminder = sequelize.define('Reminder', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    schedule_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'schedules',
        key: 'id'
      },
      comment: '关联的日程ID(可为空)'
    },
    meeting_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'meetings',
        key: 'id'
      },
      comment: '关联的会议ID(可为空)'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '提醒标题'
    },
    content: {
      type: DataTypes.TEXT,
      comment: '提醒内容'
    },
    remind_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '提醒时间'
    },
    type: {
      type: DataTypes.ENUM('schedule', 'meeting', 'task', 'birthday', 'deadline', 'custom'),
      defaultValue: 'custom',
      comment: '提醒类型'
    },
    method: {
      type: DataTypes.ENUM('popup', 'email', 'sms', 'push'),
      defaultValue: 'popup',
      comment: '提醒方式'
    },
    status: {
      type: DataTypes.ENUM('pending', 'sent', 'read', 'dismissed'),
      defaultValue: 'pending',
      comment: '提醒状态'
    },
    is_recurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否重复提醒'
    },
    recurrence_pattern: {
      type: DataTypes.TEXT,
      comment: '重复模式(JSON格式)'
    },
    sent_at: {
      type: DataTypes.DATE,
      comment: '发送时间'
    },
    read_at: {
      type: DataTypes.DATE,
      comment: '阅读时间'
    },
    dismissed_at: {
      type: DataTypes.DATE,
      comment: '忽略时间'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'reminders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '提醒表'
  });

  Reminder.associate = (models) => {
    // 关联用户
    Reminder.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });

    // 关联日程
    Reminder.belongsTo(models.Schedule, {
      foreignKey: 'schedule_id',
      as: 'schedule'
    });

    // 关联会议
    Reminder.belongsTo(models.Meeting, {
      foreignKey: 'meeting_id',
      as: 'meeting'
    });
  };

  return Reminder;
};
