"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _superPropSet;
var _set = require("./set.js");
var _getPrototypeOf = require("./getPrototypeOf.js");
function _superPropSet(classArg, property, value, receiver, isStrict, prototype) {
  return (0, _set.default)((0, _getPrototypeOf.default)(prototype ? classArg.prototype : classArg), property, value, receiver, isStrict);
}

//# sourceMappingURL=superPropSet.js.map
