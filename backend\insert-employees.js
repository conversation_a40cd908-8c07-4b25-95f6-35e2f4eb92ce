const { Sequelize } = require('sequelize');
const bcrypt = require('bcryptjs');

// 数据库配置
const sequelize = new Sequelize('smart_office_system', 'root', '123456', {
  host: 'localhost',
  dialect: 'mysql',
  logging: false,
  timezone: '+08:00'
});

// 定义模型
const User = sequelize.define('User', {
  id: { type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true },
  username: { type: Sequelize.STRING(50), allowNull: false, unique: true },
  email: { type: Sequelize.STRING(100), allowNull: false },
  password: { type: Sequelize.STRING(255), allowNull: false },
  real_name: { type: Sequelize.STRING(50), allowNull: false },
  phone: { type: Sequelize.STRING(20), allowNull: true },
  avatar: { type: Sequelize.STRING(255), allowNull: true },
  status: { type: Sequelize.ENUM('active', 'inactive'), defaultValue: 'active' }
}, { tableName: 'users', timestamps: true, underscored: true });

const Department = sequelize.define('Department', {
  id: { type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true },
  name: { type: Sequelize.STRING(100), allowNull: false },
  code: { type: Sequelize.STRING(50), allowNull: false, unique: true },
  description: { type: Sequelize.TEXT, allowNull: true },
  parent_id: { type: Sequelize.INTEGER, allowNull: true },
  is_active: { type: Sequelize.BOOLEAN, defaultValue: true }
}, { tableName: 'departments', timestamps: true, underscored: true });

const Position = sequelize.define('Position', {
  id: { type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true },
  name: { type: Sequelize.STRING(100), allowNull: false },
  level: { type: Sequelize.INTEGER, allowNull: false, defaultValue: 1 },
  description: { type: Sequelize.TEXT, allowNull: true },
  is_active: { type: Sequelize.BOOLEAN, defaultValue: true }
}, { tableName: 'positions', timestamps: true, underscored: true });

const Employee = sequelize.define('Employee', {
  id: { type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true },
  employee_code: { type: Sequelize.STRING(50), allowNull: false, unique: true },
  user_id: { type: Sequelize.INTEGER, allowNull: false },
  department_id: { type: Sequelize.INTEGER, allowNull: true },
  position_id: { type: Sequelize.INTEGER, allowNull: true },
  manager_id: { type: Sequelize.INTEGER, allowNull: true },
  hire_date: { type: Sequelize.DATE, allowNull: false },
  status: { type: Sequelize.ENUM('active', 'inactive', 'resigned'), defaultValue: 'active' },
  salary: { type: Sequelize.DECIMAL(10, 2), allowNull: true }
}, { tableName: 'employees', timestamps: true, underscored: true });

// 真实的员工数据
const employeeData = [
  // 技术部门
  { name: '张伟', position: '技术总监', department: '技术部', level: 1, phone: '13800138001', email: '<EMAIL>' },
  { name: '李娜', position: '高级前端工程师', department: '技术部', level: 3, phone: '13800138002', email: '<EMAIL>' },
  { name: '王强', position: '高级后端工程师', department: '技术部', level: 3, phone: '13800138003', email: '<EMAIL>' },
  { name: '刘芳', position: '前端工程师', department: '技术部', level: 4, phone: '13800138004', email: '<EMAIL>' },
  { name: '陈明', position: '后端工程师', department: '技术部', level: 4, phone: '13800138005', email: '<EMAIL>' },
  { name: '赵丽', position: '测试工程师', department: '技术部', level: 4, phone: '13800138006', email: '<EMAIL>' },
  { name: '孙杰', position: '运维工程师', department: '技术部', level: 4, phone: '13800138007', email: '<EMAIL>' },
  { name: '周敏', position: 'UI设计师', department: '技术部', level: 4, phone: '13800138008', email: '<EMAIL>' },
  { name: '吴涛', position: '产品经理', department: '技术部', level: 3, phone: '13800138009', email: '<EMAIL>' },
  { name: '郑红', position: '数据分析师', department: '技术部', level: 4, phone: '13800138010', email: '<EMAIL>' },
  
  // 销售部门
  { name: '马云飞', position: '销售总监', department: '销售部', level: 1, phone: '13800138011', email: '<EMAIL>' },
  { name: '林小雨', position: '销售经理', department: '销售部', level: 2, phone: '13800138012', email: '<EMAIL>' },
  { name: '黄志强', position: '高级销售', department: '销售部', level: 3, phone: '13800138013', email: '<EMAIL>' },
  { name: '许美丽', position: '销售代表', department: '销售部', level: 4, phone: '13800138014', email: '<EMAIL>' },
  { name: '谢文华', position: '销售代表', department: '销售部', level: 4, phone: '13800138015', email: '<EMAIL>' },
  { name: '袁晓东', position: '客户经理', department: '销售部', level: 3, phone: '13800138016', email: '<EMAIL>' },
  { name: '何丽娟', position: '销售助理', department: '销售部', level: 5, phone: '13800138017', email: '<EMAIL>' },
  { name: '邓建国', position: '区域经理', department: '销售部', level: 2, phone: '13800138018', email: '<EMAIL>' },
  { name: '冯雅琴', position: '销售代表', department: '销售部', level: 4, phone: '13800138019', email: '<EMAIL>' },
  { name: '曾志伟', position: '销售代表', department: '销售部', level: 4, phone: '13800138020', email: '<EMAIL>' },
  
  // 人事部门
  { name: '蒋美华', position: '人事总监', department: '人事部', level: 1, phone: '13800138021', email: '<EMAIL>' },
  { name: '韩雪梅', position: '招聘经理', department: '人事部', level: 2, phone: '13800138022', email: '<EMAIL>' },
  { name: '彭志华', position: '薪酬专员', department: '人事部', level: 4, phone: '13800138023', email: '<EMAIL>' },
  { name: '卢晓燕', position: '培训专员', department: '人事部', level: 4, phone: '13800138024', email: '<EMAIL>' },
  { name: '蔡建军', position: '人事专员', department: '人事部', level: 4, phone: '13800138025', email: '<EMAIL>' },
  
  // 财务部门
  { name: '梁秀英', position: '财务总监', department: '财务部', level: 1, phone: '13800138026', email: '<EMAIL>' },
  { name: '高建华', position: '会计经理', department: '财务部', level: 2, phone: '13800138027', email: '<EMAIL>' },
  { name: '田丽丽', position: '出纳', department: '财务部', level: 4, phone: '13800138028', email: '<EMAIL>' },
  { name: '董明珠', position: '成本会计', department: '财务部', level: 4, phone: '13800138029', email: '<EMAIL>' },
  { name: '范志强', position: '税务专员', department: '财务部', level: 4, phone: '13800138030', email: '<EMAIL>' },
  
  // 市场部门
  { name: '石小红', position: '市场总监', department: '市场部', level: 1, phone: '13800138031', email: '<EMAIL>' },
  { name: '罗建国', position: '品牌经理', department: '市场部', level: 2, phone: '13800138032', email: '<EMAIL>' },
  { name: '钟美玲', position: '市场专员', department: '市场部', level: 4, phone: '13800138033', email: '<EMAIL>' },
  { name: '汤志华', position: '活动策划', department: '市场部', level: 4, phone: '13800138034', email: '<EMAIL>' },
  { name: '贺晓东', position: '新媒体运营', department: '市场部', level: 4, phone: '13800138035', email: '<EMAIL>' }
];

// 生成更多员工数据
function generateMoreEmployees() {
  const departments = ['技术部', '销售部', '人事部', '财务部', '市场部', '运营部', '客服部'];
  const positions = ['专员', '主管', '经理', '高级专员', '助理', '顾问'];
  const surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗'];
  const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞'];
  
  const additionalEmployees = [];
  
  for (let i = employeeData.length; i < 100; i++) {
    const surname = surnames[Math.floor(Math.random() * surnames.length)];
    const name = names[Math.floor(Math.random() * names.length)];
    const fullName = surname + name;
    const department = departments[Math.floor(Math.random() * departments.length)];
    const position = positions[Math.floor(Math.random() * positions.length)];
    const level = Math.floor(Math.random() * 3) + 3; // 3-5级
    
    additionalEmployees.push({
      name: fullName,
      position: position,
      department: department,
      level: level,
      phone: `138${String(i).padStart(8, '0')}`,
      email: `${fullName.toLowerCase()}${i}@company.com`
    });
  }
  
  return [...employeeData, ...additionalEmployees];
}

async function insertEmployees() {
  try {
    console.log('🔄 连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    const allEmployees = generateMoreEmployees();
    console.log(`📊 准备插入 ${allEmployees.length} 个员工数据...`);

    // 创建部门
    const departments = ['技术部', '销售部', '人事部', '财务部', '市场部', '运营部', '客服部'];
    for (const deptName of departments) {
      await Department.findOrCreate({
        where: { name: deptName },
        defaults: {
          name: deptName,
          code: deptName.replace('部', '').toUpperCase(),
          description: `${deptName}门`,
          is_active: true
        }
      });
    }

    // 创建职位
    const positions = ['总监', '经理', '高级工程师', '工程师', '专员', '助理', '主管', '顾问'];
    for (let i = 0; i < positions.length; i++) {
      await Position.findOrCreate({
        where: { name: positions[i] },
        defaults: {
          name: positions[i],
          level: i + 1,
          description: `${positions[i]}职位`,
          is_active: true
        }
      });
    }

    let successCount = 0;
    
    for (const emp of allEmployees) {
      try {
        // 查找部门
        const department = await Department.findOne({ where: { name: emp.department } });
        const position = await Position.findOne({ where: { name: emp.position } });

        // 创建用户
        const hashedPassword = await bcrypt.hash('123456', 10);
        const username = `emp${String(successCount + 1).padStart(3, '0')}`;
        
        const user = await User.create({
          username: username,
          email: emp.email,
          password: hashedPassword,
          real_name: emp.name,
          phone: emp.phone,
          status: 'active'
        });

        // 创建员工
        await Employee.create({
          employee_code: `EMP${String(successCount + 1).padStart(3, '0')}`,
          user_id: user.id,
          department_id: department ? department.id : null,
          position_id: position ? position.id : null,
          hire_date: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          status: 'active',
          salary: Math.floor(Math.random() * 15000) + 5000
        });

        successCount++;
        if (successCount % 10 === 0) {
          console.log(`✅ 已插入 ${successCount} 个员工...`);
        }
      } catch (error) {
        console.log(`❌ 插入员工 ${emp.name} 失败:`, error.message);
      }
    }

    console.log(`🎉 员工数据插入完成！成功插入 ${successCount} 个员工`);
    
  } catch (error) {
    console.error('❌ 插入员工数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

insertEmployees();
