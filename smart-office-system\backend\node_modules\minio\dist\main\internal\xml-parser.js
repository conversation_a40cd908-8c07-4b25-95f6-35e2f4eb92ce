"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseBucketRegion = parseBucketRegion;
exports.parseError = parseError;
exports.parseInitiateMultipart = parseInitiateMultipart;
exports.parseListBucket = parseListBucket;
exports.parseListObjectsV2WithMetadata = parseListObjectsV2WithMetadata;
exports.parseListParts = parseListParts;
exports.parseReplicationConfig = parseReplicationConfig;
exports.parseResponseError = parseResponseError;
var _fastXmlParser = require("fast-xml-parser");
var errors = _interopRequireWildcard(require("../errors.js"), true);
var _helper = require("./helper.js");
var _response = require("./response.js");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// parse XML response for bucket region
function parseBucketRegion(xml) {
  // return region information
  return (0, _helper.parseXml)(xml).LocationConstraint;
}
const fxp = new _fastXmlParser.XMLParser();

// Parse XML and return information as Javascript types
// parse error XML response
function parseError(xml, headerInfo) {
  let xmlErr = {};
  const xmlObj = fxp.parse(xml);
  if (xmlObj.Error) {
    xmlErr = xmlObj.Error;
  }
  const e = new errors.S3Error();
  Object.entries(xmlErr).forEach(([key, value]) => {
    e[key.toLowerCase()] = value;
  });
  Object.entries(headerInfo).forEach(([key, value]) => {
    e[key] = value;
  });
  return e;
}

// Generates an Error object depending on http statusCode and XML body
async function parseResponseError(response) {
  const statusCode = response.statusCode;
  let code, message;
  if (statusCode === 301) {
    code = 'MovedPermanently';
    message = 'Moved Permanently';
  } else if (statusCode === 307) {
    code = 'TemporaryRedirect';
    message = 'Are you using the correct endpoint URL?';
  } else if (statusCode === 403) {
    code = 'AccessDenied';
    message = 'Valid and authorized credentials required';
  } else if (statusCode === 404) {
    code = 'NotFound';
    message = 'Not Found';
  } else if (statusCode === 405) {
    code = 'MethodNotAllowed';
    message = 'Method Not Allowed';
  } else if (statusCode === 501) {
    code = 'MethodNotAllowed';
    message = 'Method Not Allowed';
  } else {
    code = 'UnknownError';
    message = `${statusCode}`;
  }
  const headerInfo = {};
  // A value created by S3 compatible server that uniquely identifies the request.
  headerInfo.amzRequestid = response.headers['x-amz-request-id'];
  // A special token that helps troubleshoot API replies and issues.
  headerInfo.amzId2 = response.headers['x-amz-id-2'];

  // Region where the bucket is located. This header is returned only
  // in HEAD bucket and ListObjects response.
  headerInfo.amzBucketRegion = response.headers['x-amz-bucket-region'];
  const xmlString = await (0, _response.readAsString)(response);
  if (xmlString) {
    throw parseError(xmlString, headerInfo);
  }

  // Message should be instantiated for each S3Errors.
  const e = new errors.S3Error(message, {
    cause: headerInfo
  });
  // S3 Error code.
  e.code = code;
  Object.entries(headerInfo).forEach(([key, value]) => {
    // @ts-expect-error force set error properties
    e[key] = value;
  });
  throw e;
}

/**
 * parse XML response for list objects v2 with metadata in a bucket
 */
function parseListObjectsV2WithMetadata(xml) {
  const result = {
    objects: [],
    isTruncated: false,
    nextContinuationToken: ''
  };
  let xmlobj = (0, _helper.parseXml)(xml);
  if (!xmlobj.ListBucketResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListBucketResult"');
  }
  xmlobj = xmlobj.ListBucketResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextContinuationToken) {
    result.nextContinuationToken = xmlobj.NextContinuationToken;
  }
  if (xmlobj.Contents) {
    (0, _helper.toArray)(xmlobj.Contents).forEach(content => {
      const name = (0, _helper.sanitizeObjectKey)(content.Key);
      const lastModified = new Date(content.LastModified);
      const etag = (0, _helper.sanitizeETag)(content.ETag);
      const size = content.Size;
      let metadata;
      if (content.UserMetadata != null) {
        metadata = (0, _helper.toArray)(content.UserMetadata)[0];
      } else {
        metadata = null;
      }
      result.objects.push({
        name,
        lastModified,
        etag,
        size,
        metadata
      });
    });
  }
  if (xmlobj.CommonPrefixes) {
    (0, _helper.toArray)(xmlobj.CommonPrefixes).forEach(commonPrefix => {
      result.objects.push({
        prefix: (0, _helper.sanitizeObjectKey)((0, _helper.toArray)(commonPrefix.Prefix)[0]),
        size: 0
      });
    });
  }
  return result;
}
// parse XML response for list parts of an in progress multipart upload
function parseListParts(xml) {
  let xmlobj = (0, _helper.parseXml)(xml);
  const result = {
    isTruncated: false,
    parts: [],
    marker: 0
  };
  if (!xmlobj.ListPartsResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListPartsResult"');
  }
  xmlobj = xmlobj.ListPartsResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextPartNumberMarker) {
    result.marker = (0, _helper.toArray)(xmlobj.NextPartNumberMarker)[0] || '';
  }
  if (xmlobj.Part) {
    (0, _helper.toArray)(xmlobj.Part).forEach(p => {
      const part = parseInt((0, _helper.toArray)(p.PartNumber)[0], 10);
      const lastModified = new Date(p.LastModified);
      const etag = p.ETag.replace(/^"/g, '').replace(/"$/g, '').replace(/^&quot;/g, '').replace(/&quot;$/g, '').replace(/^&#34;/g, '').replace(/&#34;$/g, '');
      result.parts.push({
        part,
        lastModified,
        etag,
        size: parseInt(p.Size, 10)
      });
    });
  }
  return result;
}
function parseListBucket(xml) {
  let result = [];
  const parsedXmlRes = (0, _helper.parseXml)(xml);
  if (!parsedXmlRes.ListAllMyBucketsResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListAllMyBucketsResult"');
  }
  const {
    ListAllMyBucketsResult: {
      Buckets = {}
    } = {}
  } = parsedXmlRes;
  if (Buckets.Bucket) {
    result = (0, _helper.toArray)(Buckets.Bucket).map((bucket = {}) => {
      const {
        Name: bucketName,
        CreationDate
      } = bucket;
      const creationDate = new Date(CreationDate);
      return {
        name: bucketName,
        creationDate: creationDate
      };
    });
  }
  return result;
}
function parseInitiateMultipart(xml) {
  let xmlobj = (0, _helper.parseXml)(xml);
  if (!xmlobj.InitiateMultipartUploadResult) {
    throw new errors.InvalidXMLError('Missing tag: "InitiateMultipartUploadResult"');
  }
  xmlobj = xmlobj.InitiateMultipartUploadResult;
  if (xmlobj.UploadId) {
    return xmlobj.UploadId;
  }
  throw new errors.InvalidXMLError('Missing tag: "UploadId"');
}
function parseReplicationConfig(xml) {
  const xmlObj = (0, _helper.parseXml)(xml);
  const {
    Role,
    Rule
  } = xmlObj.ReplicationConfiguration;
  return {
    ReplicationConfiguration: {
      role: Role,
      rules: (0, _helper.toArray)(Rule)
    }
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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