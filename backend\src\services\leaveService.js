const { LeaveRequest, Employee, User } = require('../models');
const { ValidationError, BusinessError } = require('../shared/errors');
const logger = require('../shared/logger');

class LeaveService {
  // 提交请假申请
  async submitLeaveRequest(employeeId, data) {
    try {
      const {
        leaveType,
        startDate,
        endDate,
        startTime,
        endTime,
        reason,
        attachments
      } = data;

      // 检查员工是否存在
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new ValidationError('员工不存在');
      }

      // 验证请假日期
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (start > end) {
        throw new ValidationError('请假开始日期不能晚于结束日期');
      }

      // 计算请假天数
      const duration = this._calculateLeaveDuration(startDate, endDate, startTime, endTime);

      // 检查是否有重叠的请假申请
      const overlappingRequest = await this._checkOverlappingLeave(employeeId, startDate, endDate);
      if (overlappingRequest) {
        throw new BusinessError('该时间段已有请假申请，请检查后重新提交');
      }

      // 创建请假申请
      const leaveRequest = await LeaveRequest.create({
        employee_id: employeeId,
        leave_type: leaveType,
        start_date: startDate,
        end_date: endDate,
        start_time: startTime,
        end_time: endTime,
        duration,
        reason,
        attachments,
        status: 'pending'
      });

      logger.info(`员工 ${employeeId} 提交请假申请`, { employeeId, leaveRequestId: leaveRequest.id });

      return leaveRequest;
    } catch (error) {
      logger.error('提交请假申请失败:', error);
      throw error;
    }
  }

  // 获取请假申请列表
  async getLeaveRequests(options) {
    try {
      return await LeaveRequest.getLeaveRequests(options);
    } catch (error) {
      logger.error('获取请假申请列表失败:', error);
      throw error;
    }
  }

  // 获取待审批的请假申请
  async getPendingRequests(approverId) {
    try {
      return await LeaveRequest.getPendingRequests(approverId);
    } catch (error) {
      logger.error('获取待审批请假申请失败:', error);
      throw error;
    }
  }

  // 审批请假申请
  async approveLeaveRequest(requestId, approverId, data) {
    try {
      const { action, comments } = data; // action: 'approve' | 'reject'

      const leaveRequest = await LeaveRequest.findByPk(requestId);
      if (!leaveRequest) {
        throw new ValidationError('请假申请不存在');
      }

      if (leaveRequest.status !== 'pending') {
        throw new BusinessError('该请假申请已处理，无法重复操作');
      }

      // 检查审批人权限（这里简化处理，实际应该检查是否是员工的上级）
      const approver = await Employee.findByPk(approverId);
      if (!approver) {
        throw new ValidationError('审批人不存在');
      }

      let result;
      if (action === 'approve') {
        result = await leaveRequest.approve(approverId, comments);
        logger.info(`请假申请 ${requestId} 已批准`, { requestId, approverId });
      } else if (action === 'reject') {
        result = await leaveRequest.reject(approverId, comments);
        logger.info(`请假申请 ${requestId} 已拒绝`, { requestId, approverId });
      } else {
        throw new ValidationError('无效的审批操作');
      }

      return result;
    } catch (error) {
      logger.error('审批请假申请失败:', error);
      throw error;
    }
  }

  // 取消请假申请
  async cancelLeaveRequest(requestId, employeeId) {
    try {
      const leaveRequest = await LeaveRequest.findByPk(requestId);
      if (!leaveRequest) {
        throw new ValidationError('请假申请不存在');
      }

      // 验证是否是申请人本人
      if (leaveRequest.employee_id !== employeeId) {
        throw new ValidationError('只能取消自己的请假申请');
      }

      const result = await leaveRequest.cancel();
      logger.info(`请假申请 ${requestId} 已取消`, { requestId, employeeId });

      return result;
    } catch (error) {
      logger.error('取消请假申请失败:', error);
      throw error;
    }
  }

  // 获取员工请假统计
  async getLeaveStats(employeeId, year) {
    try {
      return await LeaveRequest.getLeaveStats(employeeId, year);
    } catch (error) {
      logger.error('获取请假统计失败:', error);
      throw error;
    }
  }

  // 获取部门请假汇总
  async getDepartmentLeaveStats(departmentId, startDate, endDate) {
    try {
      // 获取部门所有员工
      const employees = await Employee.findAll({
        where: { department_id: departmentId },
        include: [{
          model: User,
          as: 'user',
          attributes: ['real_name']
        }]
      });

      const stats = [];
      
      for (const employee of employees) {
        const leaveStats = await LeaveRequest.findAll({
          where: {
            employee_id: employee.id,
            status: 'approved',
            start_date: {
              [require('sequelize').Op.between]: [startDate, endDate]
            }
          }
        });

        const totalDays = leaveStats.reduce((sum, leave) => sum + parseFloat(leave.duration), 0);
        const leaveByType = {};
        
        leaveStats.forEach(leave => {
          const type = leave.leave_type;
          if (!leaveByType[type]) {
            leaveByType[type] = 0;
          }
          leaveByType[type] += parseFloat(leave.duration);
        });

        stats.push({
          employeeId: employee.id,
          employeeName: employee.user.real_name,
          totalLeaveDays: totalDays,
          leaveByType,
          leaveRequests: leaveStats.length
        });
      }

      return stats;
    } catch (error) {
      logger.error('获取部门请假汇总失败:', error);
      throw error;
    }
  }

  // 获取请假申请详情
  async getLeaveRequestDetail(requestId) {
    try {
      const leaveRequest = await LeaveRequest.findByPk(requestId, {
        include: [
          {
            model: Employee,
            as: 'employee',
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name', 'email']
            }]
          },
          {
            model: Employee,
            as: 'approver',
            required: false,
            include: [{
              model: User,
              as: 'user',
              attributes: ['real_name']
            }]
          }
        ]
      });

      if (!leaveRequest) {
        throw new ValidationError('请假申请不存在');
      }

      return leaveRequest;
    } catch (error) {
      logger.error('获取请假申请详情失败:', error);
      throw error;
    }
  }

  // 计算请假天数
  _calculateLeaveDuration(startDate, endDate, startTime, endTime) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // 如果是同一天且指定了具体时间，计算半天假
    if (start.getTime() === end.getTime() && startTime && endTime) {
      return 0.5;
    }
    
    // 计算完整天数（包含开始和结束日期）
    const timeDiff = end.getTime() - start.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
    
    return daysDiff;
  }

  // 检查是否有重叠的请假申请
  async _checkOverlappingLeave(employeeId, startDate, endDate) {
    const overlapping = await LeaveRequest.findOne({
      where: {
        employee_id: employeeId,
        status: ['pending', 'approved'],
        [require('sequelize').Op.or]: [
          {
            start_date: {
              [require('sequelize').Op.between]: [startDate, endDate]
            }
          },
          {
            end_date: {
              [require('sequelize').Op.between]: [startDate, endDate]
            }
          },
          {
            [require('sequelize').Op.and]: [
              {
                start_date: {
                  [require('sequelize').Op.lte]: startDate
                }
              },
              {
                end_date: {
                  [require('sequelize').Op.gte]: endDate
                }
              }
            ]
          }
        ]
      }
    });

    return overlapping;
  }

  // 获取员工可用假期余额（简化版，实际应该从HR系统获取）
  async getLeaveBalance(employeeId, year = new Date().getFullYear()) {
    try {
      const usedLeave = await LeaveRequest.getLeaveStats(employeeId, year);
      
      // 假设每个员工有以下假期额度
      const annualQuota = {
        annual: 10,    // 年假 10 天
        sick: 10,      // 病假 10 天
        personal: 5,   // 事假 5 天
        other: 2       // 其他假期 2 天
      };

      const balance = {};
      Object.keys(annualQuota).forEach(type => {
        balance[type] = {
          quota: annualQuota[type],
          used: usedLeave[type] || 0,
          remaining: annualQuota[type] - (usedLeave[type] || 0)
        };
      });

      return balance;
    } catch (error) {
      logger.error('获取假期余额失败:', error);
      throw error;
    }
  }
}

module.exports = new LeaveService(); 