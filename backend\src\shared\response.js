/**
 * 统一响应格式工具
 */

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} statusCode - HTTP状态码
 */
const success = (res, data = null, message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
};

/**
 * 分页成功响应
 * @param {Object} res - Express响应对象
 * @param {Array} data - 数据列表
 * @param {Object} pagination - 分页信息
 * @param {string} message - 响应消息
 */
const successWithPagination = (res, data, pagination, message = 'Success') => {
  const response = {
    success: true,
    message,
    data,
    pagination: {
      page: pagination.page || 1,
      pageSize: pagination.pageSize || 20,
      total: pagination.total || 0,
      totalPages: pagination.totalPages || 0
    },
    timestamp: new Date().toISOString()
  };

  return res.status(200).json(response);
};

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP状态码
 * @param {string} code - 错误代码
 * @param {*} details - 错误详情
 */
const error = (res, message = 'Internal Server Error', statusCode = 500, code = 'INTERNAL_ERROR', details = null) => {
  const response = {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: new Date().toISOString()
  };

  // 在开发环境中包含更多错误信息
  if (process.env.NODE_ENV === 'development' && details) {
    response.error.stack = details.stack;
  }

  return res.status(statusCode).json(response);
};

/**
 * 验证错误响应
 * @param {Object} res - Express响应对象
 * @param {Array|Object} errors - 验证错误
 */
const validationError = (res, errors) => {
  const response = {
    success: false,
    error: {
      code: 'VALIDATION_ERROR',
      message: 'Validation failed',
      details: errors
    },
    timestamp: new Date().toISOString()
  };

  return res.status(400).json(response);
};

/**
 * 未找到资源响应
 * @param {Object} res - Express响应对象
 * @param {string} resource - 资源名称
 */
const notFound = (res, resource = 'Resource') => {
  return error(res, `${resource} not found`, 404, 'NOT_FOUND');
};

/**
 * 未授权响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
const unauthorized = (res, message = 'Unauthorized') => {
  return error(res, message, 401, 'UNAUTHORIZED');
};

/**
 * 禁止访问响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
const forbidden = (res, message = 'Forbidden') => {
  return error(res, message, 403, 'FORBIDDEN');
};

/**
 * 冲突响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
const conflict = (res, message = 'Resource already exists') => {
  return error(res, message, 409, 'CONFLICT');
};

/**
 * 创建成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 创建的数据
 * @param {string} message - 响应消息
 */
const created = (res, data, message = 'Created successfully') => {
  return success(res, data, message, 201);
};

/**
 * 更新成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 更新的数据
 * @param {string} message - 响应消息
 */
const updated = (res, data, message = 'Updated successfully') => {
  return success(res, data, message, 200);
};

/**
 * 删除成功响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 响应消息
 */
const deleted = (res, message = 'Deleted successfully') => {
  return success(res, null, message, 200);
};

/**
 * 无内容响应
 * @param {Object} res - Express响应对象
 */
const noContent = (res) => {
  return res.status(204).send();
};

module.exports = {
  success,
  successWithPagination,
  error,
  validationError,
  notFound,
  unauthorized,
  forbidden,
  conflict,
  created,
  updated,
  deleted,
  noContent
};
