'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('employees', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      employee_no: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true,
        comment: '员工工号'
      },
      department_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      position_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'positions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      direct_supervisor_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'employees',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '直接上级ID'
      },
      employment_type: {
        type: Sequelize.ENUM('full_time', 'part_time', 'contract', 'intern'),
        allowNull: false,
        defaultValue: 'full_time'
      },
      hire_date: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '入职日期'
      },
      probation_end_date: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '试用期结束日期'
      },
      resignation_date: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '离职日期'
      },
      work_location: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '工作地点'
      },
      salary: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: '薪资'
      },
      emergency_contact_name: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '紧急联系人姓名'
      },
      emergency_contact_phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '紧急联系人电话'
      },
      emergency_contact_relationship: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '紧急联系人关系'
      },
      id_number: {
        type: Sequelize.STRING(30),
        allowNull: true,
        unique: true,
        comment: '身份证号'
      },
      bank_account: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '银行账号'
      },
      bank_name: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '开户银行'
      },
      status: {
        type: Sequelize.ENUM('active', 'probation', 'resigned', 'suspended'),
        defaultValue: 'probation',
        allowNull: false
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '备注信息'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('employees', ['user_id']);
    await queryInterface.addIndex('employees', ['employee_no']);
    await queryInterface.addIndex('employees', ['department_id']);
    await queryInterface.addIndex('employees', ['position_id']);
    await queryInterface.addIndex('employees', ['direct_supervisor_id']);
    await queryInterface.addIndex('employees', ['status']);
    await queryInterface.addIndex('employees', ['hire_date']);
    await queryInterface.addIndex('employees', ['id_number']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('employees');
  }
}; 