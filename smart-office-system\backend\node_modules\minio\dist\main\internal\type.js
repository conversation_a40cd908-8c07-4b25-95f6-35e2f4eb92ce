"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
// nodejs IncomingHttpHeaders is Record<string, string | string[]>, but it's actually this:
let ENCRYPTION_TYPES = /*#__PURE__*/function (ENCRYPTION_TYPES) {
  ENCRYPTION_TYPES["SSEC"] = "SSE-C";
  ENCRYPTION_TYPES["KMS"] = "KMS";
  return ENCRYPTION_TYPES;
}({});
exports.ENCRYPTION_TYPES = ENCRYPTION_TYPES;
let RETENTION_MODES = /*#__PURE__*/function (RETENTION_MODES) {
  RETENTION_MODES["GOVERNANCE"] = "GOVERNANCE";
  RETENTION_MODES["COMPLIANCE"] = "COMPLIANCE";
  return RETENTION_MODES;
}({});
exports.RETENTION_MODES = RETENTION_MODES;
let RETENTION_VALIDITY_UNITS = /*#__PURE__*/function (RETENTION_VALIDITY_UNITS) {
  RETENTION_VALIDITY_UNITS["DAYS"] = "Days";
  RETENTION_VALIDITY_UNITS["YEARS"] = "Years";
  return RETENTION_VALIDITY_UNITS;
}({});
exports.RETENTION_VALIDITY_UNITS = RETENTION_VALIDITY_UNITS;
let LEGAL_HOLD_STATUS = /*#__PURE__*/function (LEGAL_HOLD_STATUS) {
  LEGAL_HOLD_STATUS["ENABLED"] = "ON";
  LEGAL_HOLD_STATUS["DISABLED"] = "OFF";
  return LEGAL_HOLD_STATUS;
}({});
/* Replication Config types */
/* Replication Config types */
exports.LEGAL_HOLD_STATUS = LEGAL_HOLD_STATUS;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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