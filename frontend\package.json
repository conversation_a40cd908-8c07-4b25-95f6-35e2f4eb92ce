{"name": "smart-office-frontend", "version": "1.0.0", "private": true, "description": "智能办公系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.3", "axios": "^1.6.2", "@element-plus/icons-vue": "^2.1.0", "nprogress": "^0.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "@rushstack/eslint-patch": "^1.6.0", "@vue/eslint-config-prettier": "^8.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}