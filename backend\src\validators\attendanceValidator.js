const { body, query, param, validationResult } = require('express-validator');
const { error } = require('../shared/response');

// 验证结果处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ');
    return error(res, errorMessages, 400);
  }
  next();
};

// 考勤打卡验证
const validateAttendance = {
  // 打卡验证
  clockIn: [
    body('location')
      .optional()
      .isString()
      .withMessage('打卡地点必须是字符串'),
    body('latitude')
      .optional()
      .isFloat({ min: -90, max: 90 })
      .withMessage('纬度必须在-90到90之间'),
    body('longitude')
      .optional()
      .isFloat({ min: -180, max: 180 })
      .withMessage('经度必须在-180到180之间'),
    body('remarks')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500字符'),
    handleValidationErrors
  ],

  clockOut: [
    body('location')
      .optional()
      .isString()
      .withMessage('打卡地点必须是字符串'),
    body('latitude')
      .optional()
      .isFloat({ min: -90, max: 90 })
      .withMessage('纬度必须在-90到90之间'),
    body('longitude')
      .optional()
      .isFloat({ min: -180, max: 180 })
      .withMessage('经度必须在-180到180之间'),
    body('remarks')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500字符'),
    handleValidationErrors
  ]
};

// 请假申请验证
const validateLeaveRequest = {
  // 提交请假申请验证
  submit: [
    body('leaveType')
      .notEmpty()
      .withMessage('请假类型不能为空')
      .isIn(['annual', 'sick', 'personal', 'maternity', 'bereavement', 'other'])
      .withMessage('无效的请假类型'),
    body('startDate')
      .notEmpty()
      .withMessage('请假开始日期不能为空')
      .isISO8601()
      .withMessage('请假开始日期格式无效'),
    body('endDate')
      .notEmpty()
      .withMessage('请假结束日期不能为空')
      .isISO8601()
      .withMessage('请假结束日期格式无效'),
    body('startTime')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('开始时间格式无效（HH:MM）'),
    body('endTime')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('结束时间格式无效（HH:MM）'),
    body('reason')
      .notEmpty()
      .withMessage('请假原因不能为空')
      .isLength({ min: 5, max: 1000 })
      .withMessage('请假原因长度必须在5-1000字符之间'),
    body('attachments')
      .optional()
      .isArray()
      .withMessage('附件必须是数组格式'),
    // 自定义验证：检查日期逻辑
    body('endDate').custom((endDate, { req }) => {
      const startDate = new Date(req.body.startDate);
      const end = new Date(endDate);
      if (end < startDate) {
        throw new Error('结束日期不能早于开始日期');
      }
      return true;
    }),
    handleValidationErrors
  ],

  // 审批请假申请验证
  approve: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('请假申请ID必须是有效的整数'),
    body('action')
      .notEmpty()
      .withMessage('审批操作不能为空')
      .isIn(['approve', 'reject'])
      .withMessage('审批操作必须是approve或reject'),
    body('comments')
      .optional()
      .isString()
      .isLength({ max: 1000 })
      .withMessage('审批意见长度不能超过1000字符'),
    handleValidationErrors
  ]
};

// 查询参数验证
const validateQuery = {
  // 考勤记录查询验证
  attendanceRecords: [
    query('employeeId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('员工ID必须是有效的整数'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('开始日期格式无效'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('结束日期格式无效'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('clockType')
      .optional()
      .isIn(['clock_in', 'clock_out'])
      .withMessage('打卡类型必须是clock_in或clock_out'),
    handleValidationErrors
  ],

  // 考勤统计查询验证
  attendanceStats: [
    query('employeeId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('员工ID必须是有效的整数'),
    query('startDate')
      .notEmpty()
      .withMessage('开始日期不能为空')
      .isISO8601()
      .withMessage('开始日期格式无效'),
    query('endDate')
      .notEmpty()
      .withMessage('结束日期不能为空')
      .isISO8601()
      .withMessage('结束日期格式无效'),
    handleValidationErrors
  ],

  // 部门考勤汇总查询验证
  departmentStats: [
    query('departmentId')
      .notEmpty()
      .withMessage('部门ID不能为空')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是有效的整数'),
    query('date')
      .notEmpty()
      .withMessage('日期不能为空')
      .isISO8601()
      .withMessage('日期格式无效'),
    handleValidationErrors
  ],

  // 请假申请查询验证
  leaveRequests: [
    query('employeeId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('员工ID必须是有效的整数'),
    query('status')
      .optional()
      .isIn(['pending', 'approved', 'rejected', 'cancelled'])
      .withMessage('状态必须是pending、approved、rejected或cancelled'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('开始日期格式无效'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('结束日期格式无效'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    handleValidationErrors
  ],

  // 请假统计查询验证
  leaveStats: [
    query('employeeId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('员工ID必须是有效的整数'),
    query('year')
      .optional()
      .isInt({ min: 2000, max: 3000 })
      .withMessage('年份必须在2000-3000之间'),
    handleValidationErrors
  ]
};

module.exports = {
  validateAttendance,
  validateLeaveRequest,
  validateQuery
}; 