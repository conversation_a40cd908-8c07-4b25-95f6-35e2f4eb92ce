const fileService = require('../services/fileService');
const { ResponseHelper } = require('../shared/response');
const logger = require('../shared/logger');

class FileController {
  // 上传文件
  async uploadFile(req, res) {
    try {
      const userId = req.user.employeeId;
      const file = req.file;
      
      if (!file) {
        return ResponseHelper.error(res, '请选择要上传的文件', 400);
      }

      const options = {
        folderPath: req.body.folderPath || '/',
        description: req.body.description || '',
        isPublic: req.body.isPublic === 'true',
        tags: req.body.tags ? JSON.parse(req.body.tags) : []
      };

      const result = await fileService.uploadFile(
        file.buffer,
        file.originalname,
        userId,
        options
      );

      ResponseHelper.success(res, result, '文件上传成功');
    } catch (error) {
      logger.error('文件上传失败:', error);
      ResponseHelper.error(res, error.message || '文件上传失败');
    }
  }

  // 获取用户文件列表
  async getUserFiles(req, res) {
    try {
      const userId = req.user.employeeId;
      const {
        page = 1,
        pageSize = 20,
        folder,
        fileType,
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        folder,
        fileType,
        keyword,
        sortBy,
        sortOrder
      };

      const result = await fileService.getUserFiles(userId, options);
      
      ResponseHelper.success(res, result, '获取文件列表成功');
    } catch (error) {
      logger.error('获取文件列表失败:', error);
      ResponseHelper.error(res, error.message || '获取文件列表失败');
    }
  }

  // 获取共享文件列表
  async getSharedFiles(req, res) {
    try {
      const userId = req.user.employeeId;
      const {
        page = 1,
        pageSize = 20,
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        keyword,
        sortBy,
        sortOrder
      };

      const result = await fileService.getSharedFiles(userId, options);
      
      ResponseHelper.success(res, result, '获取共享文件列表成功');
    } catch (error) {
      logger.error('获取共享文件列表失败:', error);
      ResponseHelper.error(res, error.message || '获取共享文件列表失败');
    }
  }

  // 获取文件详情
  async getFileById(req, res) {
    try {
      const { fileId } = req.params;
      const result = await fileService.getFileById(fileId);
      
      ResponseHelper.success(res, result, '获取文件详情成功');
    } catch (error) {
      logger.error('获取文件详情失败:', error);
      ResponseHelper.error(res, error.message || '获取文件详情失败');
    }
  }

  // 获取文件下载URL
  async getDownloadUrl(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;
      const { expiresIn = 3600 } = req.query;

      const result = await fileService.getDownloadUrl(
        fileId,
        userId,
        parseInt(expiresIn)
      );
      
      ResponseHelper.success(res, result, '获取下载链接成功');
    } catch (error) {
      logger.error('获取下载链接失败:', error);
      ResponseHelper.error(res, error.message || '获取下载链接失败');
    }
  }

  // 删除文件
  async deleteFile(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;

      await fileService.deleteFile(fileId, userId);
      
      ResponseHelper.success(res, null, '文件删除成功');
    } catch (error) {
      logger.error('文件删除失败:', error);
      ResponseHelper.error(res, error.message || '文件删除失败');
    }
  }

  // 创建文件分享
  async createFileShare(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;
      const shareData = req.body;

      const result = await fileService.createFileShare(fileId, userId, shareData);
      
      ResponseHelper.success(res, result, '文件分享创建成功');
    } catch (error) {
      logger.error('创建文件分享失败:', error);
      ResponseHelper.error(res, error.message || '创建文件分享失败');
    }
  }

  // 通过分享令牌访问文件
  async accessSharedFile(req, res) {
    try {
      const { shareToken } = req.params;
      const { accessCode } = req.query;
      const userId = req.user ? req.user.employeeId : null;

      const result = await fileService.accessSharedFile(shareToken, accessCode, userId);
      
      ResponseHelper.success(res, result, '访问分享文件成功');
    } catch (error) {
      logger.error('访问分享文件失败:', error);
      ResponseHelper.error(res, error.message || '访问分享文件失败');
    }
  }

  // 获取分享文件的下载URL
  async getSharedFileDownloadUrl(req, res) {
    try {
      const { shareToken } = req.params;
      const { accessCode } = req.query;
      const userId = req.user ? req.user.employeeId : null;

      const result = await fileService.getSharedFileDownloadUrl(shareToken, accessCode, userId);
      
      ResponseHelper.success(res, result, '获取分享文件下载链接成功');
    } catch (error) {
      logger.error('获取分享文件下载链接失败:', error);
      ResponseHelper.error(res, error.message || '获取分享文件下载链接失败');
    }
  }

  // 获取用户的分享记录
  async getUserShares(req, res) {
    try {
      const userId = req.user.employeeId;
      const {
        page = 1,
        pageSize = 20,
        type = 'shared_by',
        keyword,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        type,
        keyword,
        sortBy,
        sortOrder
      };

      const result = await fileService.getUserShares(userId, options);
      
      ResponseHelper.success(res, result, '获取分享记录成功');
    } catch (error) {
      logger.error('获取分享记录失败:', error);
      ResponseHelper.error(res, error.message || '获取分享记录失败');
    }
  }

  // 停用分享
  async deactivateShare(req, res) {
    try {
      const { shareId } = req.params;
      const userId = req.user.employeeId;

      await fileService.deactivateShare(shareId, userId);
      
      ResponseHelper.success(res, null, '分享已停用');
    } catch (error) {
      logger.error('停用分享失败:', error);
      ResponseHelper.error(res, error.message || '停用分享失败');
    }
  }

  // 更新文件信息
  async updateFile(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;
      const updateData = req.body;

      const result = await fileService.updateFile(fileId, userId, updateData);
      
      ResponseHelper.success(res, result, '文件信息更新成功');
    } catch (error) {
      logger.error('更新文件信息失败:', error);
      ResponseHelper.error(res, error.message || '更新文件信息失败');
    }
  }

  // 创建文件新版本
  async createFileVersion(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;
      const file = req.file;
      const { versionNote = '' } = req.body;

      if (!file) {
        return ResponseHelper.error(res, '请选择要上传的新版本文件', 400);
      }

      const result = await fileService.createFileVersion(
        fileId,
        userId,
        file.buffer,
        versionNote
      );
      
      ResponseHelper.success(res, result, '文件新版本创建成功');
    } catch (error) {
      logger.error('创建文件新版本失败:', error);
      ResponseHelper.error(res, error.message || '创建文件新版本失败');
    }
  }

  // 获取文件统计信息
  async getFileStats(req, res) {
    try {
      const userId = req.user.employeeId;
      const result = await fileService.getFileStats(userId);
      
      ResponseHelper.success(res, result, '获取文件统计信息成功');
    } catch (error) {
      logger.error('获取文件统计信息失败:', error);
      ResponseHelper.error(res, error.message || '获取文件统计信息失败');
    }
  }

  // 搜索文件
  async searchFiles(req, res) {
    try {
      const userId = req.user.employeeId;
      const { keyword } = req.query;
      
      if (!keyword) {
        return ResponseHelper.error(res, '请提供搜索关键词', 400);
      }

      const {
        page = 1,
        pageSize = 20,
        folder,
        fileType,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        folder,
        fileType,
        sortBy,
        sortOrder
      };

      const result = await fileService.searchFiles(userId, keyword, options);
      
      ResponseHelper.success(res, result, '搜索文件成功');
    } catch (error) {
      logger.error('搜索文件失败:', error);
      ResponseHelper.error(res, error.message || '搜索文件失败');
    }
  }

  // 获取文件预览信息
  async getFilePreview(req, res) {
    try {
      const { fileId } = req.params;
      const userId = req.user.employeeId;

      const result = await fileService.getFilePreview(fileId, userId);
      
      ResponseHelper.success(res, result, '获取文件预览信息成功');
    } catch (error) {
      logger.error('获取文件预览信息失败:', error);
      ResponseHelper.error(res, error.message || '获取文件预览信息失败');
    }
  }

  // 清理过期分享（管理员功能）
  async cleanupExpiredShares(req, res) {
    try {
      // 检查管理员权限
      if (!req.user.roles.includes('admin')) {
        return ResponseHelper.error(res, '无权限执行此操作', 403);
      }

      const cleanedCount = await fileService.cleanupExpiredShares();
      
      ResponseHelper.success(res, { cleanedCount }, '过期分享清理完成');
    } catch (error) {
      logger.error('清理过期分享失败:', error);
      ResponseHelper.error(res, error.message || '清理过期分享失败');
    }
  }

  // 批量删除文件
  async batchDeleteFiles(req, res) {
    try {
      const userId = req.user.employeeId;
      const { fileIds } = req.body;

      if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
        return ResponseHelper.error(res, '请提供要删除的文件ID列表', 400);
      }

      const results = [];
      for (const fileId of fileIds) {
        try {
          await fileService.deleteFile(fileId, userId);
          results.push({ fileId, success: true });
        } catch (error) {
          results.push({ fileId, success: false, error: error.message });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      ResponseHelper.success(res, {
        results,
        summary: {
          total: fileIds.length,
          success: successCount,
          failed: failCount
        }
      }, `批量删除完成：成功 ${successCount} 个，失败 ${failCount} 个`);
    } catch (error) {
      logger.error('批量删除文件失败:', error);
      ResponseHelper.error(res, error.message || '批量删除文件失败');
    }
  }

  // 移动文件到文件夹
  async moveFiles(req, res) {
    try {
      const userId = req.user.employeeId;
      const { fileIds, targetFolder } = req.body;

      if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
        return ResponseHelper.error(res, '请提供要移动的文件ID列表', 400);
      }

      if (!targetFolder) {
        return ResponseHelper.error(res, '请提供目标文件夹路径', 400);
      }

      const results = [];
      for (const fileId of fileIds) {
        try {
          const result = await fileService.updateFile(fileId, userId, {
            folderPath: targetFolder
          });
          results.push({ fileId, success: true, file: result });
        } catch (error) {
          results.push({ fileId, success: false, error: error.message });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      ResponseHelper.success(res, {
        results,
        summary: {
          total: fileIds.length,
          success: successCount,
          failed: failCount
        }
      }, `文件移动完成：成功 ${successCount} 个，失败 ${failCount} 个`);
    } catch (error) {
      logger.error('移动文件失败:', error);
      ResponseHelper.error(res, error.message || '移动文件失败');
    }
  }
}

module.exports = new FileController(); 