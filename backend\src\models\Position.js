const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Position extends Model {
    static associate(models) {
      // 关联到员工
      Position.hasMany(models.Employee, {
        foreignKey: 'position_id',
        as: 'employees'
      });

      // 关联到部门（职位可能属于特定部门）
      Position.belongsTo(models.Department, {
        foreignKey: 'department_id',
        as: 'department'
      });
    }

    // 静态方法：获取职位列表
    static async getPositionList(options = {}) {
      const {
        page = 1,
        pageSize = 20,
        departmentId,
        level,
        status,
        keyword
      } = options;

      const where = {};
      const include = [
        {
          model: sequelize.models.Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ];

      // 部门筛选
      if (departmentId) {
        where.department_id = departmentId;
      }

      // 级别筛选
      if (level) {
        where.level = level;
      }

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 关键词搜索
      if (keyword) {
        where[sequelize.Sequelize.Op.or] = [
          { name: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { code: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } },
          { description: { [sequelize.Sequelize.Op.like]: `%${keyword}%` } }
        ];
      }

      const offset = (page - 1) * pageSize;

      const result = await this.findAndCountAll({
        where,
        include,
        limit: pageSize,
        offset,
        order: [['level', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']]
      });

      return {
        positions: result.rows,
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    }

    // 静态方法：根据部门获取职位
    static async getPositionsByDepartment(departmentId) {
      return this.findAll({
        where: {
          [sequelize.Sequelize.Op.or]: [
            { department_id: departmentId },
            { department_id: null } // 通用职位
          ]
        },
        order: [['level', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']]
      });
    }

    // 静态方法：获取所有职位的简单列表（用于下拉选择）
    static async getAllPositionsForSelect() {
      return this.findAll({
        where: { status: 'active' },
        attributes: ['id', 'name', 'level', 'department_id'],
        include: [{
          model: sequelize.models.Department,
          as: 'department',
          attributes: ['name']
        }],
        order: [['level', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']]
      });
    }

    // 实例方法：检查是否可以删除职位
    async canDelete() {
      // 检查是否有员工使用该职位
      const employeeCount = await sequelize.models.Employee.count({
        where: { position_id: this.id }
      });

      if (employeeCount > 0) {
        return { canDelete: false, reason: '该职位下还有员工，无法删除' };
      }

      return { canDelete: true };
    }

    // 静态方法：生成职位编码
    static async generatePositionCode(departmentId = null) {
      let prefix = 'P';
      
      if (departmentId) {
        const department = await sequelize.models.Department.findByPk(departmentId);
        if (department) {
          prefix = `P${department.code}`;
        }
      }

      // 查找相同前缀的最大编号
      const lastPosition = await this.findOne({
        where: {
          code: {
            [sequelize.Sequelize.Op.like]: `${prefix}%`
          }
        },
        order: [['code', 'DESC']]
      });

      let nextNumber = 1;
      if (lastPosition) {
        const match = lastPosition.code.match(/(\d+)$/);
        if (match) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
    }

    // 重写toJSON方法，格式化输出
    toJSON() {
      const values = Object.assign({}, this.get());
      
      // 添加级别名称
      const levelNames = {
        1: '初级',
        2: '中级',
        3: '高级',
        4: '主管',
        5: '经理',
        6: '总监',
        7: '副总',
        8: '总裁'
      };
      
      values.level_name = levelNames[values.level] || '未知';

      return values;
    }
  }

  Position.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '职位名称'
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: true,  // 暂时允许空值避免同步错误
      unique: false,    // 暂时取消唯一约束
      comment: '职位编码'
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id'
      },
      comment: '所属部门ID（null表示通用职位）'
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '职位级别（1-8）'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '职位描述'
    },
    responsibilities: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '职位职责'
    },
    requirements: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '任职要求'
    },
    min_salary: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '最低薪资'
    },
    max_salary: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: '最高薪资'
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序顺序'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active',
      comment: '职位状态'
    },
    is_management: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否为管理岗位'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Position',
    tableName: 'positions',
    timestamps: true,
    underscored: true,
    indexes: [
      { fields: ['code'] },
      { fields: ['department_id'] },
      { fields: ['level'] },
      { fields: ['status'] },
      { fields: ['sort_order'] }
    ]
  });

  return Position;
}; 