const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const MeetingParticipant = sequelize.define('MeetingParticipant', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    meeting_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'meetings',
        key: 'id'
      },
      comment: '会议ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    role: {
      type: DataTypes.ENUM('organizer', 'presenter', 'attendee', 'observer'),
      defaultValue: 'attendee',
      comment: '参与角色'
    },
    status: {
      type: DataTypes.ENUM('invited', 'accepted', 'declined', 'tentative', 'no_response'),
      defaultValue: 'invited',
      comment: '邀请状态'
    },
    response_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '回复时间'
    },
    response_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '回复原因'
    },
    is_optional: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否可选参与'
    },
    check_in_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '签到时间'
    },
    check_out_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '签退时间'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    }
  }, {
    tableName: 'meeting_participants',
    timestamps: true,
    underscored: true,
    comment: '会议参与者表',
    indexes: [
      {
        fields: ['meeting_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['meeting_id', 'user_id'],
        unique: true
      }
    ]
  });

  MeetingParticipant.associate = (models) => {
    // 会议关联
    MeetingParticipant.belongsTo(models.Meeting, {
      foreignKey: 'meeting_id',
      as: 'meeting'
    });

    // 用户关联
    MeetingParticipant.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  return MeetingParticipant;
}; 