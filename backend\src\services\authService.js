const bcrypt = require('bcryptjs');
const { User, Role, UserRole } = require('../models');
const tokenService = require('./tokenService');

class AuthService {
  /**
   * 用户注册
   * @param {Object} userData - 用户注册数据
   * @returns {Object} 注册结果
   */
  async register(userData) {
    const { username, email, password, real_name, phone } = userData;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({
      where: { username }
    });

    if (existingUser) {
      throw new Error('用户名已存在');
    }

    // 检查邮箱是否已存在
    const existingEmail = await User.findOne({
      where: { email }
    });

    if (existingEmail) {
      throw new Error('邮箱已被注册');
    }

    // 检查手机号是否已存在（如果提供）
    if (phone) {
      const existingPhone = await User.findOne({
        where: { phone }
      });

      if (existingPhone) {
        throw new Error('手机号已被注册');
      }
    }

    // 创建用户
    const user = await User.create({
      username,
      email,
      password, // 密码会在模型的beforeCreate钩子中自动加密
      real_name,
      phone,
      status: 'active'
    });

    // 为新用户分配默认角色（普通员工）
    const employeeRole = await Role.findOne({ where: { code: 'EMPLOYEE' } });
    if (employeeRole) {
      await UserRole.assignRole(user.id, employeeRole.id);
    }

    // 重新查询用户信息（包含角色）
    const userWithRoles = await User.findByPk(user.id, {
      include: [{
        model: Role,
        as: 'roles',
        through: { attributes: [] }
      }],
      attributes: { exclude: ['password'] }
    });

    return {
      user: userWithRoles,
      message: '注册成功'
    };
  }

  /**
   * 用户登录
   * @param {string} credential - 用户名或邮箱
   * @param {string} password - 密码
   * @param {string} ip - 登录IP地址
   * @returns {Object} 登录结果
   */
  async login(credential, password, ip = null) {
    console.log('🔍 AuthService.login called:', { credential, hasPassword: !!password });

    // 查找用户（支持用户名或邮箱登录）
    console.log('🔍 Calling User.findByCredentials with credential:', credential);

    let user = null;
    let directUser = null;

    try {
      user = await User.findByCredentials(credential);
      console.log('🔍 User found:', user ? { id: user.id, username: user.username, status: user.status } : 'null');

      // 如果没找到用户，尝试直接查询
      if (!user) {
        console.log('🔍 Trying direct query...');
        directUser = await User.findOne({
          where: {
            username: credential,
            status: 'active'
          }
        });
        console.log('🔍 Direct query result:', directUser ? { id: directUser.id, username: directUser.username } : 'null');

        // 查询所有用户看看数据库中有什么
        const allUsers = await User.findAll({
          attributes: ['id', 'username', 'email', 'status'],
          limit: 10
        });
        console.log('🔍 All users in database:', allUsers.map(u => ({ id: u.id, username: u.username, status: u.status })));
      }
    } catch (error) {
      console.error('🔍 Error in User.findByCredentials:', error);
      throw error;
    }

    const finalUser = user || directUser;

    if (!finalUser) {
      console.log('❌ User not found for credential:', credential);
      throw new Error('用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await finalUser.validatePassword(password);
    console.log('🔍 Password validation:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('❌ Password validation failed');
      throw new Error('用户名或密码错误');
    }

    // 检查用户状态
    if (finalUser.status !== 'active') {
      console.log('❌ User status is not active:', finalUser.status);
      throw new Error('账号已被禁用，请联系管理员');
    }

    // 更新登录信息
    await finalUser.updateLoginInfo(ip);

    // 生成令牌
    const tokens = tokenService.generateTokenPair(finalUser);
    console.log('✅ Login successful for user:', finalUser.username);

    return {
      user: finalUser.toJSON(),
      tokens,
      message: '登录成功'
    };
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Object} 新的令牌对
   */
  async refreshToken(refreshToken) {
    try {
      // 验证刷新令牌
      const decoded = tokenService.verifyRefreshToken(refreshToken);

      // 查找用户
      const user = await User.findByPk(decoded.id, {
        include: [{
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        }],
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      if (user.status !== 'active') {
        throw new Error('用户账号已被禁用');
      }

      // 生成新的令牌对
      const tokens = tokenService.generateTokenPair(user);

      return {
        tokens,
        message: '令牌刷新成功'
      };
    } catch (error) {
      throw new Error('刷新令牌无效或已过期');
    }
  }

  /**
   * 更改密码
   * @param {number} userId - 用户ID
   * @param {string} currentPassword - 当前密码
   * @param {string} newPassword - 新密码
   * @returns {Object} 更改结果
   */
  async changePassword(userId, currentPassword, newPassword) {
    const user = await User.findByPk(userId);

    if (!user) {
      throw new Error('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await user.validatePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      throw new Error('当前密码错误');
    }

    // 更新密码
    await user.updatePassword(newPassword);

    return {
      message: '密码更改成功'
    };
  }

  /**
   * 请求重置密码
   * @param {string} email - 用户邮箱
   * @returns {Object} 重置结果
   */
  async requestPasswordReset(email) {
    const user = await User.findOne({ where: { email } });

    if (!user) {
      // 为了安全，不暴露用户是否存在
      return {
        message: '如果该邮箱已注册，将收到重置密码的邮件'
      };
    }

    // 生成重置令牌
    const resetToken = await user.generateResetToken();

    // TODO: 发送重置密码邮件
    // await emailService.sendPasswordResetEmail(user.email, resetToken);

    return {
      message: '重置密码邮件已发送',
      resetToken // 开发环境下返回令牌，生产环境应删除
    };
  }

  /**
   * 重置密码
   * @param {string} resetToken - 重置令牌
   * @param {string} newPassword - 新密码
   * @returns {Object} 重置结果
   */
  async resetPassword(resetToken, newPassword) {
    try {
      // 验证重置令牌
      const decoded = tokenService.verifyResetPasswordToken(resetToken);

      // 查找用户
      const user = await User.findByPk(decoded.id);

      if (!user) {
        throw new Error('用户不存在');
      }

      // 检查令牌是否匹配
      if (user.reset_password_token !== resetToken) {
        throw new Error('重置令牌无效');
      }

      // 检查令牌是否过期
      if (user.reset_password_expires && new Date() > user.reset_password_expires) {
        throw new Error('重置令牌已过期');
      }

      // 更新密码并清除重置令牌
      await user.update({
        password: newPassword, // 会在beforeUpdate钩子中自动加密
        reset_password_token: null,
        reset_password_expires: null
      });

      return {
        message: '密码重置成功'
      };
    } catch (error) {
      throw new Error('重置令牌无效或已过期');
    }
  }

  /**
   * 获取用户信息
   * @param {number} userId - 用户ID
   * @returns {Object} 用户信息
   */
  async getUserProfile(userId) {
    const user = await User.findByPk(userId, {
      include: [{
        model: Role,
        as: 'roles',
        through: { attributes: [] }
      }],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    return user;
  }

  /**
   * 更新用户信息
   * @param {number} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新结果
   */
  async updateUserProfile(userId, updateData) {
    const user = await User.findByPk(userId);

    if (!user) {
      throw new Error('用户不存在');
    }

    // 过滤允许更新的字段
    const allowedFields = ['real_name', 'phone', 'avatar', 'gender', 'birth_date'];
    const filteredData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    // 检查手机号是否重复
    if (filteredData.phone) {
      const existingPhone = await User.findOne({
        where: { 
          phone: filteredData.phone,
          id: { [require('sequelize').Op.ne]: userId }
        }
      });

      if (existingPhone) {
        throw new Error('手机号已被使用');
      }
    }

    await user.update(filteredData);

    // 返回更新后的用户信息
    const updatedUser = await User.findByPk(userId, {
      include: [{
        model: Role,
        as: 'roles',
        through: { attributes: [] }
      }],
      attributes: { exclude: ['password'] }
    });

    return {
      user: updatedUser,
      message: '用户信息更新成功'
    };
  }

  /**
   * 验证邮箱
   * @param {string} verificationToken - 验证令牌
   * @returns {Object} 验证结果
   */
  async verifyEmail(verificationToken) {
    // TODO: 实现邮箱验证逻辑
    return {
      message: '邮箱验证成功'
    };
  }
}

module.exports = new AuthService(); 