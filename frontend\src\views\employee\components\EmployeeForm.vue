<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑员工' : '新增员工'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工号" prop="employee_code">
            <el-input v-model="formData.employee_code" placeholder="请输入工号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门" prop="department_id">
            <el-select v-model="formData.department_id" placeholder="请选择部门" style="width: 100%">
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              已加载 {{ departments.length }} 个部门
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="position_id">
            <el-select v-model="formData.position_id" placeholder="请选择职位" style="width: 100%">
              <el-option
                v-for="pos in positions"
                :key="pos.id"
                :label="pos.name"
                :value="pos.id"
              />
            </el-select>
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              已加载 {{ positions.length }} 个职位
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="formData.gender">
              <el-radio label="male">男</el-radio>
              <el-radio label="female">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="在职" value="active" />
              <el-option label="离职" value="inactive" />
              <el-option label="休假" value="leave" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="入职日期" prop="hire_date">
        <el-date-picker
          v-model="formData.hire_date"
          type="date"
          placeholder="请选择入职日期"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useEmployeeStore } from '@/stores/employee'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  employeeData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Store
const employeeStore = useEmployeeStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const departments = ref([])
const positions = ref([])

const formData = reactive({
  name: '',
  employee_code: '',
  department_id: '',
  position_id: '',
  email: '',
  phone: '',
  gender: 'male',
  status: 'active',
  hire_date: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  employee_code: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  position_id: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

// 监听员工数据变化
watch(() => props.employeeData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    name: '',
    employee_code: '',
    department_id: '',
    position_id: '',
    email: '',
    phone: '',
    gender: 'male',
    status: 'active',
    hire_date: ''
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 构造员工数据
    const employeeData = {
      real_name: formData.name,
      username: formData.employee_code || formData.name.toLowerCase().replace(/\s+/g, ''),
      email: formData.email,
      phone: formData.phone,
      gender: formData.gender,
      employee_code: formData.employee_code,
      department_id: formData.department_id,
      position_id: formData.position_id,
      hire_date: formData.hire_date
    }

    // 触发成功事件，让父组件处理具体的提交逻辑
    emit('success', employeeData)

    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载部门和职位数据
const loadDepartmentsAndPositions = async () => {
  try {
    console.log('🔄 开始加载部门和职位数据...')
    const [deptData, posData] = await Promise.all([
      employeeStore.fetchDepartments(),
      employeeStore.fetchPositions()
    ])
    departments.value = deptData || []
    positions.value = posData || []
    console.log('✅ 部门数据加载完成:', departments.value.length, '个部门')
    console.log('✅ 职位数据加载完成:', positions.value.length, '个职位')
    console.log('部门列表:', departments.value)
    console.log('职位列表:', positions.value)
  } catch (error) {
    console.error('❌ 加载部门和职位数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadDepartmentsAndPositions()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
