const { body, param, query, validationResult } = require('express-validator');
const { ValidationError } = require('../shared/errors');

// 错误处理函数
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    throw new ValidationError(errorMessages.join('; '));
  }
  next();
};

// 工作流定义验证
const validateWorkflowDefinition = [
  body('name')
    .notEmpty()
    .withMessage('工作流名称不能为空')
    .isLength({ min: 2, max: 100 })
    .withMessage('工作流名称长度应在2-100字符之间'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('工作流描述长度不能超过500字符'),
  
  body('category')
    .notEmpty()
    .withMessage('工作流分类不能为空')
    .isIn(['leave', 'expense', 'purchase', 'meeting', 'general'])
    .withMessage('无效的工作流分类'),
  
  body('form_config')
    .optional()
    .isObject()
    .withMessage('表单配置必须是有效的JSON对象'),
  
  body('nodes')
    .optional()
    .isArray()
    .withMessage('节点配置必须是数组'),
  
  body('nodes.*.name')
    .optional()
    .notEmpty()
    .withMessage('节点名称不能为空'),
  
  body('nodes.*.type')
    .optional()
    .isIn(['start', 'approval', 'condition', 'notification', 'end'])
    .withMessage('无效的节点类型'),
  
  handleValidationErrors
];

// 工作流实例验证
const validateWorkflowInstance = [
  body('workflowDefinitionId')
    .notEmpty()
    .withMessage('工作流定义ID不能为空')
    .isInt({ min: 1 })
    .withMessage('工作流定义ID必须是正整数'),
  
  body('title')
    .notEmpty()
    .withMessage('工作流标题不能为空')
    .isLength({ min: 2, max: 200 })
    .withMessage('工作流标题长度应在2-200字符之间'),
  
  body('business_key')
    .optional()
    .isLength({ max: 100 })
    .withMessage('业务键长度不能超过100字符'),
  
  body('form_data')
    .optional()
    .isObject()
    .withMessage('表单数据必须是有效的JSON对象'),
  
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('无效的优先级'),
  
  handleValidationErrors
];

// 审批任务验证
const validateApprovalTask = [
  body('action')
    .notEmpty()
    .withMessage('审批操作不能为空')
    .isIn(['approve', 'reject'])
    .withMessage('无效的审批操作'),
  
  body('comment')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('审批意见长度不能超过1000字符'),
  
  body('attachments')
    .optional()
    .isArray()
    .withMessage('附件必须是数组'),
  
  body('attachments.*.filename')
    .optional()
    .notEmpty()
    .withMessage('附件文件名不能为空'),
  
  body('attachments.*.url')
    .optional()
    .isURL()
    .withMessage('附件URL格式无效'),
  
  handleValidationErrors
];

// 查询参数验证
const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式无效'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式无效'),
  
  handleValidationErrors
];

// ID参数验证
const validateIdParam = (paramName = 'id') => [
  param(paramName)
    .isInt({ min: 1 })
    .withMessage(`${paramName}必须是正整数`),
  
  handleValidationErrors
];

// 员工创建验证
const validateEmployeeCreation = [
  body('employee_number')
    .notEmpty()
    .withMessage('员工编号不能为空')
    .isLength({ min: 3, max: 20 })
    .withMessage('员工编号长度应在3-20字符之间'),
  
  body('real_name')
    .notEmpty()
    .withMessage('姓名不能为空')
    .isLength({ min: 2, max: 50 })
    .withMessage('姓名长度应在2-50字符之间'),
  
  body('department')
    .notEmpty()
    .withMessage('部门不能为空'),
  
  body('position')
    .notEmpty()
    .withMessage('职位不能为空'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式无效'),
  
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('手机号格式无效'),
  
  handleValidationErrors
];

// 考勤记录验证
const validateAttendanceRecord = [
  body('clock_type')
    .notEmpty()
    .withMessage('打卡类型不能为空')
    .isIn(['in', 'out'])
    .withMessage('无效的打卡类型'),
  
  body('location')
    .optional()
    .isObject()
    .withMessage('位置信息必须是有效的JSON对象'),
  
  body('location.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('纬度值必须在-90到90之间'),
  
  body('location.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('经度值必须在-180到180之间'),
  
  handleValidationErrors
];

// 请假申请验证
const validateLeaveRequest = [
  body('leave_type')
    .notEmpty()
    .withMessage('请假类型不能为空')
    .isIn(['annual', 'sick', 'personal', 'maternity', 'other'])
    .withMessage('无效的请假类型'),
  
  body('start_date')
    .notEmpty()
    .withMessage('开始日期不能为空')
    .isISO8601()
    .withMessage('开始日期格式无效'),
  
  body('end_date')
    .notEmpty()
    .withMessage('结束日期不能为空')
    .isISO8601()
    .withMessage('结束日期格式无效'),
  
  body('reason')
    .notEmpty()
    .withMessage('请假原因不能为空')
    .isLength({ min: 5, max: 500 })
    .withMessage('请假原因长度应在5-500字符之间'),
  
  handleValidationErrors
];

// 会议室预订验证
const validateMeetingRoom = [
  body('room_name')
    .notEmpty()
    .withMessage('会议室名称不能为空')
    .isLength({ min: 2, max: 100 })
    .withMessage('会议室名称长度应在2-100字符之间'),
  
  body('capacity')
    .notEmpty()
    .withMessage('容量不能为空')
    .isInt({ min: 1, max: 1000 })
    .withMessage('容量必须是1-1000之间的整数'),
  
  body('equipment')
    .optional()
    .isArray()
    .withMessage('设备列表必须是数组'),
  
  handleValidationErrors
];

// 会议预订验证
const validateMeeting = [
  body('meeting_room_id')
    .notEmpty()
    .withMessage('会议室ID不能为空')
    .isInt({ min: 1 })
    .withMessage('会议室ID必须是正整数'),
  
  body('title')
    .notEmpty()
    .withMessage('会议标题不能为空')
    .isLength({ min: 2, max: 200 })
    .withMessage('会议标题长度应在2-200字符之间'),
  
  body('start_time')
    .notEmpty()
    .withMessage('开始时间不能为空')
    .isISO8601()
    .withMessage('开始时间格式无效'),
  
  body('end_time')
    .notEmpty()
    .withMessage('结束时间不能为空')
    .isISO8601()
    .withMessage('结束时间格式无效'),
  
  body('participants')
    .optional()
    .isArray()
    .withMessage('参会人员必须是数组'),
  
  handleValidationErrors
];

module.exports = {
  validateWorkflowDefinition,
  validateWorkflowInstance,
  validateApprovalTask,
  validateQueryParams,
  validateIdParam,
  validateEmployeeCreation,
  validateAttendanceRecord,
  validateLeaveRequest,
  validateMeetingRoom,
  validateMeeting,
  handleValidationErrors
}; 