const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const OfficeSupply = sequelize.define('OfficeSupply', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '用品名称'
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '用品编码'
    },
    category: {
      type: DataTypes.ENUM('stationery', 'electronics', 'furniture', 'consumables', 'equipment', 'other'),
      defaultValue: 'other',
      comment: '用品类别'
    },
    brand: {
      type: DataTypes.STRING(100),
      comment: '品牌'
    },
    model: {
      type: DataTypes.STRING(100),
      comment: '型号'
    },
    specification: {
      type: DataTypes.TEXT,
      comment: '规格说明'
    },
    unit: {
      type: DataTypes.STRING(20),
      defaultValue: '个',
      comment: '计量单位'
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      comment: '单价'
    },
    current_stock: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '当前库存'
    },
    min_stock: {
      type: DataTypes.INTEGER,
      defaultValue: 10,
      comment: '最低库存警戒线'
    },
    max_stock: {
      type: DataTypes.INTEGER,
      defaultValue: 1000,
      comment: '最大库存'
    },
    location: {
      type: DataTypes.STRING(100),
      comment: '存放位置'
    },
    supplier: {
      type: DataTypes.STRING(100),
      comment: '供应商'
    },
    supplier_contact: {
      type: DataTypes.STRING(100),
      comment: '供应商联系方式'
    },
    purchase_date: {
      type: DataTypes.DATE,
      comment: '采购日期'
    },
    warranty_period: {
      type: DataTypes.INTEGER,
      comment: '保修期(月)'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'discontinued'),
      defaultValue: 'active',
      comment: '状态'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '描述'
    },
    image_url: {
      type: DataTypes.STRING(500),
      comment: '图片URL'
    },
    tags: {
      type: DataTypes.TEXT,
      comment: '标签(JSON数组)'
    },
    created_by: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '创建人'
    },
    updated_by: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '更新人'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'office_supplies',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '办公用品表'
  });

  OfficeSupply.associate = (models) => {
    // 关联创建人
    OfficeSupply.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // 关联更新人
    OfficeSupply.belongsTo(models.User, {
      foreignKey: 'updated_by',
      as: 'updater'
    });

    // 关联申请记录
    OfficeSupply.hasMany(models.SupplyRequest, {
      foreignKey: 'supply_id',
      as: 'requests'
    });
  };

  return OfficeSupply;
};
