// nodejs IncomingHttpHeaders is Record<string, string | string[]>, but it's actually this:

export let ENCRYPTION_TYPES = /*#__PURE__*/function (ENCRYPTION_TYPES) {
  ENCRYPTION_TYPES["SSEC"] = "SSE-C";
  ENCRYPTION_TYPES["KMS"] = "KMS";
  return ENCRYPTION_TYPES;
}({});
export let RETENTION_MODES = /*#__PURE__*/function (RETENTION_MODES) {
  RETENTION_MODES["GOVERNANCE"] = "GOVERNANCE";
  RETENTION_MODES["COMPLIANCE"] = "COMPLIANCE";
  return RETENTION_MODES;
}({});
export let RETENTION_VALIDITY_UNITS = /*#__PURE__*/function (RETENTION_VALIDITY_UNITS) {
  RETENTION_VALIDITY_UNITS["DAYS"] = "Days";
  RETENTION_VALIDITY_UNITS["YEARS"] = "Years";
  return RETENTION_VALIDITY_UNITS;
}({});
export let LEGAL_HOLD_STATUS = /*#__PURE__*/function (LEGAL_HOLD_STATUS) {
  LEGAL_HOLD_STATUS["ENABLED"] = "ON";
  LEGAL_HOLD_STATUS["DISABLED"] = "OFF";
  return LEGAL_HOLD_STATUS;
}({});

/* Replication Config types */

/* Replication Config types */
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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