"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bucketEncryptionTransformer = bucketEncryptionTransformer;
exports.bucketVersioningTransformer = bucketVersioningTransformer;
exports.getBucketNotificationTransformer = getBucketNotificationTransformer;
exports.getCompleteMultipartTransformer = getCompleteMultipartTransformer;
exports.getConcater = getConcater;
exports.getCopyObjectTransformer = getCopyObjectTransformer;
exports.getHashSummer = getHashSummer;
exports.getListMultipartTransformer = getListMultipartTransformer;
exports.getListObjectsTransformer = getListObjectsTransformer;
exports.getListObjectsV2Transformer = getListObjectsV2Transformer;
exports.getListObjectsV2WithMetadataTransformer = getListObjectsV2WithMetadataTransformer;
exports.getNotificationTransformer = getNotificationTransformer;
exports.getTagsTransformer = getTagsTransformer;
exports.lifecycleTransformer = lifecycleTransformer;
exports.objectLegalHoldTransformer = objectLegalHoldTransformer;
exports.objectLockTransformer = objectLockTransformer;
exports.objectRetentionTransformer = objectRetentionTransformer;
exports.removeObjectsTransformer = removeObjectsTransformer;
exports.selectObjectContentTransformer = selectObjectContentTransformer;
exports.uploadPartTransformer = uploadPartTransformer;
var Crypto = _interopRequireWildcard(require("crypto"), true);
var _jsonStream = require("json-stream");
var _through = require("through2");
var _helper = require("./internal/helper.js");
var xmlParsers = _interopRequireWildcard(require("./xml-parsers.js"), true);
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015, 2016 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// getConcater returns a stream that concatenates the input and emits
// the concatenated output when 'end' has reached. If an optional
// parser function is passed upon reaching the 'end' of the stream,
// `parser(concatenated_data)` will be emitted.
function getConcater(parser, emitError) {
  var objectMode = false;
  var bufs = [];
  if (parser && !(0, _helper.isFunction)(parser)) {
    throw new TypeError('parser should be of type "function"');
  }
  if (parser) {
    objectMode = true;
  }
  return _through({
    objectMode
  }, function (chunk, enc, cb) {
    bufs.push(chunk);
    cb();
  }, function (cb) {
    if (emitError) {
      cb(parser(Buffer.concat(bufs).toString()));
      // cb(e) would mean we have to emit 'end' by explicitly calling this.push(null)
      this.push(null);
      return;
    }
    if (bufs.length) {
      if (parser) {
        this.push(parser(Buffer.concat(bufs).toString()));
      } else {
        this.push(Buffer.concat(bufs));
      }
    }
    cb();
  });
}

// A through stream that calculates md5sum and sha256sum
function getHashSummer(enableSHA256) {
  var md5 = Crypto.createHash('md5');
  var sha256 = Crypto.createHash('sha256');
  return _through.obj(function (chunk, enc, cb) {
    if (enableSHA256) {
      sha256.update(chunk);
    } else {
      md5.update(chunk);
    }
    cb();
  }, function (cb) {
    var md5sum = '';
    var sha256sum = '';
    if (enableSHA256) {
      sha256sum = sha256.digest('hex');
    } else {
      md5sum = md5.digest('base64');
    }
    var hashData = {
      md5sum,
      sha256sum
    };
    this.push(hashData);
    this.push(null);
    cb();
  });
}

// Following functions return a stream object that parses XML
// and emits suitable Javascript objects.

// Parses CopyObject response.
function getCopyObjectTransformer() {
  return getConcater(xmlParsers.parseCopyObject);
}

// Parses listMultipartUploads response.
function getListMultipartTransformer() {
  return getConcater(xmlParsers.parseListMultipart);
}

// Parses listObjects response.
function getListObjectsTransformer() {
  return getConcater(xmlParsers.parseListObjects);
}

// Parses listObjects response.
function getListObjectsV2Transformer() {
  return getConcater(xmlParsers.parseListObjectsV2);
}

// Parses listObjects with metadata response.
function getListObjectsV2WithMetadataTransformer() {
  return getConcater(xmlParsers.parseListObjectsV2WithMetadata);
}

// Parses completeMultipartUpload response.
function getCompleteMultipartTransformer() {
  return getConcater(xmlParsers.parseCompleteMultipart);
}

// Parses GET/SET BucketNotification response
function getBucketNotificationTransformer() {
  return getConcater(xmlParsers.parseBucketNotification);
}

// Parses a notification.
function getNotificationTransformer() {
  // This will parse and return each object.
  return new _jsonStream();
}
function bucketVersioningTransformer() {
  return getConcater(xmlParsers.parseBucketVersioningConfig);
}
function getTagsTransformer() {
  return getConcater(xmlParsers.parseTagging);
}
function lifecycleTransformer() {
  return getConcater(xmlParsers.parseLifecycleConfig);
}
function objectLockTransformer() {
  return getConcater(xmlParsers.parseObjectLockConfig);
}
function objectRetentionTransformer() {
  return getConcater(xmlParsers.parseObjectRetentionConfig);
}
function bucketEncryptionTransformer() {
  return getConcater(xmlParsers.parseBucketEncryptionConfig);
}
function objectLegalHoldTransformer() {
  return getConcater(xmlParsers.parseObjectLegalHoldConfig);
}
function uploadPartTransformer() {
  return getConcater(xmlParsers.uploadPartParser);
}
function selectObjectContentTransformer() {
  return getConcater();
}
function removeObjectsTransformer() {
  return getConcater(xmlParsers.removeObjectsParser);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJDcnlwdG8iLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfanNvblN0cmVhbSIsIl90aHJvdWdoIiwiX2hlbHBlciIsInhtbFBhcnNlcnMiLCJfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUiLCJub2RlSW50ZXJvcCIsIldlYWtNYXAiLCJjYWNoZUJhYmVsSW50ZXJvcCIsImNhY2hlTm9kZUludGVyb3AiLCJvYmoiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsImNhY2hlIiwiaGFzIiwiZ2V0IiwibmV3T2JqIiwiaGFzUHJvcGVydHlEZXNjcmlwdG9yIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJrZXkiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJkZXNjIiwic2V0IiwiZ2V0Q29uY2F0ZXIiLCJwYXJzZXIiLCJlbWl0RXJyb3IiLCJvYmplY3RNb2RlIiwiYnVmcyIsImlzRnVuY3Rpb24iLCJUeXBlRXJyb3IiLCJUaHJvdWdoMiIsImNodW5rIiwiZW5jIiwiY2IiLCJwdXNoIiwiQnVmZmVyIiwiY29uY2F0IiwidG9TdHJpbmciLCJsZW5ndGgiLCJnZXRIYXNoU3VtbWVyIiwiZW5hYmxlU0hBMjU2IiwibWQ1IiwiY3JlYXRlSGFzaCIsInNoYTI1NiIsInVwZGF0ZSIsIm1kNXN1bSIsInNoYTI1NnN1bSIsImRpZ2VzdCIsImhhc2hEYXRhIiwiZ2V0Q29weU9iamVjdFRyYW5zZm9ybWVyIiwicGFyc2VDb3B5T2JqZWN0IiwiZ2V0TGlzdE11bHRpcGFydFRyYW5zZm9ybWVyIiwicGFyc2VMaXN0TXVsdGlwYXJ0IiwiZ2V0TGlzdE9iamVjdHNUcmFuc2Zvcm1lciIsInBhcnNlTGlzdE9iamVjdHMiLCJnZXRMaXN0T2JqZWN0c1YyVHJhbnNmb3JtZXIiLCJwYXJzZUxpc3RPYmplY3RzVjIiLCJnZXRMaXN0T2JqZWN0c1YyV2l0aE1ldGFkYXRhVHJhbnNmb3JtZXIiLCJwYXJzZUxpc3RPYmplY3RzVjJXaXRoTWV0YWRhdGEiLCJnZXRDb21wbGV0ZU11bHRpcGFydFRyYW5zZm9ybWVyIiwicGFyc2VDb21wbGV0ZU11bHRpcGFydCIsImdldEJ1Y2tldE5vdGlmaWNhdGlvblRyYW5zZm9ybWVyIiwicGFyc2VCdWNrZXROb3RpZmljYXRpb24iLCJnZXROb3RpZmljYXRpb25UcmFuc2Zvcm1lciIsIkpTT05QYXJzZXIiLCJidWNrZXRWZXJzaW9uaW5nVHJhbnNmb3JtZXIiLCJwYXJzZUJ1Y2tldFZlcnNpb25pbmdDb25maWciLCJnZXRUYWdzVHJhbnNmb3JtZXIiLCJwYXJzZVRhZ2dpbmciLCJsaWZlY3ljbGVUcmFuc2Zvcm1lciIsInBhcnNlTGlmZWN5Y2xlQ29uZmlnIiwib2JqZWN0TG9ja1RyYW5zZm9ybWVyIiwicGFyc2VPYmplY3RMb2NrQ29uZmlnIiwib2JqZWN0UmV0ZW50aW9uVHJhbnNmb3JtZXIiLCJwYXJzZU9iamVjdFJldGVudGlvbkNvbmZpZyIsImJ1Y2tldEVuY3J5cHRpb25UcmFuc2Zvcm1lciIsInBhcnNlQnVja2V0RW5jcnlwdGlvbkNvbmZpZyIsIm9iamVjdExlZ2FsSG9sZFRyYW5zZm9ybWVyIiwicGFyc2VPYmplY3RMZWdhbEhvbGRDb25maWciLCJ1cGxvYWRQYXJ0VHJhbnNmb3JtZXIiLCJ1cGxvYWRQYXJ0UGFyc2VyIiwic2VsZWN0T2JqZWN0Q29udGVudFRyYW5zZm9ybWVyIiwicmVtb3ZlT2JqZWN0c1RyYW5zZm9ybWVyIiwicmVtb3ZlT2JqZWN0c1BhcnNlciJdLCJzb3VyY2VzIjpbInRyYW5zZm9ybWVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogTWluSU8gSmF2YXNjcmlwdCBMaWJyYXJ5IGZvciBBbWF6b24gUzMgQ29tcGF0aWJsZSBDbG91ZCBTdG9yYWdlLCAoQykgMjAxNSwgMjAxNiBNaW5JTywgSW5jLlxuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuXG5pbXBvcnQgKiBhcyBDcnlwdG8gZnJvbSAnbm9kZTpjcnlwdG8nXG5cbmltcG9ydCBKU09OUGFyc2VyIGZyb20gJ2pzb24tc3RyZWFtJ1xuaW1wb3J0IFRocm91Z2gyIGZyb20gJ3Rocm91Z2gyJ1xuXG5pbXBvcnQgeyBpc0Z1bmN0aW9uIH0gZnJvbSAnLi9pbnRlcm5hbC9oZWxwZXIudHMnXG5pbXBvcnQgKiBhcyB4bWxQYXJzZXJzIGZyb20gJy4veG1sLXBhcnNlcnMuanMnXG5cbi8vIGdldENvbmNhdGVyIHJldHVybnMgYSBzdHJlYW0gdGhhdCBjb25jYXRlbmF0ZXMgdGhlIGlucHV0IGFuZCBlbWl0c1xuLy8gdGhlIGNvbmNhdGVuYXRlZCBvdXRwdXQgd2hlbiAnZW5kJyBoYXMgcmVhY2hlZC4gSWYgYW4gb3B0aW9uYWxcbi8vIHBhcnNlciBmdW5jdGlvbiBpcyBwYXNzZWQgdXBvbiByZWFjaGluZyB0aGUgJ2VuZCcgb2YgdGhlIHN0cmVhbSxcbi8vIGBwYXJzZXIoY29uY2F0ZW5hdGVkX2RhdGEpYCB3aWxsIGJlIGVtaXR0ZWQuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q29uY2F0ZXIocGFyc2VyLCBlbWl0RXJyb3IpIHtcbiAgdmFyIG9iamVjdE1vZGUgPSBmYWxzZVxuICB2YXIgYnVmcyA9IFtdXG5cbiAgaWYgKHBhcnNlciAmJiAhaXNGdW5jdGlvbihwYXJzZXIpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcigncGFyc2VyIHNob3VsZCBiZSBvZiB0eXBlIFwiZnVuY3Rpb25cIicpXG4gIH1cblxuICBpZiAocGFyc2VyKSB7XG4gICAgb2JqZWN0TW9kZSA9IHRydWVcbiAgfVxuXG4gIHJldHVybiBUaHJvdWdoMihcbiAgICB7IG9iamVjdE1vZGUgfSxcbiAgICBmdW5jdGlvbiAoY2h1bmssIGVuYywgY2IpIHtcbiAgICAgIGJ1ZnMucHVzaChjaHVuaylcbiAgICAgIGNiKClcbiAgICB9LFxuICAgIGZ1bmN0aW9uIChjYikge1xuICAgICAgaWYgKGVtaXRFcnJvcikge1xuICAgICAgICBjYihwYXJzZXIoQnVmZmVyLmNvbmNhdChidWZzKS50b1N0cmluZygpKSlcbiAgICAgICAgLy8gY2IoZSkgd291bGQgbWVhbiB3ZSBoYXZlIHRvIGVtaXQgJ2VuZCcgYnkgZXhwbGljaXRseSBjYWxsaW5nIHRoaXMucHVzaChudWxsKVxuICAgICAgICB0aGlzLnB1c2gobnVsbClcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgICBpZiAoYnVmcy5sZW5ndGgpIHtcbiAgICAgICAgaWYgKHBhcnNlcikge1xuICAgICAgICAgIHRoaXMucHVzaChwYXJzZXIoQnVmZmVyLmNvbmNhdChidWZzKS50b1N0cmluZygpKSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aGlzLnB1c2goQnVmZmVyLmNvbmNhdChidWZzKSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgY2IoKVxuICAgIH0sXG4gIClcbn1cblxuLy8gQSB0aHJvdWdoIHN0cmVhbSB0aGF0IGNhbGN1bGF0ZXMgbWQ1c3VtIGFuZCBzaGEyNTZzdW1cbmV4cG9ydCBmdW5jdGlvbiBnZXRIYXNoU3VtbWVyKGVuYWJsZVNIQTI1Nikge1xuICB2YXIgbWQ1ID0gQ3J5cHRvLmNyZWF0ZUhhc2goJ21kNScpXG4gIHZhciBzaGEyNTYgPSBDcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JylcblxuICByZXR1cm4gVGhyb3VnaDIub2JqKFxuICAgIGZ1bmN0aW9uIChjaHVuaywgZW5jLCBjYikge1xuICAgICAgaWYgKGVuYWJsZVNIQTI1Nikge1xuICAgICAgICBzaGEyNTYudXBkYXRlKGNodW5rKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbWQ1LnVwZGF0ZShjaHVuaylcbiAgICAgIH1cbiAgICAgIGNiKClcbiAgICB9LFxuICAgIGZ1bmN0aW9uIChjYikge1xuICAgICAgdmFyIG1kNXN1bSA9ICcnXG4gICAgICB2YXIgc2hhMjU2c3VtID0gJydcbiAgICAgIGlmIChlbmFibGVTSEEyNTYpIHtcbiAgICAgICAgc2hhMjU2c3VtID0gc2hhMjU2LmRpZ2VzdCgnaGV4JylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG1kNXN1bSA9IG1kNS5kaWdlc3QoJ2Jhc2U2NCcpXG4gICAgICB9XG4gICAgICB2YXIgaGFzaERhdGEgPSB7IG1kNXN1bSwgc2hhMjU2c3VtIH1cbiAgICAgIHRoaXMucHVzaChoYXNoRGF0YSlcbiAgICAgIHRoaXMucHVzaChudWxsKVxuICAgICAgY2IoKVxuICAgIH0sXG4gIClcbn1cblxuLy8gRm9sbG93aW5nIGZ1bmN0aW9ucyByZXR1cm4gYSBzdHJlYW0gb2JqZWN0IHRoYXQgcGFyc2VzIFhNTFxuLy8gYW5kIGVtaXRzIHN1aXRhYmxlIEphdmFzY3JpcHQgb2JqZWN0cy5cblxuLy8gUGFyc2VzIENvcHlPYmplY3QgcmVzcG9uc2UuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q29weU9iamVjdFRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUNvcHlPYmplY3QpXG59XG5cbi8vIFBhcnNlcyBsaXN0TXVsdGlwYXJ0VXBsb2FkcyByZXNwb25zZS5cbmV4cG9ydCBmdW5jdGlvbiBnZXRMaXN0TXVsdGlwYXJ0VHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlTGlzdE11bHRpcGFydClcbn1cblxuLy8gUGFyc2VzIGxpc3RPYmplY3RzIHJlc3BvbnNlLlxuZXhwb3J0IGZ1bmN0aW9uIGdldExpc3RPYmplY3RzVHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlTGlzdE9iamVjdHMpXG59XG5cbi8vIFBhcnNlcyBsaXN0T2JqZWN0cyByZXNwb25zZS5cbmV4cG9ydCBmdW5jdGlvbiBnZXRMaXN0T2JqZWN0c1YyVHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlTGlzdE9iamVjdHNWMilcbn1cblxuLy8gUGFyc2VzIGxpc3RPYmplY3RzIHdpdGggbWV0YWRhdGEgcmVzcG9uc2UuXG5leHBvcnQgZnVuY3Rpb24gZ2V0TGlzdE9iamVjdHNWMldpdGhNZXRhZGF0YVRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUxpc3RPYmplY3RzVjJXaXRoTWV0YWRhdGEpXG59XG5cbi8vIFBhcnNlcyBjb21wbGV0ZU11bHRpcGFydFVwbG9hZCByZXNwb25zZS5cbmV4cG9ydCBmdW5jdGlvbiBnZXRDb21wbGV0ZU11bHRpcGFydFRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUNvbXBsZXRlTXVsdGlwYXJ0KVxufVxuXG4vLyBQYXJzZXMgR0VUL1NFVCBCdWNrZXROb3RpZmljYXRpb24gcmVzcG9uc2VcbmV4cG9ydCBmdW5jdGlvbiBnZXRCdWNrZXROb3RpZmljYXRpb25UcmFuc2Zvcm1lcigpIHtcbiAgcmV0dXJuIGdldENvbmNhdGVyKHhtbFBhcnNlcnMucGFyc2VCdWNrZXROb3RpZmljYXRpb24pXG59XG5cbi8vIFBhcnNlcyBhIG5vdGlmaWNhdGlvbi5cbmV4cG9ydCBmdW5jdGlvbiBnZXROb3RpZmljYXRpb25UcmFuc2Zvcm1lcigpIHtcbiAgLy8gVGhpcyB3aWxsIHBhcnNlIGFuZCByZXR1cm4gZWFjaCBvYmplY3QuXG4gIHJldHVybiBuZXcgSlNPTlBhcnNlcigpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBidWNrZXRWZXJzaW9uaW5nVHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlQnVja2V0VmVyc2lvbmluZ0NvbmZpZylcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFRhZ3NUcmFuc2Zvcm1lcigpIHtcbiAgcmV0dXJuIGdldENvbmNhdGVyKHhtbFBhcnNlcnMucGFyc2VUYWdnaW5nKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gbGlmZWN5Y2xlVHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnBhcnNlTGlmZWN5Y2xlQ29uZmlnKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gb2JqZWN0TG9ja1RyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZU9iamVjdExvY2tDb25maWcpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBvYmplY3RSZXRlbnRpb25UcmFuc2Zvcm1lcigpIHtcbiAgcmV0dXJuIGdldENvbmNhdGVyKHhtbFBhcnNlcnMucGFyc2VPYmplY3RSZXRlbnRpb25Db25maWcpXG59XG5leHBvcnQgZnVuY3Rpb24gYnVja2V0RW5jcnlwdGlvblRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5wYXJzZUJ1Y2tldEVuY3J5cHRpb25Db25maWcpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBvYmplY3RMZWdhbEhvbGRUcmFuc2Zvcm1lcigpIHtcbiAgcmV0dXJuIGdldENvbmNhdGVyKHhtbFBhcnNlcnMucGFyc2VPYmplY3RMZWdhbEhvbGRDb25maWcpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1cGxvYWRQYXJ0VHJhbnNmb3JtZXIoKSB7XG4gIHJldHVybiBnZXRDb25jYXRlcih4bWxQYXJzZXJzLnVwbG9hZFBhcnRQYXJzZXIpXG59XG5leHBvcnQgZnVuY3Rpb24gc2VsZWN0T2JqZWN0Q29udGVudFRyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gcmVtb3ZlT2JqZWN0c1RyYW5zZm9ybWVyKCkge1xuICByZXR1cm4gZ2V0Q29uY2F0ZXIoeG1sUGFyc2Vycy5yZW1vdmVPYmplY3RzUGFyc2VyKVxufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBZ0JBLElBQUFBLE1BQUEsR0FBQUMsdUJBQUEsQ0FBQUMsT0FBQTtBQUVBLElBQUFDLFdBQUEsR0FBQUQsT0FBQTtBQUNBLElBQUFFLFFBQUEsR0FBQUYsT0FBQTtBQUVBLElBQUFHLE9BQUEsR0FBQUgsT0FBQTtBQUNBLElBQUFJLFVBQUEsR0FBQUwsdUJBQUEsQ0FBQUMsT0FBQTtBQUE4QyxTQUFBSyx5QkFBQUMsV0FBQSxlQUFBQyxPQUFBLGtDQUFBQyxpQkFBQSxPQUFBRCxPQUFBLFFBQUFFLGdCQUFBLE9BQUFGLE9BQUEsWUFBQUYsd0JBQUEsWUFBQUEsQ0FBQUMsV0FBQSxXQUFBQSxXQUFBLEdBQUFHLGdCQUFBLEdBQUFELGlCQUFBLEtBQUFGLFdBQUE7QUFBQSxTQUFBUCx3QkFBQVcsR0FBQSxFQUFBSixXQUFBLFNBQUFBLFdBQUEsSUFBQUksR0FBQSxJQUFBQSxHQUFBLENBQUFDLFVBQUEsV0FBQUQsR0FBQSxRQUFBQSxHQUFBLG9CQUFBQSxHQUFBLHdCQUFBQSxHQUFBLDRCQUFBRSxPQUFBLEVBQUFGLEdBQUEsVUFBQUcsS0FBQSxHQUFBUix3QkFBQSxDQUFBQyxXQUFBLE9BQUFPLEtBQUEsSUFBQUEsS0FBQSxDQUFBQyxHQUFBLENBQUFKLEdBQUEsWUFBQUcsS0FBQSxDQUFBRSxHQUFBLENBQUFMLEdBQUEsU0FBQU0sTUFBQSxXQUFBQyxxQkFBQSxHQUFBQyxNQUFBLENBQUFDLGNBQUEsSUFBQUQsTUFBQSxDQUFBRSx3QkFBQSxXQUFBQyxHQUFBLElBQUFYLEdBQUEsUUFBQVcsR0FBQSxrQkFBQUgsTUFBQSxDQUFBSSxTQUFBLENBQUFDLGNBQUEsQ0FBQUMsSUFBQSxDQUFBZCxHQUFBLEVBQUFXLEdBQUEsU0FBQUksSUFBQSxHQUFBUixxQkFBQSxHQUFBQyxNQUFBLENBQUFFLHdCQUFBLENBQUFWLEdBQUEsRUFBQVcsR0FBQSxjQUFBSSxJQUFBLEtBQUFBLElBQUEsQ0FBQVYsR0FBQSxJQUFBVSxJQUFBLENBQUFDLEdBQUEsS0FBQVIsTUFBQSxDQUFBQyxjQUFBLENBQUFILE1BQUEsRUFBQUssR0FBQSxFQUFBSSxJQUFBLFlBQUFULE1BQUEsQ0FBQUssR0FBQSxJQUFBWCxHQUFBLENBQUFXLEdBQUEsU0FBQUwsTUFBQSxDQUFBSixPQUFBLEdBQUFGLEdBQUEsTUFBQUcsS0FBQSxJQUFBQSxLQUFBLENBQUFhLEdBQUEsQ0FBQWhCLEdBQUEsRUFBQU0sTUFBQSxZQUFBQSxNQUFBO0FBdEI5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBVUE7QUFDQTtBQUNBO0FBQ0E7QUFDTyxTQUFTVyxXQUFXQSxDQUFDQyxNQUFNLEVBQUVDLFNBQVMsRUFBRTtFQUM3QyxJQUFJQyxVQUFVLEdBQUcsS0FBSztFQUN0QixJQUFJQyxJQUFJLEdBQUcsRUFBRTtFQUViLElBQUlILE1BQU0sSUFBSSxDQUFDLElBQUFJLGtCQUFVLEVBQUNKLE1BQU0sQ0FBQyxFQUFFO0lBQ2pDLE1BQU0sSUFBSUssU0FBUyxDQUFDLHFDQUFxQyxDQUFDO0VBQzVEO0VBRUEsSUFBSUwsTUFBTSxFQUFFO0lBQ1ZFLFVBQVUsR0FBRyxJQUFJO0VBQ25CO0VBRUEsT0FBT0ksUUFBUSxDQUNiO0lBQUVKO0VBQVcsQ0FBQyxFQUNkLFVBQVVLLEtBQUssRUFBRUMsR0FBRyxFQUFFQyxFQUFFLEVBQUU7SUFDeEJOLElBQUksQ0FBQ08sSUFBSSxDQUFDSCxLQUFLLENBQUM7SUFDaEJFLEVBQUUsQ0FBQyxDQUFDO0VBQ04sQ0FBQyxFQUNELFVBQVVBLEVBQUUsRUFBRTtJQUNaLElBQUlSLFNBQVMsRUFBRTtNQUNiUSxFQUFFLENBQUNULE1BQU0sQ0FBQ1csTUFBTSxDQUFDQyxNQUFNLENBQUNULElBQUksQ0FBQyxDQUFDVSxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7TUFDMUM7TUFDQSxJQUFJLENBQUNILElBQUksQ0FBQyxJQUFJLENBQUM7TUFDZjtJQUNGO0lBQ0EsSUFBSVAsSUFBSSxDQUFDVyxNQUFNLEVBQUU7TUFDZixJQUFJZCxNQUFNLEVBQUU7UUFDVixJQUFJLENBQUNVLElBQUksQ0FBQ1YsTUFBTSxDQUFDVyxNQUFNLENBQUNDLE1BQU0sQ0FBQ1QsSUFBSSxDQUFDLENBQUNVLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUNuRCxDQUFDLE1BQU07UUFDTCxJQUFJLENBQUNILElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxNQUFNLENBQUNULElBQUksQ0FBQyxDQUFDO01BQ2hDO0lBQ0Y7SUFDQU0sRUFBRSxDQUFDLENBQUM7RUFDTixDQUNGLENBQUM7QUFDSDs7QUFFQTtBQUNPLFNBQVNNLGFBQWFBLENBQUNDLFlBQVksRUFBRTtFQUMxQyxJQUFJQyxHQUFHLEdBQUcvQyxNQUFNLENBQUNnRCxVQUFVLENBQUMsS0FBSyxDQUFDO0VBQ2xDLElBQUlDLE1BQU0sR0FBR2pELE1BQU0sQ0FBQ2dELFVBQVUsQ0FBQyxRQUFRLENBQUM7RUFFeEMsT0FBT1osUUFBUSxDQUFDeEIsR0FBRyxDQUNqQixVQUFVeUIsS0FBSyxFQUFFQyxHQUFHLEVBQUVDLEVBQUUsRUFBRTtJQUN4QixJQUFJTyxZQUFZLEVBQUU7TUFDaEJHLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDYixLQUFLLENBQUM7SUFDdEIsQ0FBQyxNQUFNO01BQ0xVLEdBQUcsQ0FBQ0csTUFBTSxDQUFDYixLQUFLLENBQUM7SUFDbkI7SUFDQUUsRUFBRSxDQUFDLENBQUM7RUFDTixDQUFDLEVBQ0QsVUFBVUEsRUFBRSxFQUFFO0lBQ1osSUFBSVksTUFBTSxHQUFHLEVBQUU7SUFDZixJQUFJQyxTQUFTLEdBQUcsRUFBRTtJQUNsQixJQUFJTixZQUFZLEVBQUU7TUFDaEJNLFNBQVMsR0FBR0gsTUFBTSxDQUFDSSxNQUFNLENBQUMsS0FBSyxDQUFDO0lBQ2xDLENBQUMsTUFBTTtNQUNMRixNQUFNLEdBQUdKLEdBQUcsQ0FBQ00sTUFBTSxDQUFDLFFBQVEsQ0FBQztJQUMvQjtJQUNBLElBQUlDLFFBQVEsR0FBRztNQUFFSCxNQUFNO01BQUVDO0lBQVUsQ0FBQztJQUNwQyxJQUFJLENBQUNaLElBQUksQ0FBQ2MsUUFBUSxDQUFDO0lBQ25CLElBQUksQ0FBQ2QsSUFBSSxDQUFDLElBQUksQ0FBQztJQUNmRCxFQUFFLENBQUMsQ0FBQztFQUNOLENBQ0YsQ0FBQztBQUNIOztBQUVBO0FBQ0E7O0FBRUE7QUFDTyxTQUFTZ0Isd0JBQXdCQSxDQUFBLEVBQUc7RUFDekMsT0FBTzFCLFdBQVcsQ0FBQ3ZCLFVBQVUsQ0FBQ2tELGVBQWUsQ0FBQztBQUNoRDs7QUFFQTtBQUNPLFNBQVNDLDJCQUEyQkEsQ0FBQSxFQUFHO0VBQzVDLE9BQU81QixXQUFXLENBQUN2QixVQUFVLENBQUNvRCxrQkFBa0IsQ0FBQztBQUNuRDs7QUFFQTtBQUNPLFNBQVNDLHlCQUF5QkEsQ0FBQSxFQUFHO0VBQzFDLE9BQU85QixXQUFXLENBQUN2QixVQUFVLENBQUNzRCxnQkFBZ0IsQ0FBQztBQUNqRDs7QUFFQTtBQUNPLFNBQVNDLDJCQUEyQkEsQ0FBQSxFQUFHO0VBQzVDLE9BQU9oQyxXQUFXLENBQUN2QixVQUFVLENBQUN3RCxrQkFBa0IsQ0FBQztBQUNuRDs7QUFFQTtBQUNPLFNBQVNDLHVDQUF1Q0EsQ0FBQSxFQUFHO0VBQ3hELE9BQU9sQyxXQUFXLENBQUN2QixVQUFVLENBQUMwRCw4QkFBOEIsQ0FBQztBQUMvRDs7QUFFQTtBQUNPLFNBQVNDLCtCQUErQkEsQ0FBQSxFQUFHO0VBQ2hELE9BQU9wQyxXQUFXLENBQUN2QixVQUFVLENBQUM0RCxzQkFBc0IsQ0FBQztBQUN2RDs7QUFFQTtBQUNPLFNBQVNDLGdDQUFnQ0EsQ0FBQSxFQUFHO0VBQ2pELE9BQU90QyxXQUFXLENBQUN2QixVQUFVLENBQUM4RCx1QkFBdUIsQ0FBQztBQUN4RDs7QUFFQTtBQUNPLFNBQVNDLDBCQUEwQkEsQ0FBQSxFQUFHO0VBQzNDO0VBQ0EsT0FBTyxJQUFJQyxXQUFVLENBQUMsQ0FBQztBQUN6QjtBQUVPLFNBQVNDLDJCQUEyQkEsQ0FBQSxFQUFHO0VBQzVDLE9BQU8xQyxXQUFXLENBQUN2QixVQUFVLENBQUNrRSwyQkFBMkIsQ0FBQztBQUM1RDtBQUVPLFNBQVNDLGtCQUFrQkEsQ0FBQSxFQUFHO0VBQ25DLE9BQU81QyxXQUFXLENBQUN2QixVQUFVLENBQUNvRSxZQUFZLENBQUM7QUFDN0M7QUFFTyxTQUFTQyxvQkFBb0JBLENBQUEsRUFBRztFQUNyQyxPQUFPOUMsV0FBVyxDQUFDdkIsVUFBVSxDQUFDc0Usb0JBQW9CLENBQUM7QUFDckQ7QUFFTyxTQUFTQyxxQkFBcUJBLENBQUEsRUFBRztFQUN0QyxPQUFPaEQsV0FBVyxDQUFDdkIsVUFBVSxDQUFDd0UscUJBQXFCLENBQUM7QUFDdEQ7QUFFTyxTQUFTQywwQkFBMEJBLENBQUEsRUFBRztFQUMzQyxPQUFPbEQsV0FBVyxDQUFDdkIsVUFBVSxDQUFDMEUsMEJBQTBCLENBQUM7QUFDM0Q7QUFDTyxTQUFTQywyQkFBMkJBLENBQUEsRUFBRztFQUM1QyxPQUFPcEQsV0FBVyxDQUFDdkIsVUFBVSxDQUFDNEUsMkJBQTJCLENBQUM7QUFDNUQ7QUFFTyxTQUFTQywwQkFBMEJBLENBQUEsRUFBRztFQUMzQyxPQUFPdEQsV0FBVyxDQUFDdkIsVUFBVSxDQUFDOEUsMEJBQTBCLENBQUM7QUFDM0Q7QUFFTyxTQUFTQyxxQkFBcUJBLENBQUEsRUFBRztFQUN0QyxPQUFPeEQsV0FBVyxDQUFDdkIsVUFBVSxDQUFDZ0YsZ0JBQWdCLENBQUM7QUFDakQ7QUFDTyxTQUFTQyw4QkFBOEJBLENBQUEsRUFBRztFQUMvQyxPQUFPMUQsV0FBVyxDQUFDLENBQUM7QUFDdEI7QUFFTyxTQUFTMkQsd0JBQXdCQSxDQUFBLEVBQUc7RUFDekMsT0FBTzNELFdBQVcsQ0FBQ3ZCLFVBQVUsQ0FBQ21GLG1CQUFtQixDQUFDO0FBQ3BEIn0=