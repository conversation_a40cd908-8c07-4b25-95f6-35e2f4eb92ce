import axios from 'axios'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3001/api', // 统一使用3001端口
  timeout: 10000,
  withCredentials: true
})

// 错误处理增强配置
const ERROR_CONFIG = {
  MAX_RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  HEALTH_CHECK_INTERVAL: 30000,
  SHOW_DETAILED_ERRORS: import.meta.env.DEV
}

// API健康状态管理
let apiHealthStatus = {
  isHealthy: true,
  lastCheckTime: null,
  consecutiveFailures: 0,
  healthCheckTimer: null
}

// 错误分类器
const ErrorClassifier = {
  // 分析错误类型
  classify(error) {
    const classification = {
      type: 'UNKNOWN_ERROR',
      severity: 'medium',
      userMessage: '操作失败，请稍后重试',
      canRetry: false,
      needsAuth: false,
      isNetworkError: false,
      isServerError: false,
      suggestedAction: null
    }

    // 网络错误分析
    if (!error.response) {
      classification.isNetworkError = true
      classification.canRetry = true
      
      if (error.code === 'ECONNABORTED') {
        classification.type = 'TIMEOUT_ERROR'
        classification.userMessage = '请求超时，服务器响应较慢'
        classification.suggestedAction = 'retry'
      } else if (error.code === 'ERR_NETWORK') {
        classification.type = 'NETWORK_ERROR'
        classification.severity = 'high'
        classification.userMessage = '网络连接失败，请检查网络设置'
        classification.suggestedAction = 'check_network'
      } else {
        classification.type = 'CONNECTION_ERROR'
        classification.userMessage = '无法连接到服务器，请检查网络或稍后重试'
        classification.suggestedAction = 'check_server'
      }
      return classification
    }

    // HTTP状态码错误分析
    const status = error.response.status
    const errorData = error.response.data?.error || {}
    
    switch (status) {
      case 400:
        classification.type = 'VALIDATION_ERROR'
        classification.severity = 'low'
        classification.userMessage = errorData.message || '请求参数错误，请检查输入信息'
        classification.suggestedAction = 'check_input'
        break
        
      case 401:
        classification.type = 'AUTHENTICATION_ERROR'
        classification.needsAuth = true
        classification.userMessage = '登录已过期，请重新登录'
        classification.suggestedAction = 'reauth'
        break
        
      case 403:
        classification.type = 'PERMISSION_ERROR'
        classification.userMessage = '权限不足，无法执行此操作'
        classification.suggestedAction = 'contact_admin'
        break
        
      case 404:
        classification.type = 'NOT_FOUND_ERROR'
        classification.userMessage = '请求的资源不存在'
        classification.suggestedAction = 'refresh_page'
        break
        
      case 409:
        classification.type = 'CONFLICT_ERROR'
        classification.userMessage = errorData.message || '数据冲突，请刷新页面后重试'
        classification.suggestedAction = 'refresh_page'
        break
        
      case 500:
        classification.type = 'SERVER_ERROR'
        classification.severity = 'high'
        classification.isServerError = true
        classification.canRetry = true
        
        // 基于后端错误增强的详细分析
        if (errorData.category === 'database') {
          classification.userMessage = '数据库服务暂时不可用，请稍后重试'
          classification.suggestedAction = 'retry_later'
        } else if (errorData.code === 'DATABASE_CONNECTION_FAILED') {
          classification.userMessage = '数据库连接失败，系统正在修复中'
          classification.severity = 'critical'
          classification.suggestedAction = 'contact_support'
        } else if (errorData.code === 'DATABASE_QUERY_FAILED') {
          classification.userMessage = '数据查询失败，可能是系统配置问题'
          classification.suggestedAction = 'contact_support'
        } else {
          classification.userMessage = '服务器内部错误，请稍后重试'
          classification.suggestedAction = 'retry'
        }
        break
        
      case 502:
      case 503:
      case 504:
        classification.type = 'SERVICE_UNAVAILABLE'
        classification.severity = 'high'
        classification.isServerError = true
        classification.canRetry = true
        classification.userMessage = '服务暂时不可用，请稍后重试'
        classification.suggestedAction = 'retry_later'
        break
        
      default:
        classification.userMessage = errorData.message || `请求失败 (${status})`
        classification.suggestedAction = 'retry'
    }

    return classification
  },

  // 生成用户友好的错误提示
  generateUserMessage(classification, context = {}) {
    const { operation = '操作' } = context
    
    const messages = {
      TIMEOUT_ERROR: `${operation}超时，请检查网络连接后重试`,
      NETWORK_ERROR: `网络连接失败，无法完成${operation}`,
      CONNECTION_ERROR: `无法连接到服务器，${operation}失败`,
      VALIDATION_ERROR: `${operation}失败，请检查输入信息`,
      AUTHENTICATION_ERROR: `登录已过期，请重新登录后继续${operation}`,
      PERMISSION_ERROR: `权限不足，无法执行${operation}`,
      NOT_FOUND_ERROR: `${operation}失败，请求的资源不存在`,
      CONFLICT_ERROR: `${operation}失败，数据冲突请刷新后重试`,
      SERVER_ERROR: `${operation}失败，服务器出现问题`,
      SERVICE_UNAVAILABLE: `服务暂时不可用，无法完成${operation}`
    }

    return messages[classification.type] || classification.userMessage
  }
}

// 自动重试机制
const RetryManager = {
  // 判断是否应该重试
  shouldRetry(error, retryCount = 0) {
    if (retryCount >= ERROR_CONFIG.MAX_RETRY_COUNT) {
      return false
    }

    const classification = ErrorClassifier.classify(error)
    return classification.canRetry && (
      classification.isNetworkError || 
      classification.isServerError ||
      (error.response?.status >= 500 && error.response?.status < 600)
    )
  },

  // 计算重试延迟（指数退避）
  getRetryDelay(retryCount) {
    return ERROR_CONFIG.RETRY_DELAY * Math.pow(2, retryCount)
  },

  // 执行重试
  async executeRetry(originalRequest, retryCount = 0) {
    const delay = this.getRetryDelay(retryCount)
    
    console.log(`🔄 准备重试请求 (${retryCount + 1}/${ERROR_CONFIG.MAX_RETRY_COUNT}): ${originalRequest.url}`)
    
    await new Promise(resolve => setTimeout(resolve, delay))
    
    // 标记重试次数
    originalRequest.__retryCount = retryCount + 1
    
    return axiosInstance(originalRequest)
  }
}

// API健康检查
const HealthChecker = {
  // 执行健康检查
  async checkHealth() {
    try {
      const response = await axios.get('http://localhost:3000/api/health', {
        timeout: 3000,
        headers: {} // 不带认证头的健康检查
      })
      
      apiHealthStatus.isHealthy = true
      apiHealthStatus.consecutiveFailures = 0
      apiHealthStatus.lastCheckTime = new Date()
      
      console.log('✅ API健康检查通过')
      return true
    } catch (error) {
      apiHealthStatus.isHealthy = false
      apiHealthStatus.consecutiveFailures++
      apiHealthStatus.lastCheckTime = new Date()
      
      console.warn('❌ API健康检查失败:', error.message)
      
      // 连续失败次数过多时显示通知
      if (apiHealthStatus.consecutiveFailures >= 3) {
        ElNotification({
          title: '服务连接异常',
          message: '检测到服务器连接不稳定，部分功能可能受影响',
          type: 'warning',
          duration: 5000
        })
      }
      
      return false
    }
  },

  // 启动定期健康检查
  startPeriodicCheck() {
    if (apiHealthStatus.healthCheckTimer) {
      clearInterval(apiHealthStatus.healthCheckTimer)
    }

    apiHealthStatus.healthCheckTimer = setInterval(() => {
      this.checkHealth()
    }, ERROR_CONFIG.HEALTH_CHECK_INTERVAL)
  },

  // 停止健康检查
  stopPeriodicCheck() {
    if (apiHealthStatus.healthCheckTimer) {
      clearInterval(apiHealthStatus.healthCheckTimer)
      apiHealthStatus.healthCheckTimer = null
    }
  }
}

// Token验证和刷新管理
const TokenManager = {
  // 验证token有效性
  async validateToken() {
    const token = localStorage.getItem('accessToken') || localStorage.getItem('token')
    if (!token) {
      return false
    }

    try {
      // 尝试获取用户信息来验证token
      const response = await axiosInstance.get('/v1/auth/profile')
      return response.data.success
    } catch (error) {
      if (error.response?.status === 401) {
        return false
      }
      // 其他错误不清除token，可能是网络问题
      throw error
    }
  },

  // 刷新token
  async refreshToken() {
    // 检查是否已经在刷新token
    if (this._refreshing) {
      return this._refreshPromise
    }

    this._refreshing = true

    this._refreshPromise = (async () => {
      try {
        // 检查是否有token
        const currentToken = localStorage.getItem('accessToken') || localStorage.getItem('token')
        if (!currentToken) {
          throw new Error('没有可用的token')
        }

        // 暂时禁用token刷新功能，直接清除token并跳转登录
        console.log('⚠️  Token已过期，请重新登录')
        throw new Error('Token已过期，请重新登录')

      } catch (error) {
        console.error('❌ Token刷新失败:', error)
        this.clearToken()
        throw error
      } finally {
        this._refreshing = false
        this._refreshPromise = null
      }
    })()

    return this._refreshPromise
  },

  // 清除token并跳转登录
  clearToken() {
    console.log('🧹 清除token并跳转登录页')

    // 清除所有相关的token
    localStorage.removeItem('accessToken')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')

    // 重置刷新状态
    this._refreshing = false
    this._refreshPromise = null

    // 延迟跳转，避免在请求过程中立即跳转
    setTimeout(() => {
      // 检查当前是否已经在登录页
      if (!window.location.pathname.includes('/login')) {
        console.log('🔄 跳转到登录页')
        window.location.href = '/login'
      }
    }, 100)
  }
}

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 检查是否在刷新token过程中，避免并发刷新
    if (TokenManager._refreshing && !config.url?.includes('/auth/refresh') && !config.url?.includes('/auth/login')) {
      console.log('⏳ 等待token刷新完成...')
      // 对于非登录和非刷新请求，在刷新期间暂停
      return new Promise((resolve) => {
        const checkRefresh = () => {
          if (!TokenManager._refreshing) {
            const token = localStorage.getItem('accessToken') || localStorage.getItem('token')
            if (token) {
              config.headers.Authorization = `Bearer ${token}`
            }
            resolve(config)
          } else {
            setTimeout(checkRefresh, 100)
          }
        }
        checkRefresh()
      })
    }

    // 从localStorage获取访问令牌，优先使用accessToken
    const token = localStorage.getItem('accessToken') || localStorage.getItem('token')
    if (token && !config.url?.includes('/auth/login')) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳和唯一ID
    config.metadata = {
      startTime: new Date(),
      requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }

    // 添加操作上下文（用于错误提示）
    if (!config.errorContext) {
      config.errorContext = {
        operation: '操作'
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 增强的响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    
    if (import.meta.env.DEV) {
      console.log(`✅ API请求成功 [${response.config.metadata.requestId}]: ${duration}ms`, response.config.url)
    }
    
    // 重置API健康状态
    if (!apiHealthStatus.isHealthy) {
      apiHealthStatus.isHealthy = true
      apiHealthStatus.consecutiveFailures = 0
      console.log('🟢 API服务已恢复正常')
    }
    
    return response
  },
  async (error) => {
    const originalRequest = error.config
    const retryCount = originalRequest.__retryCount || 0
    
    // 记录错误详情
    console.error(`❌ API请求失败 [${originalRequest?.metadata?.requestId}]:`, {
      url: originalRequest?.url,
      method: originalRequest?.method,
      status: error.response?.status,
      message: error.message,
      retryCount
    })

    // 分析错误
    const classification = ErrorClassifier.classify(error)
    
    // 处理401认证错误（token过期）
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      // 如果是刷新token的请求失败，直接清除token
      if (originalRequest.url?.includes('/auth/refresh')) {
        console.log('🔄 Token刷新请求失败，清除token')
        TokenManager.clearToken()
        return Promise.reject(error)
      }

      // 如果是登录请求失败，不进行token刷新
      if (originalRequest.url?.includes('/auth/login')) {
        console.log('🔐 登录请求失败')
        return Promise.reject(error)
      }

      try {
        await TokenManager.refreshToken()

        // 更新原请求的Authorization头
        const newToken = localStorage.getItem('accessToken') || localStorage.getItem('token')
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          // 重新发送原请求
          return axiosInstance(originalRequest)
        } else {
          throw new Error('无法获取新token')
        }
      } catch (refreshError) {
        console.log('🔄 Token刷新失败，清除token并跳转登录')
        TokenManager.clearToken()
        return Promise.reject(error)
      }
    }

    // 自动重试机制
    if (RetryManager.shouldRetry(error, retryCount)) {
      try {
        return await RetryManager.executeRetry(originalRequest, retryCount)
      } catch (retryError) {
        console.error(`🔄 重试失败 (${retryCount + 1}/${ERROR_CONFIG.MAX_RETRY_COUNT}):`, retryError.message)
        // 继续执行下面的错误处理逻辑
        error = retryError
      }
    }

    // 更新API健康状态
    if (classification.isNetworkError || classification.isServerError) {
      apiHealthStatus.isHealthy = false
      apiHealthStatus.consecutiveFailures++
    }

    // 生成用户友好的错误消息
    const userMessage = ErrorClassifier.generateUserMessage(
      classification, 
      originalRequest.errorContext || {}
    )

    // 显示错误提示
    if (classification.severity === 'critical') {
      ElNotification({
        title: '严重错误',
        message: userMessage,
        type: 'error',
        duration: 10000
      })
    } else if (classification.severity === 'high') {
      ElMessage({
        message: userMessage,
        type: 'error',
        duration: 5000
      })
    } else {
      ElMessage({
        message: userMessage,
        type: 'warning',
        duration: 3000
      })
    }

    // 在开发环境显示详细错误信息
    if (ERROR_CONFIG.SHOW_DETAILED_ERRORS && classification.severity === 'high') {
      console.group('🔍 详细错误信息')
      console.log('错误分类:', classification)
      console.log('原始错误:', error)
      console.log('响应数据:', error.response?.data)
      console.groupEnd()
    }

    // 增强错误对象
    error.classification = classification
    error.userMessage = userMessage
    error.canRetry = classification.canRetry
    error.retryCount = retryCount

    return Promise.reject(error)
  }
)

// 请求方法封装（增强版）
export const request = {
  get: (url, config = {}) => {
    return axiosInstance.get(url, {
      errorContext: { operation: '获取数据' },
      ...config
    })
  },
  
  post: (url, data = {}, config = {}) => {
    return axiosInstance.post(url, data, {
      errorContext: { operation: '保存数据' },
      ...config
    })
  },
  
  put: (url, data = {}, config = {}) => {
    return axiosInstance.put(url, data, {
      errorContext: { operation: '更新数据' },
      ...config
    })
  },
  
  patch: (url, data = {}, config = {}) => {
    return axiosInstance.patch(url, data, {
      errorContext: { operation: '修改数据' },
      ...config
    })
  },
  
  delete: (url, config = {}) => {
    return axiosInstance.delete(url, {
      errorContext: { operation: '删除数据' },
      ...config
    })
  }
}

// 导出工具函数
export const apiUtils = {
  // 检查API健康状态
  checkHealth: () => HealthChecker.checkHealth(),
  
  // 获取API健康状态
  getHealthStatus: () => ({ ...apiHealthStatus }),
  
  // 验证token
  validateToken: () => TokenManager.validateToken(),
  
  // 手动刷新token
  refreshToken: () => TokenManager.refreshToken(),
  
  // 启动健康检查
  startHealthCheck: () => HealthChecker.startPeriodicCheck(),
  
  // 停止健康检查
  stopHealthCheck: () => HealthChecker.stopPeriodicCheck(),
  
  // 分析错误
  classifyError: (error) => ErrorClassifier.classify(error)
}

// 启动健康检查（延迟启动避免循环依赖）
if (typeof window !== 'undefined') {
  // 延迟启动健康检查，避免router循环依赖
  setTimeout(async () => {
    try {
      const { default: router } = await import('@/router')
      if (router.currentRoute.value.name !== 'Login') {
        HealthChecker.startPeriodicCheck()
      }
    } catch (error) {
      console.warn('无法导入router，跳过健康检查:', error)
      // 如果无法导入router，直接启动健康检查
      HealthChecker.startPeriodicCheck()
    }
  }, 100)
}

export default axiosInstance 