const { Employee, Department, Position, User } = require('../models');
const { ValidationError, NotFoundError, ConflictError } = require('../shared/errors');
const { Op } = require('sequelize');

class EmployeeService {
  // 获取员工列表（支持分页和筛选）
  async getEmployeeList(options = {}, userPermissions = {}) {
    const {
      page = 1,
      pageSize = 20,
      departmentId,
      positionId,
      status,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = options;

    const queryOptions = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder
    };

    // 根据用户权限筛选员工
    if (userPermissions.restrictToDepartment) {
      queryOptions.departmentId = userPermissions.departmentId;
    } else if (departmentId) {
      queryOptions.departmentId = departmentId;
    }

    if (positionId) {
      queryOptions.positionId = positionId;
    }

    if (status) {
      queryOptions.status = status;
    }

    if (keyword) {
      queryOptions.keyword = keyword;
    }

    return await Employee.getEmployeeList(queryOptions);
  }

  // 根据ID获取员工详情
  async getEmployeeById(id, userPermissions = {}) {
    const employee = await Employee.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'real_name', 'phone', 'avatar', 'status']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name', 'code']
        },
        {
          model: Position,
          as: 'position',
          attributes: ['id', 'name', 'level', 'level_name']
        },
        {
          model: Employee,
          as: 'manager',
          attributes: ['id', 'employee_no'],
          include: [{
            model: User,
            as: 'user',
            attributes: ['real_name']
          }]
        }
      ]
    });

    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 权限检查
    if (userPermissions.restrictToDepartment && 
        employee.department_id !== userPermissions.departmentId) {
      throw new NotFoundError('员工不存在');
    }

    return employee;
  }

  // 创建员工
  async createEmployee(employeeData, userId) {
    const { userData, ...empData } = employeeData;

    // 验证必填字段
    if (!userData || !empData.department_id || !empData.position_id || !empData.hire_date) {
      throw new ValidationError('缺少必填字段');
    }

    // 验证部门和职位是否存在
    const department = await Department.findByPk(empData.department_id);
    if (!department) {
      throw new ValidationError('指定的部门不存在');
    }

    const position = await Position.findByPk(empData.position_id);
    if (!position) {
      throw new ValidationError('指定的职位不存在');
    }

    // 验证直属上级（如果指定）
    if (empData.manager_id) {
      const manager = await Employee.findByPk(empData.manager_id);
      if (!manager) {
        throw new ValidationError('指定的直属上级不存在');
      }
    }

    // 生成员工编号
          const employeeCode = await Employee.generateEmployeeNo();

    try {
      // 创建用户记录（如果需要）
      let user;
      if (userData.create_user) {
        // 这里可以调用用户服务创建用户账号
        // user = await userService.createUser(userData);
        
        // 暂时使用传入的user_id
        if (!userData.user_id) {
          throw new ValidationError('必须指定关联的用户ID');
        }
        
        user = await User.findByPk(userData.user_id);
        if (!user) {
          throw new ValidationError('指定的用户不存在');
        }
      } else {
        user = await User.findByPk(userData.user_id);
        if (!user) {
          throw new ValidationError('指定的用户不存在');
        }
      }

      // 检查用户是否已经关联员工
      const existingEmployee = await Employee.findOne({
        where: { user_id: user.id }
      });

      if (existingEmployee) {
        throw new ConflictError('该用户已经关联了员工记录');
      }

      // 创建员工记录
      const employee = await Employee.create({
        ...empData,
        user_id: user.id,
        employee_no: employeeCode
      });

      // 返回完整的员工信息
      return await this.getEmployeeById(employee.id);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new ConflictError('员工编号已存在');
      }
      throw error;
    }
  }

  // 更新员工信息
  async updateEmployee(id, updateData, userId, userPermissions = {}) {
    const employee = await Employee.findByPk(id);
    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 权限检查
    if (userPermissions.restrictToDepartment && 
        employee.department_id !== userPermissions.departmentId) {
      throw new NotFoundError('员工不存在');
    }

    const { userData, ...empData } = updateData;

    // 验证部门和职位（如果更新）
    if (empData.department_id) {
      const department = await Department.findByPk(empData.department_id);
      if (!department) {
        throw new ValidationError('指定的部门不存在');
      }
    }

    if (empData.position_id) {
      const position = await Position.findByPk(empData.position_id);
      if (!position) {
        throw new ValidationError('指定的职位不存在');
      }
    }

    // 验证直属上级（如果更新）
    if (empData.manager_id) {
      const manager = await Employee.findByPk(empData.manager_id);
      if (!manager) {
        throw new ValidationError('指定的直属上级不存在');
      }

      // 防止循环引用
      if (empData.manager_id === id) {
        throw new ValidationError('不能将自己设为直属上级');
      }
    }

    try {
      // 更新员工信息
      await employee.update(empData);

      // 更新用户信息（如果提供）
      if (userData) {
        const user = await User.findByPk(employee.user_id);
        if (user) {
          await user.update(userData);
        }
      }

      // 返回更新后的员工信息
      return await this.getEmployeeById(id);
    } catch (error) {
      throw error;
    }
  }

  // 删除员工
  async deleteEmployee(id, userId, userPermissions = {}) {
    const employee = await Employee.findByPk(id);
    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 权限检查
    if (userPermissions.restrictToDepartment && 
        employee.department_id !== userPermissions.departmentId) {
      throw new NotFoundError('员工不存在');
    }

    // 检查是否有下属员工
    const subordinatesCount = await Employee.count({
      where: { manager_id: id }
    });

    if (subordinatesCount > 0) {
      throw new ConflictError('该员工还有下属，无法删除');
    }

    // 检查是否是部门负责人
    const managedDepartments = await Department.count({
      where: { manager_id: id }
    });

    if (managedDepartments > 0) {
      throw new ConflictError('该员工是部门负责人，无法删除');
    }

    await employee.destroy();
    return { message: '员工删除成功' };
  }

  // 获取部门员工
  async getDepartmentEmployees(departmentId, userPermissions = {}) {
    // 权限检查
    if (userPermissions.restrictToDepartment && 
        departmentId !== userPermissions.departmentId) {
      throw new ValidationError('没有权限查看该部门员工');
    }

    const department = await Department.findByPk(departmentId);
    if (!department) {
      throw new NotFoundError('部门不存在');
    }

    return await Employee.getEmployeesByDepartment(departmentId);
  }

  // 获取员工的下属
  async getEmployeeSubordinates(id, userPermissions = {}) {
    const employee = await Employee.findByPk(id);
    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 权限检查
    if (userPermissions.restrictToDepartment && 
        employee.department_id !== userPermissions.departmentId) {
      throw new NotFoundError('员工不存在');
    }

    return await employee.getSubordinates();
  }

  // 转移员工部门
  async transferEmployee(id, newDepartmentId, newPositionId, userId, userPermissions = {}) {
    const employee = await Employee.findByPk(id);
    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 权限检查
    if (userPermissions.restrictToDepartment && 
        employee.department_id !== userPermissions.departmentId) {
      throw new NotFoundError('员工不存在');
    }

    // 验证新部门
    const newDepartment = await Department.findByPk(newDepartmentId);
    if (!newDepartment) {
      throw new ValidationError('目标部门不存在');
    }

    // 验证新职位
    if (newPositionId) {
      const newPosition = await Position.findByPk(newPositionId);
      if (!newPosition) {
        throw new ValidationError('目标职位不存在');
      }
    }

    const updateData = {
      department_id: newDepartmentId,
      manager_id: null // 转移时清空直属上级，需要重新指定
    };

    if (newPositionId) {
      updateData.position_id = newPositionId;
    }

    await employee.update(updateData);

    return await this.getEmployeeById(id);
  }

  // 批量更新员工状态
  async batchUpdateStatus(employeeIds, status, userId, userPermissions = {}) {
    if (!Array.isArray(employeeIds) || employeeIds.length === 0) {
      throw new ValidationError('员工ID列表不能为空');
    }

    if (!['active', 'inactive', 'resigned'].includes(status)) {
      throw new ValidationError('无效的员工状态');
    }

    let whereCondition = { id: { [Op.in]: employeeIds } };

    // 权限检查
    if (userPermissions.restrictToDepartment) {
      whereCondition.department_id = userPermissions.departmentId;
    }

    const [affectedCount] = await Employee.update(
      { status },
      { where: whereCondition }
    );

    return { 
      message: `成功更新${affectedCount}名员工的状态`,
      affectedCount 
    };
  }

  // 员工统计信息
  async getEmployeeStats(userPermissions = {}) {
    let whereCondition = {};

    // 权限检查
    if (userPermissions.restrictToDepartment) {
      whereCondition.department_id = userPermissions.departmentId;
    }

    const [total, active, inactive, resigned] = await Promise.all([
      Employee.count({ where: whereCondition }),
      Employee.count({ where: { ...whereCondition, status: 'active' } }),
      Employee.count({ where: { ...whereCondition, status: 'inactive' } }),
      Employee.count({ where: { ...whereCondition, status: 'resigned' } })
    ]);

    // 按部门统计
    const departmentStats = await Employee.findAll({
      where: whereCondition,
      attributes: [
        'department_id',
        [Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'count']
      ],
      include: [{
        model: Department,
        as: 'department',
        attributes: ['name']
      }],
      group: ['department_id'],
      raw: true
    });

    // 按职位统计
    const positionStats = await Employee.findAll({
      where: whereCondition,
      attributes: [
        'position_id',
        [Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'count']
      ],
      include: [{
        model: Position,
        as: 'position',
        attributes: ['name']
      }],
      group: ['position_id'],
      raw: true
    });

    return {
      total,
      statusStats: {
        active,
        inactive,
        resigned
      },
      departmentStats,
      positionStats
    };
  }
}

module.exports = new EmployeeService(); 