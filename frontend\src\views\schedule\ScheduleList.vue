<template>
  <div class="schedule-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>日程安排</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建日程
      </el-button>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filterForm.type" placeholder="选择类型" clearable @change="loadScheduleList">
            <el-option label="个人" value="personal" />
            <el-option label="工作" value="work" />
            <el-option label="会议" value="meeting" />
            <el-option label="提醒" value="reminder" />
            <el-option label="假期" value="holiday" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable @change="loadScheduleList">
            <el-option label="待处理" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadScheduleList">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 日程列表 -->
    <div class="schedule-list">
      <el-table
        :data="schedules"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.end_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地点" width="120" />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editSchedule(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="markCompleted(row)" v-if="row.status === 'pending'">
              完成
            </el-button>
            <el-button type="danger" size="small" @click="deleteSchedule(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑日程对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editMode ? '编辑日程' : '新建日程'"
      width="600px"
      @close="resetScheduleForm"
    >
      <el-form
        ref="scheduleFormRef"
        :model="scheduleForm"
        :rules="scheduleRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="scheduleForm.title" placeholder="请输入日程标题" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="scheduleForm.type" placeholder="选择类型">
            <el-option label="个人" value="personal" />
            <el-option label="工作" value="work" />
            <el-option label="会议" value="meeting" />
            <el-option label="提醒" value="reminder" />
            <el-option label="假期" value="holiday" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="start_time">
          <el-date-picker
            v-model="scheduleForm.start_time"
            type="datetime"
            placeholder="选择开始时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="end_time">
          <el-date-picker
            v-model="scheduleForm.end_time"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="地点">
          <el-input v-model="scheduleForm.location" placeholder="请输入地点" />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="scheduleForm.priority" placeholder="选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="提醒时间">
          <el-select v-model="scheduleForm.reminder_minutes" placeholder="选择提醒时间">
            <el-option label="不提醒" :value="0" />
            <el-option label="5分钟前" :value="5" />
            <el-option label="15分钟前" :value="15" />
            <el-option label="30分钟前" :value="30" />
            <el-option label="1小时前" :value="60" />
            <el-option label="1天前" :value="1440" />
          </el-select>
        </el-form-item>
        <el-form-item label="全天">
          <el-switch v-model="scheduleForm.is_all_day" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="scheduleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入日程描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSchedule" :loading="saving">
            {{ editMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import axios from '@/utils/axios'
import router from '@/router'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const schedules = ref([])
const showCreateDialog = ref(false)
const editMode = ref(false)
const scheduleFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  type: '',
  status: ''
})

// 日程表单
const scheduleForm = reactive({
  id: null,
  title: '',
  type: 'personal',
  start_time: '',
  end_time: '',
  location: '',
  priority: 'medium',
  reminder_minutes: 15,
  is_all_day: false,
  description: ''
})

// 表单验证规则
const scheduleRules = {
  title: [
    { required: true, message: '请输入日程标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择日程类型', trigger: 'change' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 方法定义
const loadScheduleList = async (showRetryOption = false) => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0].toISOString()
      params.end_date = filterForm.dateRange[1].toISOString()
    }

    if (filterForm.type) {
      params.type = filterForm.type
    }

    if (filterForm.status) {
      params.status = filterForm.status
    }

    const response = await axios.get('/v1/schedules', { 
      params,
      errorContext: { operation: '获取日程列表' }
    })

    if (response.data.success) {
      const responseData = response.data.data || {}
      const paginationData = response.data.pagination || responseData.pagination || {}

      schedules.value = responseData.schedules || responseData || []
      pagination.total = paginationData.total || 0
      pagination.page = paginationData.page || 1
      pagination.size = paginationData.size || paginationData.pageSize || 20

      console.log('✅ 日程数据加载成功:', {
        count: schedules.value.length,
        total: pagination.total,
        page: pagination.page
      })

      // 如果之前有错误，现在成功了，显示恢复消息
      if (showRetryOption) {
        ElMessage.success('日程列表加载成功')
      }
    }
  } catch (error) {
    console.error('获取日程列表失败:', error)
    
    // 使用增强的错误处理
    handleScheduleListError(error, 'load')
  } finally {
    loading.value = false
  }
}

// 增强的错误处理函数
const handleScheduleListError = (error, operation = 'load') => {
  const classification = error.classification || { severity: 'medium', canRetry: false }
  
  // 根据错误类型提供不同的处理方案
  if (classification.canRetry) {
    ElMessageBox.confirm(
      `${error.userMessage || '获取日程列表失败'}，是否重试？`,
      '操作失败',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false
      }
    ).then(() => {
      // 用户选择重试
      if (operation === 'load') {
        loadScheduleList(true)
      }
    }).catch(() => {
      // 用户取消，显示离线提示
      if (classification.isNetworkError) {
        showOfflineMode()
      }
    })
  } else if (classification.needsAuth) {
    // 认证错误，提示重新登录
    ElMessageBox.alert(
      '登录已过期，请重新登录',
      '认证失败',
      {
        confirmButtonText: '重新登录',
        type: 'warning'
      }
    ).then(() => {
      // 跳转到登录页
      router.push('/login')
    })
  } else {
    // 其他错误，显示错误信息和建议
    const suggestions = getSuggestionsByError(classification)
    if (suggestions.length > 0) {
      ElNotification({
        title: '操作建议',
        message: suggestions.join('；'),
        type: 'info',
        duration: 8000
      })
    }
  }
}

// 根据错误类型提供建议
const getSuggestionsByError = (classification) => {
  const suggestions = []
  
  switch (classification.suggestedAction) {
    case 'check_network':
      suggestions.push('请检查网络连接')
      suggestions.push('尝试刷新页面')
      break
    case 'check_server':
      suggestions.push('服务器可能暂时不可用')
      suggestions.push('请稍后再试或联系管理员')
      break
    case 'refresh_page':
      suggestions.push('请刷新页面重试')
      break
    case 'contact_support':
      suggestions.push('请联系技术支持')
      break
    default:
      suggestions.push('请稍后重试')
  }
  
  return suggestions
}

// 离线模式提示
const showOfflineMode = () => {
  ElNotification({
    title: '离线模式',
    message: '当前网络不可用，部分功能受限。网络恢复后将自动同步数据。',
    type: 'warning',
    duration: 0, // 不自动关闭
    showClose: true
  })
}

const saveSchedule = async () => {
  try {
    await scheduleFormRef.value.validate()

    if (new Date(scheduleForm.start_time) >= new Date(scheduleForm.end_time)) {
      ElMessage.error('结束时间必须晚于开始时间')
      return
    }

    saving.value = true

    const scheduleData = {
      title: scheduleForm.title,
      type: scheduleForm.type,
      start_time: scheduleForm.start_time,
      end_time: scheduleForm.end_time,
      location: scheduleForm.location,
      priority: scheduleForm.priority,
      reminder_minutes: scheduleForm.reminder_minutes,
      is_all_day: scheduleForm.is_all_day,
      description: scheduleForm.description
    }

    const operation = editMode.value ? '更新日程' : '创建日程'
    const config = { errorContext: { operation } }

    if (editMode.value) {
      await axios.put(`/v1/schedules/${scheduleForm.id}`, scheduleData, config)
      ElMessage.success('日程更新成功')
    } else {
      await axios.post('/v1/schedules', scheduleData, config)
      ElMessage.success('日程创建成功')
    }

    showCreateDialog.value = false
    resetScheduleForm()
    await loadScheduleList()
  } catch (error) {
    console.error('保存日程失败:', error)
    
    // 使用增强的错误处理
    handleSaveScheduleError(error)
  } finally {
    saving.value = false
  }
}

// 保存日程错误处理
const handleSaveScheduleError = (error) => {
  const classification = error.classification || { severity: 'medium', canRetry: false }
  
  if (classification.type === 'VALIDATION_ERROR') {
    // 验证错误，显示具体的验证信息
    const errorData = error.response?.data?.error
    if (errorData?.validationErrors) {
      const validationMessages = errorData.validationErrors.map(err => 
        `${err.field}: ${err.message}`
      ).join('\n')
      
      ElMessageBox.alert(
        validationMessages,
        '数据验证失败',
        { type: 'warning' }
      )
    } else {
      ElMessage.warning(error.userMessage || '请检查输入数据的格式和完整性')
    }
  } else if (classification.type === 'CONFLICT_ERROR') {
    // 冲突错误
    ElMessageBox.alert(
      error.userMessage || '日程时间冲突，请选择其他时间',
      '时间冲突',
      { type: 'warning' }
    )
  } else if (classification.canRetry) {
    // 可重试的错误
    ElMessageBox.confirm(
      `${error.userMessage || '保存失败'}，是否重试？`,
      '保存失败',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      saveSchedule()
    })
  } else {
    // 其他错误
    ElMessage.error(error.userMessage || '保存失败，请稍后重试')
  }
}

const editSchedule = (schedule) => {
  editMode.value = true
  scheduleForm.id = schedule.id
  scheduleForm.title = schedule.title
  scheduleForm.type = schedule.type
  scheduleForm.start_time = new Date(schedule.start_time)
  scheduleForm.end_time = new Date(schedule.end_time)
  scheduleForm.location = schedule.location || ''
  scheduleForm.priority = schedule.priority
  scheduleForm.reminder_minutes = schedule.reminder_minutes
  scheduleForm.is_all_day = schedule.is_all_day
  scheduleForm.description = schedule.description || ''
  showCreateDialog.value = true
}

const deleteSchedule = async (id) => {
  try {
    await ElMessageBox.confirm('确认删除这个日程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.delete(`/v1/schedules/${id}`)
    ElMessage.success('日程删除成功')
    await loadScheduleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除日程失败:', error)
      ElMessage.error('删除日程失败')
    }
  }
}

const markCompleted = async (schedule) => {
  try {
    await axios.put(`/v1/schedules/${schedule.id}`, { status: 'completed' })
    ElMessage.success('日程已标记为完成')
    await loadScheduleList()
  } catch (error) {
    console.error('标记完成失败:', error)
    ElMessage.error('标记完成失败')
  }
}

const resetScheduleForm = () => {
  editMode.value = false
  scheduleForm.id = null
  scheduleForm.title = ''
  scheduleForm.type = 'personal'
  scheduleForm.start_time = ''
  scheduleForm.end_time = ''
  scheduleForm.location = ''
  scheduleForm.priority = 'medium'
  scheduleForm.reminder_minutes = 15
  scheduleForm.is_all_day = false
  scheduleForm.description = ''
}

const resetFilter = () => {
  filterForm.dateRange = []
  filterForm.type = ''
  filterForm.status = ''
  loadScheduleList()
}

const handleDateRangeChange = () => {
  loadScheduleList()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadScheduleList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadScheduleList()
}

// 工具方法
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getTypeLabel = (type) => {
  const labels = {
    personal: '个人',
    work: '工作',
    meeting: '会议',
    reminder: '提醒',
    holiday: '假期'
  }
  return labels[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    personal: '',
    work: 'success',
    meeting: 'warning',
    reminder: 'info',
    holiday: 'danger'
  }
  return types[type] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return labels[priority] || priority
}

const getPriorityTagType = (priority) => {
  const types = {
    low: 'info',
    medium: '',
    high: 'warning',
    urgent: 'danger'
  }
  return types[priority] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return types[status] || ''
}

// 页面加载时获取数据
onMounted(() => {
  loadScheduleList()
})
</script>

<style scoped>
.schedule-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
