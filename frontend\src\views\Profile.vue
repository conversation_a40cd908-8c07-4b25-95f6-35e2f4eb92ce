<template>
  <div class="profile-page">
    <div class="profile-header">
      <h1>个人资料</h1>
      <p>管理您的个人信息</p>
    </div>

    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-button type="primary" @click="showEditDialog = true">
            <el-icon><Edit /></el-icon>
            编辑资料
          </el-button>
        </div>
      </template>

      <div class="profile-content">
        <div class="avatar-section">
          <el-avatar
            :src="authStore.user?.avatar"
            :size="120"
            shape="square"
            class="user-avatar"
          >
            <el-icon :size="60"><User /></el-icon>
          </el-avatar>
        </div>

        <div class="info-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名" :span="1">
              {{ authStore.user?.real_name || authStore.user?.username || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱" :span="1">
              {{ authStore.user?.email || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号" :span="1">
              {{ authStore.user?.phone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="性别" :span="1">
              {{ getGenderText(authStore.user?.gender) }}
            </el-descriptions-item>
            <el-descriptions-item label="生日" :span="1">
              {{ authStore.user?.birth_date || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间" :span="1">
              {{ formatDate(authStore.user?.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <span>安全设置</span>
        </div>
      </template>

      <div class="security-content">
        <div class="security-item">
          <div class="security-info">
            <h4>登录密码</h4>
            <p class="security-desc">定期更新密码有助于保护账户安全</p>
          </div>
          <el-button @click="showPasswordDialog = true">修改密码</el-button>
        </div>
      </div>
    </el-card>

    <!-- 编辑资料对话框 -->
    <ProfileForm
      v-model:visible="showEditDialog"
      @success="handleProfileUpdate"
    />

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="handlePasswordChange" :loading="passwordLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, User } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import ProfileForm from '@/components/ProfileForm.vue'

// Store
const authStore = useAuthStore()

// 响应式数据
const showEditDialog = ref(false)
const showPasswordDialog = ref(false)
const passwordLoading = ref(false)
const passwordFormRef = ref()

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return '未知'
  }
}

// 获取性别文本
const getGenderText = (gender) => {
  switch (gender) {
    case 'male':
      return '男'
    case 'female':
      return '女'
    default:
      return '未设置'
  }
}

// 处理资料更新
const handleProfileUpdate = () => {
  ElMessage.success('个人资料更新成功')
  // 刷新用户信息
  authStore.fetchUserProfile().catch(console.error)
}

// 处理密码修改
const handlePasswordChange = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    await authStore.changePassword(
      passwordForm.currentPassword,
      passwordForm.newPassword
    )

    ElMessage.success('密码修改成功')
    showPasswordDialog.value = false
    resetPasswordForm()
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error || '密码修改失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 页面加载时获取最新用户信息
onMounted(() => {
  if (authStore.isAuthenticated) {
    authStore.fetchUserProfile().catch(console.error)
  }
})
</script>

<style lang="scss" scoped>
.profile-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-header {
  margin-bottom: 24px;
  text-align: center;

  h1 {
    font-size: 28px;
    color: #303133;
    margin-bottom: 8px;
  }

  p {
    color: #606266;
    font-size: 16px;
  }
}

.profile-card,
.security-card {
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.profile-content {
  display: flex;
  gap: 32px;
  align-items: flex-start;

  .avatar-section {
    flex-shrink: 0;
    text-align: center;

    .user-avatar {
      border: 2px solid #f0f0f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .info-section {
    flex: 1;
  }
}

.security-content {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .security-info {
      h4 {
        margin: 0 0 4px 0;
        color: #303133;
        font-size: 16px;
      }

      .security-desc {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .profile-page {
    padding: 12px;
  }

  .profile-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .security-info {
      text-align: left;
    }
  }
}
</style> 