const employeeService = require('../services/employeeService');
const departmentService = require('../services/departmentService');
const { success, successWithPagination, error } = require('../shared/response');
const { asyncHandler } = require('../middleware/asyncHandler');

class EmployeeController {
  // 获取员工列表
  getEmployees = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 20,
      departmentId,
      positionId,
      status,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    // 根据用户权限确定可查看的范围
    const userPermissions = await this.getUserPermissions(req.user);

    const result = await employeeService.getEmployeeList({
      page,
      pageSize,
      departmentId,
      positionId,
      status,
      keyword,
      sortBy,
      sortOrder
    }, userPermissions);

    // 使用分页响应格式
    successWithPagination(res, result.employees, {
      page: result.page,
      pageSize: result.pageSize,
      total: result.total,
      totalPages: result.totalPages
    }, '获取员工列表成功');
  });

  // 获取员工详情
  getEmployee = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userPermissions = await this.getUserPermissions(req.user);

    const employee = await employeeService.getEmployeeById(id, userPermissions);
    success(res, employee, '获取员工详情成功');
  });

  // 创建员工
  createEmployee = asyncHandler(async (req, res) => {
    const employeeData = req.body;
    const userId = req.user.id;

    const employee = await employeeService.createEmployee(employeeData, userId);
    success(res, employee, '创建员工成功', 201);
  });

  // 更新员工信息
  updateEmployee = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user.id;
    const userPermissions = await this.getUserPermissions(req.user);

    const employee = await employeeService.updateEmployee(id, updateData, userId, userPermissions);
    success(res, employee, '更新员工信息成功');
  });

  // 删除员工
  deleteEmployee = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;
    const userPermissions = await this.getUserPermissions(req.user);

    const result = await employeeService.deleteEmployee(id, userId, userPermissions);
    success(res, result, '删除员工成功');
  });

  // 获取部门员工
  getDepartmentEmployees = asyncHandler(async (req, res) => {
    const { departmentId } = req.params;
    const userPermissions = await this.getUserPermissions(req.user);

    const employees = await employeeService.getDepartmentEmployees(departmentId, userPermissions);
    success(res, employees, '获取部门员工成功');
  });

  // 获取员工下属
  getEmployeeSubordinates = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userPermissions = await this.getUserPermissions(req.user);

    const subordinates = await employeeService.getEmployeeSubordinates(id, userPermissions);
    success(res, subordinates, '获取员工下属成功');
  });

  // 转移员工部门
  transferEmployee = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { newDepartmentId, newPositionId } = req.body;
    const userId = req.user.id;
    const userPermissions = await this.getUserPermissions(req.user);

    const employee = await employeeService.transferEmployee(
      id, 
      newDepartmentId, 
      newPositionId, 
      userId, 
      userPermissions
    );
    success(res, employee, '员工转移成功');
  });

  // 批量更新员工状态
  batchUpdateStatus = asyncHandler(async (req, res) => {
    const { employeeIds, status } = req.body;
    const userId = req.user.id;
    const userPermissions = await this.getUserPermissions(req.user);

    const result = await employeeService.batchUpdateStatus(employeeIds, status, userId, userPermissions);
    success(res, result, '批量更新员工状态成功');
  });

  // 获取员工统计信息
  getEmployeeStats = asyncHandler(async (req, res) => {
    const userPermissions = await this.getUserPermissions(req.user);

    const stats = await employeeService.getEmployeeStats(userPermissions);
    success(res, stats, '获取员工统计信息成功');
  });

  // 获取部门树形结构（用于选择）
  getDepartmentTree = asyncHandler(async (req, res) => {
    const departments = await departmentService.getDepartmentTree();
    success(res, departments, '获取部门树形结构成功');
  });

  // 获取职位列表（用于选择）
  getPositionsForSelect = asyncHandler(async (req, res) => {
    const { Position } = require('../models');
    const positions = await Position.getAllPositionsForSelect();
    success(res, positions, '获取职位列表成功');
  });

  // 获取可选择的上级员工列表
  getManagerOptions = asyncHandler(async (req, res) => {
    const { departmentId } = req.query;
    const userPermissions = await this.getUserPermissions(req.user);

    let options = {
      page: 1,
      pageSize: 100,
      status: 'active'
    };

    if (departmentId) {
      options.departmentId = departmentId;
    }

    const result = await employeeService.getEmployeeList(options, userPermissions);
    
    // 只返回基本信息
    const managers = result.employees.map(emp => ({
      id: emp.id,
              employee_no: emp.employee_no,
      real_name: emp.user?.real_name,
      department_name: emp.department?.name,
      position_name: emp.position?.name
    }));

    success(res, managers, '获取可选择的上级员工列表成功');
  });

  // 获取当前用户的员工信息
  getCurrentEmployeeInfo = asyncHandler(async (req, res) => {
    const { Employee } = require('../models');
    
    const employee = await Employee.findOne({
      where: { user_id: req.user.id },
      include: [
        {
          model: require('../models').Department,
          as: 'department',
          attributes: ['id', 'name', 'code']
        },
        {
          model: require('../models').Position,
          as: 'position',
          attributes: ['id', 'name', 'level']
        }
      ]
    });

    if (!employee) {
      return error(res, '当前用户未关联员工信息', 404);
    }

    success(res, employee, '获取当前用户员工信息成功');
  });

  // 权限辅助方法：根据用户角色确定权限
  async getUserPermissions(user) {
    const permissions = {
      restrictToDepartment: false,
      departmentId: null
    };

    try {
      // 如果用户有employee权限但没有employee:all权限，限制只能查看同部门
      const hasEmployeeRead = await user.hasPermission('employee:read');
      const hasEmployeeAll = await user.hasPermission('employee:all');
      
      if (hasEmployeeRead && !hasEmployeeAll) {
        // 获取用户的部门信息（这里需要通过员工表关联获取）
        // 暂时使用简单逻辑，实际应该查询用户关联的员工信息
        permissions.restrictToDepartment = true;
        // permissions.departmentId = user.employee?.department_id;
      }
    } catch (error) {
      console.log('权限检查失败:', error.message);
      // 默认限制权限以确保安全
      permissions.restrictToDepartment = true;
    }

    return permissions;
  }

  // 导出员工数据
  exportEmployees = asyncHandler(async (req, res) => {
    const userPermissions = await this.getUserPermissions(req.user);
    
    const result = await employeeService.getEmployeeList({
      page: 1,
      pageSize: 10000, // 导出全部
      ...req.query
    }, userPermissions);

    // 格式化导出数据
    const exportData = result.employees.map(emp => ({
              '员工编号': emp.employee_no,
      '姓名': emp.user?.real_name,
      '用户名': emp.user?.username,
      '邮箱': emp.user?.email,
      '手机': emp.user?.phone,
      '部门': emp.department?.name,
      '职位': emp.position?.name,
      '入职日期': emp.hire_date,
      '状态': emp.status === 'active' ? '在职' : emp.status === 'inactive' ? '停职' : '离职',
      '工作地点': emp.work_location,
      '薪资': emp.salary,
      '直属上级': emp.manager?.user?.real_name || '',
      '创建时间': emp.created_at
    }));

    success(res, {
      data: exportData,
      total: result.total,
      filename: `员工数据_${new Date().toISOString().split('T')[0]}.xlsx`
    }, '导出员工数据成功');
  });
}

module.exports = new EmployeeController(); 