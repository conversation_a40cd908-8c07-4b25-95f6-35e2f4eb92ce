import { defineStore } from 'pinia'
import axios from '@/utils/axios'

export const useSupplyStore = defineStore('supply', {
  state: () => ({
    // 办公用品相关
    supplies: [],
    currentSupply: null,
    categories: [
      { value: 'stationery', label: '文具用品' },
      { value: 'electronics', label: '电子设备' },
      { value: 'furniture', label: '办公家具' },
      { value: 'consumables', label: '消耗品' },
      { value: 'equipment', label: '办公设备' },
      { value: 'other', label: '其他' }
    ],
    
    // 申请相关
    requests: [],
    myRequests: [],
    currentRequest: null,
    
    // 状态管理
    loading: false,
    pagination: {
      supplies: { page: 1, size: 20, total: 0, totalPages: 0 },
      requests: { page: 1, size: 20, total: 0, totalPages: 0 }
    },
    filters: {
      supplies: { search: '', category: '', status: '' },
      requests: { status: '', urgency: '' }
    }
  }),

  getters: {
    // 获取低库存用品
    lowStockSupplies() {
      return this.supplies.filter(supply => 
        supply.current_stock <= supply.min_stock
      )
    },

    // 获取按类别分组的用品
    suppliesByCategory() {
      return this.supplies.reduce((acc, supply) => {
        if (!acc[supply.category]) {
          acc[supply.category] = []
        }
        acc[supply.category].push(supply)
        return acc
      }, {})
    },

    // 获取待处理申请数量
    pendingRequestCount() {
      return this.requests.filter(request => 
        request.status === 'pending'
      ).length
    },

    // 获取我的待处理申请
    myPendingRequests() {
      return this.myRequests.filter(request => 
        request.status === 'pending'
      )
    },

    // 获取紧急申请
    urgentRequests() {
      return this.requests.filter(request => 
        request.urgency === 'urgent' && request.status === 'pending'
      )
    }
  },

  actions: {
    /**
     * 获取办公用品列表
     */
    async fetchSupplies(params = {}) {
      try {
        this.loading = true
        console.log('🔍 获取办公用品列表请求:', params)

        const queryParams = {
          page: params.page || this.pagination.supplies.page,
          size: params.size || this.pagination.supplies.size,
          ...this.filters.supplies,
          ...params
        }

        const response = await axios.get('/v1/office-supplies', { params: queryParams })

        console.log('✅ 办公用品列表响应:', response.data)

        if (response.data.success) {
          const responseData = response.data.data || {}
          
          // 确保用品数据是数组
          const supplyData = responseData.supplies || responseData || []
          if (!Array.isArray(supplyData)) {
            console.error('办公用品数据不是数组格式:', supplyData)
            this.supplies = []
          } else {
            this.supplies = supplyData
          }

          // 更新分页信息
          if (responseData.pagination) {
            this.pagination.supplies = {
              page: responseData.pagination.page || 1,
              size: responseData.pagination.size || 20,
              total: responseData.pagination.total || 0,
              totalPages: responseData.pagination.totalPages || 0
            }
          }

          console.log('✅ 办公用品数据加载成功:', this.supplies.length)
        } else {
          console.error('办公用品API返回失败:', response.data.message)
          this.supplies = []
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取办公用品列表失败:', error)
        // API失败时使用模拟数据
        this.supplies = [
          {
            id: 1,
            name: '中性笔',
            code: 'PEN001',
            category: 'stationery',
            brand: '晨光',
            unit: '支',
            unit_price: 2.50,
            current_stock: 50,
            min_stock: 20,
            status: 'active'
          },
          {
            id: 2,
            name: 'A4复印纸',
            code: 'PAPER001',
            category: 'consumables',
            brand: '得力',
            unit: '包',
            unit_price: 25.00,
            current_stock: 5,
            min_stock: 10,
            status: 'active'
          }
        ]
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取申请列表
     */
    async fetchRequests(params = {}) {
      try {
        this.loading = true
        console.log('🔍 获取申请列表请求:', params)

        const queryParams = {
          page: params.page || this.pagination.requests.page,
          size: params.size || this.pagination.requests.size,
          ...this.filters.requests,
          ...params
        }

        const response = await axios.get('/v1/supply-requests', { params: queryParams })

        console.log('✅ 申请列表响应:', response.data)

        if (response.data.success) {
          const responseData = response.data.data || {}
          
          // 确保申请数据是数组
          const requestData = responseData.requests || responseData || []
          if (!Array.isArray(requestData)) {
            console.error('申请数据不是数组格式:', requestData)
            this.requests = []
          } else {
            this.requests = requestData
          }

          // 更新分页信息
          if (responseData.pagination) {
            this.pagination.requests = {
              page: responseData.pagination.page || 1,
              size: responseData.pagination.size || 20,
              total: responseData.pagination.total || 0,
              totalPages: responseData.pagination.totalPages || 0
            }
          }

          console.log('✅ 申请数据加载成功:', this.requests.length)
        } else {
          console.error('申请API返回失败:', response.data.message)
          this.requests = []
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取申请列表失败:', error)
        // API失败时使用模拟数据
        this.requests = [
          {
            id: 1,
            request_no: 'REQ20250618001',
            supply: { name: '中性笔', unit: '支' },
            requester: { real_name: '张三' },
            quantity: 10,
            purpose: '日常办公使用',
            urgency: 'medium',
            status: 'pending',
            created_at: new Date().toISOString()
          }
        ]
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 创建申请
     */
    async createRequest(requestData) {
      try {
        this.loading = true
        console.log('🔧 创建申请:', requestData)

        const response = await axios.post('/v1/supply-requests', requestData)

        if (response.data.success) {
          const newRequest = response.data.data
          this.requests.unshift(newRequest)
          this.myRequests.unshift(newRequest)
          console.log('✅ 申请创建成功')
        }

        return response.data
      } catch (error) {
        console.error('❌ 创建申请失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取用品详情
     */
    async fetchSupplyById(id) {
      try {
        console.log('🔍 获取用品详情:', id)
        
        const response = await axios.get(`/v1/office-supplies/${id}`)
        
        if (response.data.success) {
          this.currentSupply = response.data.data
          console.log('✅ 用品详情获取成功')
        } else {
          this.currentSupply = null
        }

        return response.data
      } catch (error) {
        console.error('❌ 获取用品详情失败:', error)
        this.currentSupply = null
        throw error
      }
    },

    /**
     * 更新过滤条件
     */
    updateSupplyFilters(filters) {
      this.filters.supplies = { ...this.filters.supplies, ...filters }
    },

    updateRequestFilters(filters) {
      this.filters.requests = { ...this.filters.requests, ...filters }
    },

    /**
     * 清除当前数据
     */
    clearCurrentSupply() {
      this.currentSupply = null
    },

    clearCurrentRequest() {
      this.currentRequest = null
    },

    /**
     * 重置状态
     */
    resetState() {
      this.supplies = []
      this.currentSupply = null
      this.requests = []
      this.myRequests = []
      this.currentRequest = null
      this.pagination = {
        supplies: { page: 1, size: 20, total: 0, totalPages: 0 },
        requests: { page: 1, size: 20, total: 0, totalPages: 0 }
      }
      this.filters = {
        supplies: { search: '', category: '', status: '' },
        requests: { status: '', urgency: '' }
      }
    }
  }
})
