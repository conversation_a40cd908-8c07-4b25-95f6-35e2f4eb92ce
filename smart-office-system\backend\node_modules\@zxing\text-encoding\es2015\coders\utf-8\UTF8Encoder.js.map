{"version": 3, "file": "UTF8Encoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/utf-8/UTF8Encoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC7E,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEnD;;;;GAIG;AACH,MAAM,OAAO,WAAW;IAItB,YAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,+DAA+D;QAC/D,IAAI,KAAa,EAAE,MAAc,CAAC;QAClC,+BAA+B;QAC/B,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;YACvC,aAAa;YACb,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,IAAI,CAAC;SACf;QACD,+BAA+B;aAC1B,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;YAC5C,aAAa;YACb,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,IAAI,CAAC;SACf;QACD,kCAAkC;aAC7B,IAAI,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;YAC/C,aAAa;YACb,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,IAAI,CAAC;SACf;QAED,4DAA4D;QAC5D,kCAAkC;QAClC,MAAM,KAAK,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QAErD,uDAAuD;QACvD,OAAO,KAAK,GAAG,CAAC,EAAE;YAEhB,kDAAkD;YAClD,MAAM,IAAI,GAAG,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;YAE7C,2CAA2C;YAC3C,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;YAEjC,4BAA4B;YAC5B,KAAK,IAAI,CAAC,CAAC;SACZ;QAED,mCAAmC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}