'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('departments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true
      },
      parent_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'departments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '父部门ID，用于邻接列表'
      },
      path: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: '部门路径，格式如: /1/3/5/ 用于物化路径快速查询'
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '部门层级，1为顶级部门'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '同级部门排序'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      manager_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '部门负责人ID，关联到员工表'
      },
      contact_phone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      contact_email: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('departments', ['code']);
    await queryInterface.addIndex('departments', ['parent_id']);
    await queryInterface.addIndex('departments', ['path']);
    await queryInterface.addIndex('departments', ['level']);
    await queryInterface.addIndex('departments', ['is_active']);
    await queryInterface.addIndex('departments', ['manager_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('departments');
  }
}; 