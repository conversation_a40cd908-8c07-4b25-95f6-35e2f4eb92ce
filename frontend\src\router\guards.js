import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

/**
 * 认证守卫 - 检查用户是否已登录
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 导航函数
 */
export const authGuard = async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 如果用户已认证，直接通过
  if (authStore.isAuthenticated) {
    return next()
  }
  
  // 如果有访问令牌但没有用户信息，尝试获取用户信息
  if (authStore.accessToken && !authStore.user) {
    try {
      await authStore.fetchUserProfile()
      return next()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      authStore.clearAuth()
    }
  }
  
  // 未认证，重定向到登录页
  ElMessage.warning('请先登录')
  next({
    name: 'Login',
    query: { redirect: to.fullPath }
  })
}

/**
 * 客人守卫 - 已登录用户不能访问的页面（如登录、注册页）
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 导航函数
 */
export const guestGuard = (to, from, next) => {
  const authStore = useAuthStore()
  
  // 如果用户已认证，重定向到首页
  if (authStore.isAuthenticated) {
    return next({ name: 'Dashboard' })
  }
  
  next()
}

/**
 * 权限守卫工厂 - 检查用户权限
 * @param {string|Array} requiredPermissions - 所需权限
 * @returns {Function} 守卫函数
 */
export const permissionGuard = (requiredPermissions) => {
  return (to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查是否已认证
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      return next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
    }
    
    // 检查权限
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    
    for (const permission of permissions) {
      if (!authStore.hasPermission(permission)) {
        ElMessage.error('权限不足，无法访问该页面')
        return next({ name: 'Forbidden' })
      }
    }
    
    next()
  }
}

/**
 * 角色守卫工厂 - 检查用户角色
 * @param {string|Array} requiredRoles - 所需角色
 * @returns {Function} 守卫函数
 */
export const roleGuard = (requiredRoles) => {
  return (to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查是否已认证
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      return next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
    }
    
    // 检查角色
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
    
    for (const role of roles) {
      if (!authStore.hasRole(role)) {
        ElMessage.error('角色权限不足，无法访问该页面')
        return next({ name: 'Forbidden' })
      }
    }
    
    next()
  }
}

/**
 * 管理员守卫 - 只有管理员可以访问
 */
export const adminGuard = roleGuard(['ADMIN', 'SUPER_ADMIN'])

/**
 * 超级管理员守卫 - 只有超级管理员可以访问
 */
export const superAdminGuard = roleGuard('SUPER_ADMIN')

/**
 * 设置全局路由守卫
 * @param {Object} router - Vue Router实例
 */
export const setupRouterGuards = (router) => {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 显示进度条
    if (window.NProgress) {
      window.NProgress.start()
    }
    
    // 初始化认证状态（仅在应用启动时）
    const authStore = useAuthStore()
    if (!authStore.user && authStore.accessToken) {
      try {
        await authStore.initAuth()
      } catch (error) {
        console.error('初始化认证状态失败:', error)
      }
    }
    
    next()
  })
  
  // 全局解析守卫
  router.beforeResolve((to, from, next) => {
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to, from) => {
    // 隐藏进度条
    if (window.NProgress) {
      window.NProgress.done()
    }
    
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - 智能办公系统`
    } else {
      document.title = '智能办公系统'
    }
  })
}

export default {
  authGuard,
  guestGuard,
  permissionGuard,
  roleGuard,
  adminGuard,
  superAdminGuard,
  setupRouterGuards
} 