const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

async function testAPI() {
  try {
    console.log('🔍 测试登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      credential: 'admin',
      password: '123456'
    });
    
    const token = loginResponse.data.data.accessToken;
    console.log('✅ 登录成功，获取token');
    
    console.log('\n🔍 测试员工API...');
    const employeeResponse = await axios.get(`${BASE_URL}/employees?page=1&pageSize=10`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('📊 员工API响应结构:');
    console.log('- success:', employeeResponse.data.success);
    console.log('- message:', employeeResponse.data.message);
    console.log('- data类型:', typeof employeeResponse.data.data);
    console.log('- data是数组吗:', Array.isArray(employeeResponse.data.data));
    
    if (employeeResponse.data.data) {
      console.log('- data结构:', Object.keys(employeeResponse.data.data));
      console.log('- employees字段:', employeeResponse.data.data.employees ? '存在' : '不存在');
      console.log('- employees是数组吗:', Array.isArray(employeeResponse.data.data.employees));
      
      if (employeeResponse.data.data.employees) {
        console.log('- 员工数量:', employeeResponse.data.data.employees.length);
        console.log('- 总数:', employeeResponse.data.data.total);
      }
    }
    
    console.log('\n📋 完整响应数据:');
    console.log(JSON.stringify(employeeResponse.data, null, 2));
    
  } catch (error) {
    console.log('❌ 测试失败:');
    console.log('状态码:', error.response?.status);
    console.log('错误信息:', error.response?.data);
    console.log('完整错误:', error.message);
  }
}

testAPI(); 