<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧背景 -->
      <div class="login-banner">
        <div class="banner-content">
          <h1>智能办公系统</h1>
          <p>高效、智能、协作的企业办公解决方案</p>
          <div class="feature-list">
            <div class="feature-item">
              <el-icon><User /></el-icon>
              <span>员工管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Clock /></el-icon>
              <span>考勤管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Calendar /></el-icon>
              <span>会议管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Document /></el-icon>
              <span>工作流审批</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="form-header">
            <h2>欢迎登录</h2>
            <p>请输入您的账户信息</p>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            size="large"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="credential">
              <el-input
                v-model="loginForm.credential"
                placeholder="请输入用户名或邮箱"
                prefix-icon="User"
                clearable
                :disabled="loading"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
                :disabled="loading"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="rememberMe" :disabled="loading">
                  记住我
                </el-checkbox>
                <el-link type="primary" @click="showForgotPassword = true">
                  忘记密码？
                </el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                class="login-button"
                @click="handleLogin"
              >
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>

            <div class="register-link">
              <span>还没有账户？</span>
              <el-link type="primary" @click="$router.push('/register')">
                立即注册
              </el-link>
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="showForgotPassword"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-width="80px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showForgotPassword = false">取消</el-button>
          <el-button
            type="primary"
            :loading="forgotPasswordLoading"
            @click="handleForgotPassword"
          >
            发送重置邮件
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, Clock, Calendar, Document, Message } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const loginFormRef = ref()
const forgotPasswordFormRef = ref()
const loading = ref(false)
const rememberMe = ref(false)
const showForgotPassword = ref(false)
const forgotPasswordLoading = ref(false)

// 登录表单
const loginForm = reactive({
  credential: '',
  password: ''
})

// 忘记密码表单
const forgotPasswordForm = reactive({
  email: ''
})

// 登录表单验证规则
const loginRules = reactive({
  credential: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, message: '用户名至少3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ]
})

// 忘记密码验证规则
const forgotPasswordRules = reactive({
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
})

// 处理登录
const handleLogin = async () => {
  try {
    // 表单验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 调用登录API
    await authStore.login(loginForm.credential, loginForm.password)

    ElMessage.success('登录成功')

    // 跳转到目标页面或首页
    const redirectPath = route.query.redirect || '/dashboard'
    router.push(redirectPath)

  } catch (error) {
    ElMessage.error(error || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 处理忘记密码
const handleForgotPassword = async () => {
  try {
    // 表单验证
    const valid = await forgotPasswordFormRef.value.validate()
    if (!valid) return

    forgotPasswordLoading.value = true

    // TODO: 调用忘记密码API
    // await authStore.requestPasswordReset(forgotPasswordForm.email)

    ElMessage.success('重置密码邮件已发送，请查收')
    showForgotPassword.value = false
    forgotPasswordForm.email = ''

  } catch (error) {
    ElMessage.error(error || '发送重置邮件失败')
  } finally {
    forgotPasswordLoading.value = false
  }
}

// 组件挂载时检查登录状态
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/dashboard'
    router.push(redirectPath)
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-wrapper {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 40px;
}

.banner-content {
  text-align: center;
  max-width: 400px;
}

.banner-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.banner-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.feature-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.feature-item .el-icon {
  font-size: 1.2rem;
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-header p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 6px;
}

.register-link {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.register-link .el-link {
  margin-left: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    min-height: auto;
  }
  
  .login-banner {
    padding: 30px 20px;
  }
  
  .banner-content h1 {
    font-size: 2rem;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .login-form-container {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .banner-content h1 {
    font-size: 1.8rem;
  }
  
  .banner-content p {
    font-size: 1rem;
  }
}
</style> 