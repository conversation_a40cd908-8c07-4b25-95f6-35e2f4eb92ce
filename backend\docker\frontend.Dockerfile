# 前端生产环境Dockerfile
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装依赖阶段
FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 构建阶段
FROM base AS build
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 生产运行阶段 - 使用Nginx提供静态文件服务
FROM nginx:alpine AS production

# 复制自定义nginx配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 复制构建产物到nginx目录
COPY --from=build /app/dist /usr/share/nginx/html

# 创建非root用户
RUN addgroup -g 1001 -S nginx-user
RUN adduser -S nginx-user -u 1001

# 设置正确的权限
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html
RUN chown -R nginx-user:nginx-user /var/cache/nginx
RUN chown -R nginx-user:nginx-user /var/log/nginx
RUN chown -R nginx-user:nginx-user /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R nginx-user:nginx-user /var/run/nginx.pid

# 切换到非root用户
USER nginx-user

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"] 