<template>
  <div class="employee-list-container">
    <el-card class="header-card">
      <div class="header-content">
        <div class="header-left">
          <h2>员工管理</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>人事管理</el-breadcrumb-item>
            <el-breadcrumb-item>员工管理</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-actions">
          <el-button 
            type="primary" 
            icon="Plus"
            @click="showCreateDialog = true"
            v-permission="'employee:create'">
            新增员工
          </el-button>
          <el-button 
            icon="Download"
            @click="handleExport"
            v-permission="'employee:read'">
            导出数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索筛选栏 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、员工编号、邮箱"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="部门">
            <el-tree-select
              v-model="searchForm.departmentId"
              :data="departmentTreeData"
              placeholder="选择部门"
              clearable
              filterable
              style="width: 200px"
              node-key="id"
              :props="{ label: 'name', children: 'children' }"
              @change="handleSearch" />
          </el-form-item>

          <el-form-item label="职位">
            <el-select
              v-model="searchForm.positionId"
              placeholder="选择职位"
              clearable
              style="width: 150px"
              @change="handleSearch">
              <el-option
                v-for="position in positions"
                :key="position.id"
                :label="position.name"
                :value="position.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="选择状态"
              clearable
              style="width: 120px"
              @change="handleSearch">
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 员工列表 -->
    <el-card class="table-card">
      <!-- 批量操作工具栏 -->
      <div class="batch-actions" v-if="selectedEmployees.length > 0">
        <span class="batch-info">已选择 {{ selectedEmployees.length }} 名员工</span>
        <el-button-group>
          <el-button 
            type="success" 
            size="small"
            @click="handleBatchStatus('active')"
            v-permission="'employee:update'">
            设为在职
          </el-button>
          <el-button 
            type="warning" 
            size="small"
            @click="handleBatchStatus('inactive')"
            v-permission="'employee:update'">
            设为停职
          </el-button>
          <el-button 
            type="danger" 
            size="small"
            @click="handleBatchStatus('resigned')"
            v-permission="'employee:update'">
            设为离职
          </el-button>
        </el-button-group>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="employees"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        stripe
        border>
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="员工编号" prop="employee_no" width="120" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewDetail(row)">
              {{ row.employee_no }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="头像" width="60">
          <template #default="{ row }">
            <el-avatar 
              :size="40" 
              :src="row.user?.avatar"
              :alt="row.user?.real_name">
              {{ row.user?.real_name?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column label="姓名" prop="user.real_name" width="100" />
        
        <el-table-column label="用户名" prop="user.username" width="120" />
        
        <el-table-column label="邮箱" prop="user.email" width="180" show-overflow-tooltip />
        
        <el-table-column label="手机" prop="user.phone" width="120" />
        
        <el-table-column label="部门" width="150">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.department?.name }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="职位" width="120">
          <template #default="{ row }">
            <span>{{ row.position?.name }}</span>
            <el-tag size="small" class="level-tag">
              L{{ row.position?.level }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="入职日期" prop="hire_date" width="110" />
        
        <el-table-column label="直属上级" width="120">
          <template #default="{ row }">
            <span v-if="row.manager">{{ row.manager.user?.real_name }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)" 
              size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                size="small" 
                icon="View"
                @click="handleViewDetail(row)"
                v-permission="'employee:read'">
                详情
              </el-button>
              <el-button 
                size="small" 
                icon="Edit"
                @click="handleEdit(row)"
                v-permission="'employee:update'">
                编辑
              </el-button>
              <el-dropdown>
                <el-button size="small" icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      icon="Switch"
                      @click="handleTransfer(row)"
                      v-permission="'employee:update'">
                      转移
                    </el-dropdown-item>
                    <el-dropdown-item 
                      icon="Delete"
                      @click="handleDelete(row)"
                      v-permission="'employee:delete'"
                      divided>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 新增/编辑员工对话框 -->
    <EmployeeForm
      v-model:visible="showCreateDialog"
      :employee-data="currentEmployee"
      :mode="formMode"
      @success="handleFormSuccess" />

    <!-- 员工详情对话框 -->
    <EmployeeDetail
      v-model:visible="showDetailDialog"
      :employee-id="selectedEmployeeId" />

    <!-- 转移员工对话框 -->
    <EmployeeTransfer
      v-model:visible="showTransferDialog"
      :employee-data="transferEmployee"
      @success="handleTransferSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useEmployeeStore } from '@/stores/employee';
import { ElMessage } from 'element-plus';
import { Search, Plus, Download, View, Edit, More, Switch, Delete, Refresh } from '@element-plus/icons-vue';
import EmployeeForm from './components/EmployeeForm.vue';
import EmployeeDetail from './components/EmployeeDetail.vue';
import EmployeeTransfer from './components/EmployeeTransfer.vue';

// Store
const employeeStore = useEmployeeStore();

// 响应式数据
const loading = computed(() => employeeStore.loading);
const employees = computed(() => {
  const storeEmployees = employeeStore.employees;
  // 确保始终返回数组，避免 data.includes 错误
  return Array.isArray(storeEmployees) ? storeEmployees : [];
});
const pagination = computed(() => employeeStore.pagination);
const statusOptions = computed(() => employeeStore.statusOptions);
const departments = computed(() => {
  const storeDepartments = employeeStore.departments;
  return Array.isArray(storeDepartments) ? storeDepartments : [];
});
const positions = computed(() => {
  const storePositions = employeeStore.positions;
  return Array.isArray(storePositions) ? storePositions : [];
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  departmentId: null,
  positionId: null,
  status: null
});

// 选中的员工
const selectedEmployees = ref([]);

// 对话框控制
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const showTransferDialog = ref(false);

// 表单状态
const formMode = ref('create');
const currentEmployee = ref(null);
const selectedEmployeeId = ref(null);
const transferEmployee = ref(null);

// 部门树形数据
const departmentTreeData = computed(() => {
  const transformTree = (nodes) => {
    if (!Array.isArray(nodes)) {
      return [];
    }
    return nodes.map(node => ({
      ...node,
      children: node.children ? transformTree(node.children) : []
    }));
  };
  return transformTree(departments.value || []);
});

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'warning',
    resigned: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    active: '在职',
    inactive: '停职',
    resigned: '离职'
  };
  return statusMap[status] || '未知';
};

// 处理搜索
const handleSearch = () => {
  employeeStore.setFilters(searchForm);
  employeeStore.setPagination({ page: 1 });
  fetchEmployees();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    departmentId: null,
    positionId: null,
    status: null
  });
  employeeStore.resetFilters();
  employeeStore.setPagination({ page: 1 });
  fetchEmployees();
};

// 获取员工列表
const fetchEmployees = async () => {
  try {
    await employeeStore.fetchEmployees();
  } catch (error) {
    console.error('获取员工列表失败:', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  employeeStore.setPagination({ pageSize: size, page: 1 });
  fetchEmployees();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  employeeStore.setPagination({ page });
  fetchEmployees();
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedEmployees.value = selection;
};

// 查看员工详情
const handleViewDetail = (employee) => {
  selectedEmployeeId.value = employee.id;
  showDetailDialog.value = true;
};

// 编辑员工
const handleEdit = (employee) => {
  currentEmployee.value = { ...employee };
  formMode.value = 'edit';
  showCreateDialog.value = true;
};

// 删除员工
const handleDelete = async (employee) => {
  try {
    console.log('删除员工:', employee);
    console.log('员工ID:', employee.id);
    console.log('当前员工列表:', employees.value);
    
    await employeeStore.deleteEmployee(employee.id);
  } catch (error) {
    console.error('删除员工失败:', error);
  }
};

// 转移员工
const handleTransfer = (employee) => {
  transferEmployee.value = employee;
  showTransferDialog.value = true;
};

// 批量更新状态
const handleBatchStatus = async (status) => {
  const employeeIds = selectedEmployees.value.map(emp => emp.id);
  try {
    await employeeStore.batchUpdateStatus(employeeIds, status);
    selectedEmployees.value = [];
  } catch (error) {
    console.error('批量更新状态失败:', error);
  }
};

// 导出数据
const handleExport = async () => {
  try {
    const result = await employeeStore.exportEmployees();
    // 这里可以处理文件下载逻辑
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 表单成功回调
const handleFormSuccess = async (employeeData) => {
  try {
    if (formMode.value === 'create') {
      // 创建新员工
      await employeeStore.createEmployee(employeeData);
    } else if (formMode.value === 'edit' && currentEmployee.value) {
      // 更新员工信息
      await employeeStore.updateEmployee(currentEmployee.value.id, employeeData);
      // 编辑后重新获取列表
      await fetchEmployees();
    }
    
    showCreateDialog.value = false;
    currentEmployee.value = null;
    formMode.value = 'create';
    
  } catch (error) {
    console.error('操作失败:', error);
    // 即使操作失败，也要刷新列表以同步最新状态
    await fetchEmployees();
  }
};

// 转移成功回调
const handleTransferSuccess = () => {
  showTransferDialog.value = false;
  transferEmployee.value = null;
  fetchEmployees();
};

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      employeeStore.fetchDepartments(),
      employeeStore.fetchPositions(),
      fetchEmployees()
    ]);
  } catch (error) {
    console.error('初始化失败:', error);
  }
});
</script>

<style scoped>
.employee-list-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-content {
  padding: 10px 0;
}

.search-form {
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.batch-info {
  color: #409eff;
  font-weight: 500;
}

.level-tag {
  margin-left: 4px;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-button-group) {
  .el-button {
    padding: 5px 8px;
  }
}
</style> 