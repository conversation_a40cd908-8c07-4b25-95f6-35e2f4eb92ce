const crypto = require('crypto');

// JWT配置
const JWT_CONFIG = {
  // 访问令牌配置
  ACCESS_TOKEN: {
    secret: process.env.JWT_ACCESS_SECRET || crypto.randomBytes(64).toString('hex'),
    expiresIn: process.env.JWT_ACCESS_EXPIRES || '15m', // 15分钟
    algorithm: 'HS256',
    issuer: 'smart-office-system',
    audience: 'smart-office-users'
  },
  
  // 刷新令牌配置
  REFRESH_TOKEN: {
    secret: process.env.JWT_REFRESH_SECRET || crypto.randomBytes(64).toString('hex'),
    expiresIn: process.env.JWT_REFRESH_EXPIRES || '7d', // 7天
    algorithm: 'HS256',
    issuer: 'smart-office-system',
    audience: 'smart-office-users'
  },

  // 令牌类型
  TOKEN_TYPES: {
    ACCESS: 'access',
    REFRESH: 'refresh',
    RESET_PASSWORD: 'reset_password',
    EMAIL_VERIFICATION: 'email_verification'
  },

  // Cookie配置
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
  }
};

// 验证配置
if (process.env.NODE_ENV === 'production') {
  if (!process.env.JWT_ACCESS_SECRET || !process.env.JWT_REFRESH_SECRET) {
    console.warn('⚠️  警告: 生产环境应该设置JWT_ACCESS_SECRET和JWT_REFRESH_SECRET环境变量');
  }
}

module.exports = JWT_CONFIG; 