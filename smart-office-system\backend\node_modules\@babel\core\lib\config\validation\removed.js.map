{"version": 3, "names": ["auxiliaryComment", "message", "blacklist", "breakConfig", "experimental", "externalHelpers", "extra", "jsxPragma", "loose", "metadataUsedHelpers", "modules", "nonStandard", "optional", "sourceMapName", "stage", "whitelist", "resolveModuleSource", "version", "metadata", "sourceMapTarget"], "sources": ["../../../src/config/validation/removed.ts"], "sourcesContent": ["export default {\n  auxiliaryComment: {\n    message: \"Use `auxiliaryCommentBefore` or `auxiliaryCommentAfter`\",\n  },\n  blacklist: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  breakConfig: {\n    message: \"This is not a necessary option in Babel 6\",\n  },\n  experimental: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  externalHelpers: {\n    message:\n      \"Use the `external-helpers` plugin instead. \" +\n      \"Check out http://babeljs.io/docs/plugins/external-helpers/\",\n  },\n  extra: {\n    message: \"\",\n  },\n  jsxPragma: {\n    message:\n      \"use the `pragma` option in the `react-jsx` plugin. \" +\n      \"Check out http://babeljs.io/docs/plugins/transform-react-jsx/\",\n  },\n  loose: {\n    message:\n      \"Specify the `loose` option for the relevant plugin you are using \" +\n      \"or use a preset that sets the option.\",\n  },\n  metadataUsedHelpers: {\n    message: \"Not required anymore as this is enabled by default\",\n  },\n  modules: {\n    message:\n      \"Use the corresponding module transform plugin in the `plugins` option. \" +\n      \"Check out http://babeljs.io/docs/plugins/#modules\",\n  },\n  nonStandard: {\n    message:\n      \"Use the `react-jsx` and `flow-strip-types` plugins to support JSX and Flow. \" +\n      \"Also check out the react preset http://babeljs.io/docs/plugins/preset-react/\",\n  },\n  optional: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  sourceMapName: {\n    message:\n      \"The `sourceMapName` option has been removed because it makes more sense for the \" +\n      \"tooling that calls Babel to assign `map.file` themselves.\",\n  },\n  stage: {\n    message:\n      \"Check out the corresponding stage-x presets http://babeljs.io/docs/plugins/#presets\",\n  },\n  whitelist: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n\n  resolveModuleSource: {\n    version: 6,\n    message: \"Use `babel-plugin-module-resolver@3`'s 'resolvePath' options\",\n  },\n  metadata: {\n    version: 6,\n    message:\n      \"Generated plugin metadata is always included in the output result\",\n  },\n  sourceMapTarget: {\n    version: 6,\n    message:\n      \"The `sourceMapTarget` option has been removed because it makes more sense for the tooling \" +\n      \"that calls Babel to assign `map.file` themselves.\",\n  },\n} as { [name: string]: { version?: number; message: string } };\n"], "mappings": ";;;;;;iCAAe;EACbA,gBAAgB,EAAE;IAChBC,OAAO,EAAE;EACX,CAAC;EACDC,SAAS,EAAE;IACTD,OAAO,EAAE;EACX,CAAC;EACDE,WAAW,EAAE;IACXF,OAAO,EAAE;EACX,CAAC;EACDG,YAAY,EAAE;IACZH,OAAO,EAAE;EACX,CAAC;EACDI,eAAe,EAAE;IACfJ,OAAO,EACL,6CAA6C,GAC7C;EACJ,CAAC;EACDK,KAAK,EAAE;IACLL,OAAO,EAAE;EACX,CAAC;EACDM,SAAS,EAAE;IACTN,OAAO,EACL,qDAAqD,GACrD;EACJ,CAAC;EACDO,KAAK,EAAE;IACLP,OAAO,EACL,mEAAmE,GACnE;EACJ,CAAC;EACDQ,mBAAmB,EAAE;IACnBR,OAAO,EAAE;EACX,CAAC;EACDS,OAAO,EAAE;IACPT,OAAO,EACL,yEAAyE,GACzE;EACJ,CAAC;EACDU,WAAW,EAAE;IACXV,OAAO,EACL,8EAA8E,GAC9E;EACJ,CAAC;EACDW,QAAQ,EAAE;IACRX,OAAO,EAAE;EACX,CAAC;EACDY,aAAa,EAAE;IACbZ,OAAO,EACL,kFAAkF,GAClF;EACJ,CAAC;EACDa,KAAK,EAAE;IACLb,OAAO,EACL;EACJ,CAAC;EACDc,SAAS,EAAE;IACTd,OAAO,EAAE;EACX,CAAC;EAEDe,mBAAmB,EAAE;IACnBC,OAAO,EAAE,CAAC;IACVhB,OAAO,EAAE;EACX,CAAC;EACDiB,QAAQ,EAAE;IACRD,OAAO,EAAE,CAAC;IACVhB,OAAO,EACL;EACJ,CAAC;EACDkB,eAAe,EAAE;IACfF,OAAO,EAAE,CAAC;IACVhB,OAAO,EACL,4FAA4F,GAC5F;EACJ;AACF,CAAC;AAAA", "ignoreList": []}