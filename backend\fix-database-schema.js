const mysql = require('mysql2/promise');

// 数据库修复脚本 - 架构师级别的解决方案
async function fixDatabaseSchema() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'smart_office_system'
  });

  console.log('🔧 开始修复数据库结构...');

  try {
    // 1. 修复会议室表 - 添加缺失的字段
    console.log('📋 修复会议室表结构...');
    
    // 检查 status 字段是否存在
    const [statusColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'smart_office_system' 
      AND TABLE_NAME = 'meeting_rooms' 
      AND COLUMN_NAME = 'status'
    `);

    if (statusColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE meeting_rooms 
        ADD COLUMN status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available'
      `);
      console.log('✅ 会议室表添加 status 字段');
    } else {
      console.log('✅ 会议室表 status 字段已存在');
    }

    // 2. 修复会议表 - 添加缺失的字段
    console.log('📋 修复会议表结构...');
    
    const [agendaColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'smart_office_system' 
      AND TABLE_NAME = 'meetings' 
      AND COLUMN_NAME = 'agenda'
    `);

    if (agendaColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE meetings 
        ADD COLUMN agenda TEXT NULL
      `);
      console.log('✅ 会议表添加 agenda 字段');
    } else {
      console.log('✅ 会议表 agenda 字段已存在');
    }

    // 3. 检查会议参与者表结构
    console.log('📋 检查会议参与者表结构...');
    
    const [participantColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'smart_office_system' 
      AND TABLE_NAME = 'meeting_participants'
    `);

    console.log('会议参与者表字段:', participantColumns.map(col => col.COLUMN_NAME));

    // 4. 修复办公用品表 status 字段长度
    console.log('📋 修复办公用品表 status 字段...');
    
    await connection.execute(`
      ALTER TABLE office_supplies 
      MODIFY COLUMN status ENUM('available', 'out_of_stock', 'discontinued') DEFAULT 'available'
    `);
    console.log('✅ 办公用品表 status 字段修复完成');

    // 5. 检查所有表结构
    console.log('📋 检查所有关键表结构...');
    
    const tables = ['users', 'employees', 'departments', 'positions', 'meetings', 'meeting_rooms', 'meeting_participants', 'office_supplies', 'schedules'];
    
    for (const table of tables) {
      const [columns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'smart_office_system' 
        AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `, [table]);
      
      console.log(`\n📊 ${table} 表结构:`);
      columns.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
      });
    }

    console.log('\n🎉 数据库结构修复完成！');

  } catch (error) {
    console.error('❌ 数据库修复失败:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// 执行修复
if (require.main === module) {
  fixDatabaseSchema().catch(console.error);
}

module.exports = { fixDatabaseSchema };
